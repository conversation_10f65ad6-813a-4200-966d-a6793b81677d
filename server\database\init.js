const { pool } = require('../config/database');
const fs = require('fs');
const path = require('path');

const initializeDatabase = async () => {
  try {
    console.log('Starting database initialization...');

    // Read and execute the full schema
    const schemaPath = path.join(__dirname, 'full_schema.sql');
    if (fs.existsSync(schemaPath)) {
      console.log('Executing full schema...');
      const schema = fs.readFileSync(schemaPath, 'utf8');
      
      // Execute the full schema as a single query to support multi-line statements
      try {
        await pool.query(schema);
        console.log('Full schema executed successfully');
      } catch (error) {
        console.error('Schema execution error:', error.message);
      }
    } else {
      console.log('Full schema not found, creating basic organizations table...');
      
      // Create basic organizations table if full schema doesn't exist
      try {
        await pool.query(`
          CREATE TABLE IF NOT EXISTS organizations (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            phone VARCHAR(50),
            address_line1 VARCHAR(255),
            contact_person VARCHAR(255),
            contact_email VARCHAR(255),
            contact_phone VARCHAR(50),
            subscription_plan VARCHAR(50) DEFAULT 'basic',
            billing_address_line1 VARCHAR(255),
            payment_method VARCHAR(50) DEFAULT 'invoice',
            max_users INTEGER DEFAULT 100,
            max_locations INTEGER DEFAULT 10,
            status VARCHAR(20) DEFAULT 'active',
            per_user_fee DECIMAL(10,2) DEFAULT 5.00,
            branding JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );
        `);
        
        // Create indexes
        try {
          await pool.query(`
            CREATE INDEX IF NOT EXISTS idx_organizations_name ON organizations(name);
            CREATE INDEX IF NOT EXISTS idx_organizations_email ON organizations(email);
            CREATE INDEX IF NOT EXISTS idx_organizations_status ON organizations(status);
          `);
        } catch (error) {
          console.log('Indexes may already exist:', error.message);
        }
        
        console.log('Basic organizations table created successfully');
      } catch (error) {
        console.log('Organizations table may already exist:', error.message);
      }
    }

    // Check if we need to insert sample data
    try {
      const orgCount = await pool.query('SELECT COUNT(*) FROM organizations');
      if (parseInt(orgCount.rows[0].count) === 0) {
        console.log('Inserting sample organizations...');
        
        // Insert sample organizations
        await pool.query(`
          INSERT INTO organizations (id, name, email, contact_person, subscription_plan, max_users, max_locations, status) VALUES
          ('00000000-0000-0000-0000-000000000001', 'Acme Security', '<EMAIL>', 'Alice Manager', 'premium', 100, 10, 'active'),
          ('00000000-0000-0000-0000-000000000002', 'City Guard', '<EMAIL>', 'Bob Supervisor', 'standard', 50, 5, 'active'),
          ('00000000-0000-0000-0000-000000000003', 'Metro Protection', '<EMAIL>', 'Carol Admin', 'enterprise', 200, 20, 'active');
        `);
        
        console.log('Sample organizations inserted successfully');
      } else {
        console.log(`Database already has ${orgCount.rows[0].count} organizations`);
      }
    } catch (error) {
      console.log('Error checking/inserting sample data:', error.message);
    }

    // Create invoices tables
    try {
      await pool.query(`
        CREATE TABLE IF NOT EXISTS invoices (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
          invoice_number VARCHAR(50) UNIQUE NOT NULL,
          invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
          due_date DATE NOT NULL,
          billing_period_start DATE NOT NULL,
          billing_period_end DATE NOT NULL,
          subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          tax_rate DECIMAL(5,2) DEFAULT 0.00,
          tax_amount DECIMAL(10,2) DEFAULT 0.00,
          total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('draft', 'pending', 'paid', 'overdue', 'cancelled')),
          payment_date DATE,
          payment_method VARCHAR(50),
          notes TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
      `);

      await pool.query(`
        CREATE TABLE IF NOT EXISTS invoice_line_items (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
          location_id UUID REFERENCES locations(id),
          location_name VARCHAR(255) NOT NULL,
          user_count INTEGER NOT NULL DEFAULT 0,
          unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          line_total DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
      `);

      console.log('Invoice tables created successfully');
    } catch (error) {
      console.log('Error creating invoice tables:', error.message);
    }

    // Create subscription plans table
    try {
      await pool.query(`
        CREATE TABLE IF NOT EXISTS subscription_plans (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name VARCHAR(100) NOT NULL UNIQUE,
          display_name VARCHAR(100) NOT NULL,
          description TEXT,
          monthly_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          annual_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          per_user_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          max_users INTEGER NOT NULL DEFAULT 0,
          max_locations INTEGER NOT NULL DEFAULT 0,
          max_monthly_tracked_hours INTEGER DEFAULT NULL,
          max_storage_gb INTEGER DEFAULT NULL,
          max_api_calls_per_hour INTEGER DEFAULT NULL,
          features JSONB DEFAULT '[]',
          is_active BOOLEAN DEFAULT true,
          sort_order INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Insert default subscription plans
      await pool.query(`
        INSERT INTO subscription_plans (
          name, display_name, description, monthly_price, annual_price, per_user_fee,
          max_users, max_locations, max_monthly_tracked_hours, max_storage_gb,
          max_api_calls_per_hour, features, sort_order
        ) VALUES
        (
          'basic',
          'Basic Plan',
          'Perfect for small teams getting started with employee tracking',
          29.99,
          299.99,
          5.00,
          10,
          3,
          1000,
          5,
          500,
          '["Basic location tracking", "Employee management", "Basic reporting", "Email support"]',
          1
        ),
        (
          'standard',
          'Standard Plan',
          'Ideal for growing businesses with advanced tracking needs',
          79.99,
          799.99,
          4.00,
          50,
          10,
          5000,
          25,
          2000,
          '["Advanced location tracking", "Employee management", "Advanced reporting", "Geofencing", "Real-time alerts", "Priority email support"]',
          2
        ),
        (
          'premium',
          'Premium Plan',
          'Comprehensive solution for larger organizations',
          149.99,
          1499.99,
          3.50,
          200,
          25,
          20000,
          100,
          5000,
          '["Premium location tracking", "Advanced employee management", "Custom reporting", "Advanced geofencing", "Real-time alerts", "API access", "Phone support", "Custom integrations"]',
          3
        ),
        (
          'enterprise',
          'Enterprise Plan',
          'Unlimited solution for large enterprises with custom requirements',
          299.99,
          2999.99,
          3.00,
          999999,
          999999,
          NULL,
          NULL,
          NULL,
          '["Unlimited location tracking", "Advanced employee management", "Custom reporting", "Advanced geofencing", "Real-time alerts", "Full API access", "24/7 phone support", "Custom integrations", "Dedicated account manager", "Custom features"]',
          4
        ) ON CONFLICT (name) DO NOTHING;
      `);

      console.log('Subscription plans table created successfully');
    } catch (error) {
      console.log('Error creating subscription plans table:', error.message);
    }

    console.log('Database initialization completed successfully');
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
};

module.exports = { initializeDatabase }; 