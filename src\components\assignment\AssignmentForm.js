import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import BrandedButton from '../BrandedButton';
import { X, Calendar, Clock, Repeat, User, MapPin } from 'lucide-react';

const AssignmentForm = ({ assignment, employees, locations, tenantId, onSubmit, onCancel, loading = false }) => {
  const isEditing = !!assignment;
  const [recurringType, setRecurringType] = useState(assignment?.recurring?.type || 'none');
  const [selectedDays, setSelectedDays] = useState(assignment?.recurring?.days || []);

  // Helper function to convert assignment data to form format
  const getFormDefaults = () => {
    if (assignment) {
      const startDateTime = new Date(assignment.startTime);
      const endDateTime = new Date(assignment.endTime);

      return {
        employeeId: assignment.employeeId || '',
        locationId: assignment.locationId || '',
        startDate: startDateTime.toISOString().split('T')[0],
        startTime: startDateTime.toTimeString().slice(0, 5), // HH:MM format
        endTime: endDateTime.toTimeString().slice(0, 5), // HH:MM format
        status: assignment.status || 'active',
        notes: assignment.notes || '',
        recurring: assignment.recurring || { type: 'none', days: [] }
      };
    }

    return {
      employeeId: '',
      locationId: '',
      startTime: '',
      endTime: '',
      startDate: new Date().toISOString().split('T')[0],
      status: 'active',
      notes: '',
      recurring: {
        type: 'none',
        days: []
      }
    };
  };

  const { register, handleSubmit, formState: { errors, isSubmitting }, watch, setValue } = useForm({
    defaultValues: getFormDefaults()
  });

  const handleFormSubmit = async (data) => {
    try {
      // Combine date and time
      const startDateTime = new Date(`${data.startDate}T${data.startTime}`);
      const endDateTime = new Date(`${data.startDate}T${data.endTime}`);
      
      const assignmentData = {
        ...data,
        startTime: startDateTime.toISOString(),
        endTime: endDateTime.toISOString(),
        recurring: {
          type: recurringType,
          days: selectedDays
        }
      };
      
      onSubmit(assignmentData);
    } catch (error) {
      console.error('Error saving assignment:', error);
    }
  };

  const handleDayToggle = (day) => {
    setSelectedDays(prev => 
      prev.includes(day) 
        ? prev.filter(d => d !== day)
        : [...prev, day]
    );
  };

  const dayOptions = [
    { value: 'monday', label: 'Monday' },
    { value: 'tuesday', label: 'Tuesday' },
    { value: 'wednesday', label: 'Wednesday' },
    { value: 'thursday', label: 'Thursday' },
    { value: 'friday', label: 'Friday' },
    { value: 'saturday', label: 'Saturday' },
    { value: 'sunday', label: 'Sunday' }
  ];

  const recurringOptions = [
    { value: 'none', label: 'One-time' },
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' }
  ];

  const statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  const isFormSubmitting = isSubmitting || loading;

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          {isEditing ? 'Edit Assignment' : 'Create New Assignment'}
        </h2>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-gray-600"
          disabled={isFormSubmitting}
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Employee and Location Selection */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <User className="h-5 w-5" />
            Assignment Details
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Employee *
              </label>
              <select
                {...register('employeeId', { required: 'Employee is required' })}
                className="input"
                disabled={isFormSubmitting}
              >
                <option value="">Select employee</option>
                {employees.map(employee => (
                  <option key={employee.id} value={employee.id}>
                    {employee.name}
                  </option>
                ))}
              </select>
              {errors.employeeId && (
                <span className="text-red-600 text-xs">{errors.employeeId.message}</span>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Location *
              </label>
              <select
                {...register('locationId', { required: 'Location is required' })}
                className="input"
                disabled={isFormSubmitting}
              >
                <option value="">Select location</option>
                {locations.map(location => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
              {errors.locationId && (
                <span className="text-red-600 text-xs">{errors.locationId.message}</span>
              )}
            </div>
          </div>
        </div>

        {/* Date and Time Selection */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Schedule
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date *
              </label>
              <input
                type="date"
                {...register('startDate', { required: 'Start date is required' })}
                className="input"
                disabled={isFormSubmitting}
              />
              {errors.startDate && (
                <span className="text-red-600 text-xs">{errors.startDate.message}</span>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                {...register('endDate')}
                className="input"
                disabled={isFormSubmitting}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Time *
              </label>
              <input
                type="time"
                {...register('startTime', { required: 'Start time is required' })}
                className="input"
                disabled={isFormSubmitting}
              />
              {errors.startTime && (
                <span className="text-red-600 text-xs">{errors.startTime.message}</span>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Time *
              </label>
              <input
                type="time"
                {...register('endTime', { required: 'End time is required' })}
                className="input"
                disabled={isFormSubmitting}
              />
              {errors.endTime && (
                <span className="text-red-600 text-xs">{errors.endTime.message}</span>
              )}
            </div>
          </div>
        </div>

        {/* Recurring Schedule */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <Repeat className="h-5 w-5" />
            Recurring Schedule
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Recurrence Type
              </label>
              <select
                value={recurringType}
                onChange={(e) => setRecurringType(e.target.value)}
                className="input"
                disabled={isFormSubmitting}
              >
                {recurringOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Weekly Recurrence Days */}
            {recurringType === 'weekly' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Repeat on Days
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {dayOptions.map(day => (
                    <label key={day.value} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedDays.includes(day.value)}
                        onChange={() => handleDayToggle(day.value)}
                        disabled={isFormSubmitting}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{day.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Status and Notes */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Additional Information
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                {...register('status')}
                className="input"
                disabled={isFormSubmitting}
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                {...register('notes')}
                rows={3}
                className="input"
                placeholder="Enter any additional notes about this assignment"
                disabled={isFormSubmitting}
              />
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50"
            disabled={isFormSubmitting}
          >
            Cancel
          </button>
          <BrandedButton
            type="submit"
            disabled={isFormSubmitting}
          >
            {isFormSubmitting ? 'Saving...' : (isEditing ? 'Update Assignment' : 'Create Assignment')}
          </BrandedButton>
        </div>
      </form>
    </div>
  );
};

export default AssignmentForm; 