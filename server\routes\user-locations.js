const express = require('express');
const { authenticateToken, requireTenantAccess } = require('../middleware/auth');
const pool = require('../config/database');
const router = express.Router({ mergeParams: true });

// Get current locations for all users in organization
router.get('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { userId = 'all', includeHistory = false } = req.query;

    let query = `
      SELECT DISTINCT ON (ul.user_id)
        ul.id,
        ul.user_id,
        ul.location_id,
        ul.assignment_id,
        ul.latitude,
        ul.longitude,
        ul.accuracy,
        ul.altitude,
        ul.speed,
        ul.heading,
        ul.battery_level,
        ul.is_charging,
        ul.activity_type,
        ul.timestamp,
        ul.created_at,
        u.first_name,
        u.last_name,
        u.email,
        u.role,
        u.status as user_status,
        l.name as location_name,
        l.address as location_address,
        a.shift_start,
        a.shift_end,
        a.status as assignment_status
      FROM user_locations ul
      JOIN users u ON ul.user_id = u.id
      LEFT JOIN locations l ON ul.location_id = l.id
      LEFT JOIN assignments a ON ul.assignment_id = a.id
      WHERE u.organization_id = $1
    `;
    
    const params = [organizationId];
    let paramIndex = 2;

    if (userId !== 'all') {
      query += ` AND ul.user_id = $${paramIndex}`;
      params.push(userId);
      paramIndex++;
    }

    if (!includeHistory) {
      query += ` AND ul.timestamp >= NOW() - INTERVAL '1 hour'`;
    }

    query += ` ORDER BY ul.user_id, ul.timestamp DESC`;

    const result = await pool.query(query, params);
    const userLocations = result.rows.map(location => ({
      id: location.id,
      userId: location.user_id,
      locationId: location.location_id,
      assignmentId: location.assignment_id,
      coordinates: {
        lat: parseFloat(location.latitude),
        lng: parseFloat(location.longitude)
      },
      accuracy: location.accuracy ? parseFloat(location.accuracy) : null,
      altitude: location.altitude ? parseFloat(location.altitude) : null,
      speed: location.speed ? parseFloat(location.speed) : null,
      heading: location.heading ? parseFloat(location.heading) : null,
      batteryLevel: location.battery_level,
      isCharging: location.is_charging,
      activityType: location.activity_type,
      timestamp: location.timestamp,
      user: {
        id: location.user_id,
        name: `${location.first_name} ${location.last_name}`,
        email: location.email,
        role: location.role,
        status: location.user_status
      },
      location: location.location_id ? {
        id: location.location_id,
        name: location.location_name,
        address: location.location_address
      } : null,
      assignment: location.assignment_id ? {
        id: location.assignment_id,
        shiftStart: location.shift_start,
        shiftEnd: location.shift_end,
        status: location.assignment_status
      } : null,
      createdAt: location.created_at
    }));

    res.json(userLocations);
  } catch (error) {
    console.error('Error fetching user locations:', error);
    res.status(500).json({ error: 'Failed to fetch user locations' });
  }
});

// Create/update user location
router.post('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const {
      userId,
      locationId,
      assignmentId,
      latitude,
      longitude,
      accuracy,
      altitude,
      speed,
      heading,
      batteryLevel,
      isCharging,
      activityType,
      timestamp = new Date().toISOString()
    } = req.body;

    // Validate required fields
    if (!userId || !latitude || !longitude) {
      return res.status(400).json({ error: 'User ID, latitude, and longitude are required' });
    }

    // Verify user belongs to organization
    const userCheck = await pool.query(
      'SELECT id FROM users WHERE id = $1 AND organization_id = $2',
      [userId, organizationId]
    );

    if (userCheck.rows.length === 0) {
      return res.status(400).json({ error: 'User not found or does not belong to organization' });
    }

    const result = await pool.query(`
      INSERT INTO user_locations (
        user_id, location_id, assignment_id, latitude, longitude,
        accuracy, altitude, speed, heading, battery_level,
        is_charging, activity_type, timestamp
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *
    `, [
      userId, locationId, assignmentId, latitude, longitude,
      accuracy, altitude, speed, heading, batteryLevel,
      isCharging, activityType, timestamp
    ]);

    const userLocation = result.rows[0];

    // Get user details
    const userResult = await pool.query(
      'SELECT first_name, last_name, email, role, status FROM users WHERE id = $1',
      [userId]
    );
    const user = userResult.rows[0];

    // Get location details if provided
    let location = null;
    if (locationId) {
      const locationResult = await pool.query(
        'SELECT id, name, address FROM locations WHERE id = $1',
        [locationId]
      );
      if (locationResult.rows.length > 0) {
        location = locationResult.rows[0];
      }
    }

    res.status(201).json({
      id: userLocation.id,
      userId: userLocation.user_id,
      locationId: userLocation.location_id,
      assignmentId: userLocation.assignment_id,
      coordinates: {
        lat: parseFloat(userLocation.latitude),
        lng: parseFloat(userLocation.longitude)
      },
      accuracy: userLocation.accuracy ? parseFloat(userLocation.accuracy) : null,
      altitude: userLocation.altitude ? parseFloat(userLocation.altitude) : null,
      speed: userLocation.speed ? parseFloat(userLocation.speed) : null,
      heading: userLocation.heading ? parseFloat(userLocation.heading) : null,
      batteryLevel: userLocation.battery_level,
      isCharging: userLocation.is_charging,
      activityType: userLocation.activity_type,
      timestamp: userLocation.timestamp,
      user: {
        id: userId,
        name: `${user.first_name} ${user.last_name}`,
        email: user.email,
        role: user.role,
        status: user.status
      },
      location,
      createdAt: userLocation.created_at
    });
  } catch (error) {
    console.error('Error creating user location:', error);
    res.status(500).json({ error: 'Failed to create user location' });
  }
});

// Get location history for a specific user
router.get('/history/:userId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, userId } = req.params;
    const { 
      startDate, 
      endDate, 
      limit = 100, 
      page = 1 
    } = req.query;

    let query = `
      SELECT 
        ul.id,
        ul.latitude,
        ul.longitude,
        ul.accuracy,
        ul.altitude,
        ul.speed,
        ul.heading,
        ul.battery_level,
        ul.is_charging,
        ul.activity_type,
        ul.timestamp,
        ul.created_at,
        l.name as location_name,
        l.address as location_address
      FROM user_locations ul
      LEFT JOIN locations l ON ul.location_id = l.id
      JOIN users u ON ul.user_id = u.id
      WHERE u.organization_id = $1 AND ul.user_id = $2
    `;
    
    const params = [organizationId, userId];
    let paramIndex = 3;

    if (startDate) {
      query += ` AND ul.timestamp >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      query += ` AND ul.timestamp <= $${paramIndex}`;
      params.push(endDate);
      paramIndex++;
    }

    query += ` ORDER BY ul.timestamp DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, (page - 1) * limit);

    const result = await pool.query(query, params);
    const locationHistory = result.rows.map(location => ({
      id: location.id,
      coordinates: {
        lat: parseFloat(location.latitude),
        lng: parseFloat(location.longitude)
      },
      accuracy: location.accuracy ? parseFloat(location.accuracy) : null,
      altitude: location.altitude ? parseFloat(location.altitude) : null,
      speed: location.speed ? parseFloat(location.speed) : null,
      heading: location.heading ? parseFloat(location.heading) : null,
      batteryLevel: location.battery_level,
      isCharging: location.is_charging,
      activityType: location.activity_type,
      timestamp: location.timestamp,
      location: location.location_name ? {
        name: location.location_name,
        address: location.location_address
      } : null,
      createdAt: location.created_at
    }));

    res.json(locationHistory);
  } catch (error) {
    console.error('Error fetching location history:', error);
    res.status(500).json({ error: 'Failed to fetch location history' });
  }
});

module.exports = router;
