// Utility function to extract error message from various error formats
export const getErrorMessage = (error) => {
  if (typeof error === 'string') return error;
  if (error?.response?.data?.message) return error.response.data.message;
  if (error?.response?.data?.error) return error.response.data.error;
  if (error?.response?.data && typeof error.response.data === 'string') return error.response.data;
  if (error?.message) return error.message;
  if (error?.error) return error.error;
  return 'An error occurred';
};

// Helper function to safely render error messages in React components
export const renderErrorMessage = (error, fallback = 'An error occurred') => {
  const message = getErrorMessage(error);
  return typeof message === 'string' ? message : fallback;
}; 