// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Polyfills for mapbox-gl in Jest environment
import { TextDecoder, TextEncoder } from 'util';

global.TextDecoder = TextDecoder;
global.TextEncoder = TextEncoder;

// Mock window.URL if needed
if (!global.URL) {
  global.URL = URL;
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor(cb) {
    this.cb = cb;
  }
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor(cb) {
    this.cb = cb;
  }
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock WebGL context if needed
const mockGetContext = () => ({
  getExtension: () => null,
  getParameter: () => 4096,
  createShader: () => ({}),
  shaderSource: () => {},
  compileShader: () => {},
  getShaderParameter: () => true,
  createProgram: () => ({}),
  attachShader: () => {},
  linkProgram: () => {},
  getProgramParameter: () => true,
  getShaderInfoLog: () => '',
  getProgramInfoLog: () => '',
  deleteShader: () => {},
  deleteProgram: () => {},
  createBuffer: () => ({}),
  createTexture: () => ({}),
  createFramebuffer: () => ({}),
  createRenderbuffer: () => ({}),
  deleteBuffer: () => {},
  deleteTexture: () => {},
  deleteFramebuffer: () => {},
  deleteRenderbuffer: () => {},
  bindBuffer: () => {},
  bindTexture: () => {},
  bindFramebuffer: () => {},
  bindRenderbuffer: () => {},
  bufferData: () => {},
  texImage2D: () => {},
  texParameteri: () => {},
  framebufferTexture2D: () => {},
  renderbufferStorage: () => {},
  framebufferRenderbuffer: () => {},
  checkFramebufferStatus: () => 36053, // FRAMEBUFFER_COMPLETE
  clear: () => {},
  clearColor: () => {},
  clearDepth: () => {},
  enable: () => {},
  disable: () => {},
  depthFunc: () => {},
  blendFunc: () => {},
  viewport: () => {},
  drawArrays: () => {},
  drawElements: () => {},
  flush: () => {},
  finish: () => {},
});

HTMLCanvasElement.prototype.getContext = jest.fn(mockGetContext);

// Mock fetch if not available
if (!global.fetch) {
  global.fetch = jest.fn(() =>
    Promise.resolve({
      ok: true,
      json: () => Promise.resolve({}),
      text: () => Promise.resolve(''),
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
    })
  );
}
