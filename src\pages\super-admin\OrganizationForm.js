import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  ArrowLeft,
  Save,
  Building2,
  Mail,
  Phone,
  MapPin,
  User,
  CreditCard,
  Loader2,
  Globe,
  Clock,
  FileText,
  Shield,
  Settings,
  DollarSign,
  Calendar,
  Tag,
  AlertCircle,
  Info,
  Briefcase,
  Link,
  Hash
} from 'lucide-react';
import { createOrganization, updateOrganization, fetchOrganizationById } from '../../store/slices/organizationSlice';
import { renderErrorMessage } from '../../utils/errorUtils';

// Suppress ResizeObserver errors for this component
const suppressResizeObserverErrors = () => {
  const originalError = console.error;
  console.error = (...args) => {
    if (typeof args[0] === 'string' && args[0].includes('ResizeObserver')) {
      return;
    }
    originalError.apply(console, args);
  };
};

const OrganizationForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isEditing = Boolean(id);
  
  // Redux state
  const { currentOrganization, isLoading, error } = useSelector(state => state.organization);
  
  const [formData, setFormData] = useState({
    // Basic Organization Information
    name: '',
    legal_name: '',
    business_type: '',
    industry: '',
    description: '',
    website: '',

    // Contact Information
    email: '',
    phone: '',
    fax: '',

    // Address Information
    address_line1: '',
    address_line2: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'United States',

    // Primary Contact Person
    contact_person: '',
    contact_email: '',
    contact_phone: '',
    contact_title: '',

    // Business Registration Details
    tax_id: '',
    registration_number: '',
    registration_date: '',

    // Billing Information
    billing_address_line1: '',
    billing_address_line2: '',
    billing_city: '',
    billing_state: '',
    billing_postal_code: '',
    billing_country: '',
    billing_contact_name: '',
    billing_contact_email: '',
    billing_contact_phone: '',

    // Subscription & Plan Details
    subscription_plan: 'basic',
    subscription_status: 'active',
    subscription_start_date: '',
    subscription_end_date: '',
    billing_cycle: 'monthly',
    payment_method: 'invoice',

    // Service Limits
    max_users: 100,
    max_locations: 10,
    max_monthly_tracked_hours: 5000,
    max_storage_gb: 10,
    max_api_calls_per_hour: 1000,

    // Organization Settings
    timezone: 'America/New_York',
    date_format: 'MM/DD/YYYY',
    time_format: '12h',
    currency: 'USD',
    locale: 'en_US',

    // Compliance & Security
    security_level: 'standard',
    two_factor_required: false,

    // Organization Status
    status: 'active',
    approval_status: 'approved',

    // Metadata
    logo_url: '',
    notes: '',
    internal_notes: '',
    tags: '',

    // Admin credentials
    adminUsername: '',
    adminPassword: '',
    adminFirstName: '',
    adminLastName: ''
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Suppress ResizeObserver errors when component mounts
    suppressResizeObserverErrors();
    
    if (isEditing && id) {
      // Fetch organization data for editing
      dispatch(fetchOrganizationById(id));
    }
  }, [isEditing, id, dispatch]);

  useEffect(() => {
    if (isEditing && currentOrganization) {
      // Populate form with fetched organization data
      setFormData({
        // Basic Organization Information
        name: currentOrganization.name || '',
        legal_name: currentOrganization.legal_name || '',
        business_type: currentOrganization.business_type || '',
        industry: currentOrganization.industry || '',
        description: currentOrganization.description || '',
        website: currentOrganization.website || '',

        // Contact Information
        email: currentOrganization.email || '',
        phone: currentOrganization.phone || '',
        fax: currentOrganization.fax || '',

        // Address Information
        address_line1: currentOrganization.address_line1 || '',
        address_line2: currentOrganization.address_line2 || '',
        city: currentOrganization.city || '',
        state: currentOrganization.state || '',
        postal_code: currentOrganization.postal_code || '',
        country: currentOrganization.country || 'United States',

        // Primary Contact Person
        contact_person: currentOrganization.contact_person || '',
        contact_email: currentOrganization.contact_email || '',
        contact_phone: currentOrganization.contact_phone || '',
        contact_title: currentOrganization.contact_title || '',

        // Business Registration Details
        tax_id: currentOrganization.tax_id || '',
        registration_number: currentOrganization.registration_number || '',
        registration_date: currentOrganization.registration_date ? currentOrganization.registration_date.split('T')[0] : '',

        // Billing Information
        billing_address_line1: currentOrganization.billing_address_line1 || '',
        billing_address_line2: currentOrganization.billing_address_line2 || '',
        billing_city: currentOrganization.billing_city || '',
        billing_state: currentOrganization.billing_state || '',
        billing_postal_code: currentOrganization.billing_postal_code || '',
        billing_country: currentOrganization.billing_country || '',
        billing_contact_name: currentOrganization.billing_contact_name || '',
        billing_contact_email: currentOrganization.billing_contact_email || '',
        billing_contact_phone: currentOrganization.billing_contact_phone || '',

        // Subscription & Plan Details
        subscription_plan: currentOrganization.subscription_plan || 'basic',
        subscription_status: currentOrganization.subscription_status || 'active',
        subscription_start_date: currentOrganization.subscription_start_date ? currentOrganization.subscription_start_date.split('T')[0] : '',
        subscription_end_date: currentOrganization.subscription_end_date ? currentOrganization.subscription_end_date.split('T')[0] : '',
        billing_cycle: currentOrganization.billing_cycle || 'monthly',
        payment_method: currentOrganization.payment_method || 'invoice',

        // Service Limits
        max_users: currentOrganization.max_users || 100,
        max_locations: currentOrganization.max_locations || 10,
        max_monthly_tracked_hours: currentOrganization.max_monthly_tracked_hours || 5000,
        max_storage_gb: currentOrganization.max_storage_gb || 10,
        max_api_calls_per_hour: currentOrganization.max_api_calls_per_hour || 1000,

        // Organization Settings
        timezone: currentOrganization.timezone || 'America/New_York',
        date_format: currentOrganization.date_format || 'MM/DD/YYYY',
        time_format: currentOrganization.time_format || '12h',
        currency: currentOrganization.currency || 'USD',
        locale: currentOrganization.locale || 'en_US',

        // Compliance & Security
        security_level: currentOrganization.security_level || 'standard',
        two_factor_required: currentOrganization.two_factor_required || false,

        // Organization Status
        status: currentOrganization.status || 'active',
        approval_status: currentOrganization.approval_status || 'approved',

        // Metadata
        logo_url: currentOrganization.logo_url || '',
        notes: currentOrganization.notes || '',
        internal_notes: currentOrganization.internal_notes || '',
        tags: Array.isArray(currentOrganization.tags) ? currentOrganization.tags.join(', ') : '',

        // Admin credentials (don't populate password for security)
        adminUsername: currentOrganization.admin_username || '',
        adminPassword: '', // Never populate password
        adminFirstName: currentOrganization.admin_first_name || '',
        adminLastName: currentOrganization.admin_last_name || ''
      });
    }
  }, [isEditing, currentOrganization]);

  const handleInputChange = (field, value) => {
    try {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
      
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors(prev => ({
          ...prev,
          [field]: null
        }));
      }
    } catch (error) {
      // Ignore ResizeObserver errors
      if (!error.message?.includes('ResizeObserver')) {
        console.error('Error in handleInputChange:', error);
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Basic Organization Information - Required fields
    if (!formData.name.trim()) {
      newErrors.name = 'Organization name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    // Address Information - Required fields
    if (!formData.address_line1.trim()) {
      newErrors.address_line1 = 'Address line 1 is required';
    }

    if (!formData.country.trim()) {
      newErrors.country = 'Country is required';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }

    if (!formData.timezone.trim()) {
      newErrors.timezone = 'Timezone is required';
    }

    // Primary Contact Person - Required fields
    if (!formData.contact_person.trim()) {
      newErrors.contact_person = 'Contact person is required';
    }

    if (!formData.contact_email.trim()) {
      newErrors.contact_email = 'Contact email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.contact_email)) {
      newErrors.contact_email = 'Contact email is invalid';
    }

    // Email validation for billing contact if provided
    if (formData.billing_contact_email && !/\S+@\S+\.\S+/.test(formData.billing_contact_email)) {
      newErrors.billing_contact_email = 'Billing contact email is invalid';
    }

    // Service Limits validation
    if (formData.max_users < 1) {
      newErrors.max_users = 'Max users must be at least 1';
    }

    if (formData.max_locations < 1) {
      newErrors.max_locations = 'Max locations must be at least 1';
    }

    if (formData.max_monthly_tracked_hours < 1) {
      newErrors.max_monthly_tracked_hours = 'Max monthly tracked hours must be at least 1';
    }

    if (formData.max_storage_gb < 1) {
      newErrors.max_storage_gb = 'Max storage must be at least 1 GB';
    }

    if (formData.max_api_calls_per_hour < 1) {
      newErrors.max_api_calls_per_hour = 'Max API calls per hour must be at least 1';
    }

    // Website URL validation if provided
    if (formData.website && !/^https?:\/\/.+/.test(formData.website)) {
      newErrors.website = 'Website must be a valid URL (include http:// or https://)';
    }

    // Admin credentials validation (only for new organizations)
    if (!isEditing) {
      if (!formData.adminUsername.trim()) {
        newErrors.adminUsername = 'Admin username is required';
      } else if (formData.adminUsername.length < 3) {
        newErrors.adminUsername = 'Username must be at least 3 characters';
      }

      if (!formData.adminPassword.trim()) {
        newErrors.adminPassword = 'Admin password is required';
      } else if (formData.adminPassword.length < 6) {
        newErrors.adminPassword = 'Password must be at least 6 characters';
      }

      if (!formData.adminFirstName.trim()) {
        newErrors.adminFirstName = 'Admin first name is required';
      }

      if (!formData.adminLastName.trim()) {
        newErrors.adminLastName = 'Admin last name is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const organizationData = {
        // Basic Organization Information
        name: formData.name,
        legal_name: formData.legal_name,
        business_type: formData.business_type,
        industry: formData.industry,
        description: formData.description,
        website: formData.website,

        // Contact Information
        email: formData.email,
        phone: formData.phone,
        fax: formData.fax,

        // Address Information
        address_line1: formData.address_line1,
        address_line2: formData.address_line2,
        city: formData.city,
        state: formData.state,
        postal_code: formData.postal_code,
        country: formData.country,

        // Primary Contact Person
        contact_person: formData.contact_person,
        contact_email: formData.contact_email,
        contact_phone: formData.contact_phone,
        contact_title: formData.contact_title,

        // Business Registration Details
        tax_id: formData.tax_id,
        registration_number: formData.registration_number,
        registration_date: formData.registration_date,

        // Billing Information
        billing_address_line1: formData.billing_address_line1,
        billing_address_line2: formData.billing_address_line2,
        billing_city: formData.billing_city,
        billing_state: formData.billing_state,
        billing_postal_code: formData.billing_postal_code,
        billing_country: formData.billing_country,
        billing_contact_name: formData.billing_contact_name,
        billing_contact_email: formData.billing_contact_email,
        billing_contact_phone: formData.billing_contact_phone,

        // Subscription & Plan Details
        subscription_plan: formData.subscription_plan,
        subscription_status: formData.subscription_status,
        subscription_start_date: formData.subscription_start_date,
        subscription_end_date: formData.subscription_end_date,
        billing_cycle: formData.billing_cycle,
        payment_method: formData.payment_method,

        // Service Limits
        max_users: parseInt(formData.max_users),
        max_locations: parseInt(formData.max_locations),
        max_monthly_tracked_hours: parseInt(formData.max_monthly_tracked_hours),
        max_storage_gb: parseInt(formData.max_storage_gb),
        max_api_calls_per_hour: parseInt(formData.max_api_calls_per_hour),

        // Organization Settings
        timezone: formData.timezone,
        date_format: formData.date_format,
        time_format: formData.time_format,
        currency: formData.currency,
        locale: formData.locale,

        // Compliance & Security
        security_level: formData.security_level,
        two_factor_required: formData.two_factor_required,

        // Organization Status
        status: formData.status,
        approval_status: formData.approval_status,

        // Metadata
        logo_url: formData.logo_url,
        notes: formData.notes,
        internal_notes: formData.internal_notes,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : []
      };

      // Add admin credentials for new organizations
      if (!isEditing) {
        organizationData.adminUsername = formData.adminUsername;
        organizationData.adminPassword = formData.adminPassword;
        organizationData.adminFirstName = formData.adminFirstName;
        organizationData.adminLastName = formData.adminLastName;
      }
      
      if (isEditing) {
        await dispatch(updateOrganization({ organizationId: id, organizationData: organizationData })).unwrap();
      } else {
        await dispatch(createOrganization(organizationData)).unwrap();
      }
      
      // Navigate back to organizations list with a small delay to prevent ResizeObserver errors
      setTimeout(() => {
        navigate('/super-admin/organizations');
      }, 100);
      
    } catch (error) {
      // Ignore ResizeObserver errors but handle other errors
      if (error.message?.includes('ResizeObserver')) {
        // Still navigate on ResizeObserver errors as they don't affect functionality
        setTimeout(() => {
          navigate('/super-admin/organizations');
        }, 100);
        return;
      }
      
      console.error('Error saving organization:', error);
      setErrors({ submit: renderErrorMessage(error, 'Failed to save organization. Please try again.') });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading && isEditing) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600">Loading organization...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button 
            onClick={() => navigate('/super-admin/organizations')}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEditing ? 'Edit Organization' : 'Add New Organization'}
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              {isEditing ? 'Update organization details' : 'Create a new organization in the system'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={() => navigate('/super-admin/organizations')}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            form="organization-form"
            disabled={isSubmitting}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                {isEditing ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {isEditing ? 'Update Organization' : 'Create Organization'}
              </>
            )}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-sm text-red-600">
            {renderErrorMessage(error, 'An error occurred while processing your request.')}
          </p>
        </div>
      )}

      {errors.submit && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      {/* Form */}
      <form id="organization-form" onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Organization Information */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200">
          <div className="px-6 py-6 sm:p-8">
            <div className="flex items-center mb-6">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-blue-500 text-white">
                  <Building2 className="h-6 w-6" />
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Basic Organization Information</h3>
                <p className="text-sm text-gray-600">Essential details about the organization</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <div className="sm:col-span-2">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Organization Name *
                </label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`block w-full px-4 py-3 border rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.name ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400'
                  }`}
                  placeholder="Enter organization name"
                />
                {errors.name && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.name}</p>}
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="suspended">Suspended</option>
                  <option value="pending_approval">Pending Approval</option>
                </select>
              </div>

              <div>
                <label htmlFor="legal_name" className="block text-sm font-medium text-gray-700 mb-2">
                  Legal Name
                </label>
                <input
                  type="text"
                  id="legal_name"
                  value={formData.legal_name}
                  onChange={(e) => handleInputChange('legal_name', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Legal business name"
                />
              </div>

              <div>
                <label htmlFor="business_type" className="block text-sm font-medium text-gray-700 mb-2">
                  Business Type
                </label>
                <select
                  id="business_type"
                  value={formData.business_type}
                  onChange={(e) => handleInputChange('business_type', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select business type</option>
                  <option value="corporation">Corporation</option>
                  <option value="llc">LLC</option>
                  <option value="partnership">Partnership</option>
                  <option value="sole_proprietorship">Sole Proprietorship</option>
                  <option value="non_profit">Non-Profit</option>
                </select>
              </div>

              <div>
                <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-2">
                  Industry
                </label>
                <input
                  type="text"
                  id="industry"
                  value={formData.industry}
                  onChange={(e) => handleInputChange('industry', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Technology, Healthcare, Manufacturing"
                />
              </div>

              <div>
                <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
                  Website
                </label>
                <div className="relative">
                  <input
                    type="url"
                    id="website"
                    value={formData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    className={`block w-full pl-10 pr-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.website ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="https://www.example.com"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Link className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.website && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.website}</p>}
              </div>

              <div className="sm:col-span-2 lg:col-span-3">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  id="description"
                  rows={3}
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Brief description of the organization"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200">
          <div className="px-6 py-6 sm:p-8">
            <div className="flex items-center mb-6">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-green-500 text-white">
                  <Mail className="h-6 w-6" />
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Contact Information</h3>
                <p className="text-sm text-gray-600">Organization and primary contact details</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Organization Email *
                </label>
                <div className="relative">
                  <input
                    type="email"
                    id="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`block w-full pl-10 pr-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="<EMAIL>"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.email && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.email}</p>}
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <div className="relative">
                  <input
                    type="tel"
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="+****************"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="fax" className="block text-sm font-medium text-gray-700 mb-2">
                  Fax Number
                </label>
                <div className="relative">
                  <input
                    type="tel"
                    id="fax"
                    value={formData.fax}
                    onChange={(e) => handleInputChange('fax', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="+****************"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="contact_person" className="block text-sm font-medium text-gray-700 mb-2">
                  Primary Contact Person *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="contact_person"
                    value={formData.contact_person}
                    onChange={(e) => handleInputChange('contact_person', e.target.value)}
                    className={`block w-full pl-10 pr-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.contact_person ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="Full name"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.contact_person && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.contact_person}</p>}
              </div>

              <div>
                <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Email *
                </label>
                <div className="relative">
                  <input
                    type="email"
                    id="contact_email"
                    value={formData.contact_email}
                    onChange={(e) => handleInputChange('contact_email', e.target.value)}
                    className={`block w-full pl-10 pr-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.contact_email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="<EMAIL>"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.contact_email && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.contact_email}</p>}
              </div>

              <div>
                <label htmlFor="contact_phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Phone
                </label>
                <div className="relative">
                  <input
                    type="tel"
                    id="contact_phone"
                    value={formData.contact_phone}
                    onChange={(e) => handleInputChange('contact_phone', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="+****************"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div className="sm:col-span-2 lg:col-span-1">
                <label htmlFor="contact_title" className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Title
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="contact_title"
                    value={formData.contact_title}
                    onChange={(e) => handleInputChange('contact_title', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., CEO, Manager, Director"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Briefcase className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Address Information */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200">
          <div className="px-6 py-6 sm:p-8">
            <div className="flex items-center mb-6">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-purple-500 text-white">
                  <MapPin className="h-6 w-6" />
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Address Information</h3>
                <p className="text-sm text-gray-600">Physical location and address details</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <div className="sm:col-span-2">
                <label htmlFor="address_line1" className="block text-sm font-medium text-gray-700 mb-2">
                  Address Line 1 *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="address_line1"
                    value={formData.address_line1}
                    onChange={(e) => handleInputChange('address_line1', e.target.value)}
                    className={`block w-full pl-10 pr-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.address_line1 ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="Street address"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MapPin className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.address_line1 && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.address_line1}</p>}
              </div>

              <div>
                <label htmlFor="address_line2" className="block text-sm font-medium text-gray-700 mb-2">
                  Address Line 2
                </label>
                <input
                  type="text"
                  id="address_line2"
                  value={formData.address_line2}
                  onChange={(e) => handleInputChange('address_line2', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Apt, suite, unit, etc."
                />
              </div>

              <div>
                <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-2">
                  City *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="city"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    className={`block w-full pl-10 pr-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.city ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="City name"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MapPin className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.city && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.city}</p>}
              </div>

              <div>
                <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-2">
                  State/Province
                </label>
                <input
                  type="text"
                  id="state"
                  value={formData.state}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="State or province"
                />
              </div>

              <div>
                <label htmlFor="postal_code" className="block text-sm font-medium text-gray-700 mb-2">
                  Postal Code
                </label>
                <input
                  type="text"
                  id="postal_code"
                  value={formData.postal_code}
                  onChange={(e) => handleInputChange('postal_code', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="ZIP or postal code"
                />
              </div>

              <div>
                <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-2">
                  Country *
                </label>
                <div className="relative">
                  <select
                    id="country"
                    value={formData.country}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    className={`block w-full pl-10 pr-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.country ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                  >
                    <option value="United States">United States</option>
                    <option value="Canada">Canada</option>
                    <option value="United Kingdom">United Kingdom</option>
                    <option value="Australia">Australia</option>
                    <option value="Germany">Germany</option>
                    <option value="France">France</option>
                    <option value="Japan">Japan</option>
                    <option value="India">India</option>
                    <option value="Other">Other</option>
                  </select>
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Globe className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.country && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.country}</p>}
              </div>

              <div className="sm:col-span-2 lg:col-span-1">
                <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 mb-2">
                  Timezone *
                </label>
                <div className="relative">
                  <select
                    id="timezone"
                    value={formData.timezone}
                    onChange={(e) => handleInputChange('timezone', e.target.value)}
                    className={`block w-full pl-10 pr-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.timezone ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                  >
                    <option value="America/New_York">Eastern Time (ET)</option>
                    <option value="America/Chicago">Central Time (CT)</option>
                    <option value="America/Denver">Mountain Time (MT)</option>
                    <option value="America/Los_Angeles">Pacific Time (PT)</option>
                    <option value="UTC">UTC</option>
                    <option value="Europe/London">London (GMT)</option>
                    <option value="Europe/Paris">Paris (CET)</option>
                    <option value="Europe/Berlin">Berlin (CET)</option>
                    <option value="Asia/Tokyo">Tokyo (JST)</option>
                    <option value="Asia/Shanghai">Shanghai (CST)</option>
                    <option value="Asia/Kolkata">India (IST)</option>
                    <option value="Australia/Sydney">Sydney (AEST)</option>
                  </select>
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Clock className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.timezone && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.timezone}</p>}
              </div>
            </div>
          </div>
        </div>

        {/* Business Registration Details */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200">
          <div className="px-6 py-6 sm:p-8">
            <div className="flex items-center mb-6">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-indigo-500 text-white">
                  <FileText className="h-6 w-6" />
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Business Registration Details</h3>
                <p className="text-sm text-gray-600">Legal and registration information</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <div>
                <label htmlFor="tax_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Tax ID / EIN
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="tax_id"
                    value={formData.tax_id}
                    onChange={(e) => handleInputChange('tax_id', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="XX-XXXXXXX"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Hash className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="registration_number" className="block text-sm font-medium text-gray-700 mb-2">
                  Registration Number
                </label>
                <input
                  type="text"
                  id="registration_number"
                  value={formData.registration_number}
                  onChange={(e) => handleInputChange('registration_number', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Business registration number"
                />
              </div>

              <div>
                <label htmlFor="registration_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Registration Date
                </label>
                <div className="relative">
                  <input
                    type="date"
                    id="registration_date"
                    value={formData.registration_date}
                    onChange={(e) => handleInputChange('registration_date', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Subscription & Billing */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200">
          <div className="px-6 py-6 sm:p-8">
            <div className="flex items-center mb-6">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-yellow-500 text-white">
                  <CreditCard className="h-6 w-6" />
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Subscription & Billing</h3>
                <p className="text-sm text-gray-600">Plan details and billing information</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <div>
                <label htmlFor="subscription_plan" className="block text-sm font-medium text-gray-700 mb-2">
                  Subscription Plan
                </label>
                <select
                  id="subscription_plan"
                  value={formData.subscription_plan}
                  onChange={(e) => handleInputChange('subscription_plan', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="basic">Basic</option>
                  <option value="standard">Standard</option>
                  <option value="premium">Premium</option>
                  <option value="enterprise">Enterprise</option>
                </select>
              </div>

              <div>
                <label htmlFor="subscription_status" className="block text-sm font-medium text-gray-700 mb-2">
                  Subscription Status
                </label>
                <select
                  id="subscription_status"
                  value={formData.subscription_status}
                  onChange={(e) => handleInputChange('subscription_status', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="active">Active</option>
                  <option value="trial">Trial</option>
                  <option value="suspended">Suspended</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div>
                <label htmlFor="billing_cycle" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing Cycle
                </label>
                <select
                  id="billing_cycle"
                  value={formData.billing_cycle}
                  onChange={(e) => handleInputChange('billing_cycle', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="monthly">Monthly</option>
                  <option value="quarterly">Quarterly</option>
                  <option value="annual">Annual</option>
                </select>
              </div>

              <div>
                <label htmlFor="payment_method" className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Method
                </label>
                <select
                  id="payment_method"
                  value={formData.payment_method}
                  onChange={(e) => handleInputChange('payment_method', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="invoice">Invoice</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="subscription_start_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Subscription Start Date
                </label>
                <div className="relative">
                  <input
                    type="date"
                    id="subscription_start_date"
                    value={formData.subscription_start_date}
                    onChange={(e) => handleInputChange('subscription_start_date', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="subscription_end_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Subscription End Date
                </label>
                <div className="relative">
                  <input
                    type="date"
                    id="subscription_end_date"
                    value={formData.subscription_end_date}
                    onChange={(e) => handleInputChange('subscription_end_date', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Service Limits */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200">
          <div className="px-6 py-6 sm:p-8">
            <div className="flex items-center mb-6">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-red-500 text-white">
                  <Settings className="h-6 w-6" />
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Service Limits</h3>
                <p className="text-sm text-gray-600">Usage limits and quotas for the organization</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <div>
                <label htmlFor="max_users" className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Users
                </label>
                <input
                  type="number"
                  id="max_users"
                  min="1"
                  value={formData.max_users}
                  onChange={(e) => handleInputChange('max_users', parseInt(e.target.value) || 0)}
                  className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.max_users ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="100"
                />
                {errors.max_users && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.max_users}</p>}
              </div>

              <div>
                <label htmlFor="max_locations" className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Locations
                </label>
                <input
                  type="number"
                  id="max_locations"
                  min="1"
                  value={formData.max_locations}
                  onChange={(e) => handleInputChange('max_locations', parseInt(e.target.value) || 0)}
                  className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.max_locations ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="10"
                />
                {errors.max_locations && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.max_locations}</p>}
              </div>

              <div>
                <label htmlFor="max_monthly_tracked_hours" className="block text-sm font-medium text-gray-700 mb-2">
                  Max Monthly Tracked Hours
                </label>
                <input
                  type="number"
                  id="max_monthly_tracked_hours"
                  min="1"
                  value={formData.max_monthly_tracked_hours}
                  onChange={(e) => handleInputChange('max_monthly_tracked_hours', parseInt(e.target.value) || 0)}
                  className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.max_monthly_tracked_hours ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="5000"
                />
                {errors.max_monthly_tracked_hours && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.max_monthly_tracked_hours}</p>}
              </div>

              <div>
                <label htmlFor="max_storage_gb" className="block text-sm font-medium text-gray-700 mb-2">
                  Max Storage (GB)
                </label>
                <input
                  type="number"
                  id="max_storage_gb"
                  min="1"
                  value={formData.max_storage_gb}
                  onChange={(e) => handleInputChange('max_storage_gb', parseInt(e.target.value) || 0)}
                  className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.max_storage_gb ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="10"
                />
                {errors.max_storage_gb && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.max_storage_gb}</p>}
              </div>

              <div>
                <label htmlFor="max_api_calls_per_hour" className="block text-sm font-medium text-gray-700 mb-2">
                  Max API Calls/Hour
                </label>
                <input
                  type="number"
                  id="max_api_calls_per_hour"
                  min="1"
                  value={formData.max_api_calls_per_hour}
                  onChange={(e) => handleInputChange('max_api_calls_per_hour', parseInt(e.target.value) || 0)}
                  className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.max_api_calls_per_hour ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="1000"
                />
                {errors.max_api_calls_per_hour && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.max_api_calls_per_hour}</p>}
              </div>
            </div>
          </div>
        </div>

        {/* Billing Information */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200">
          <div className="px-6 py-6 sm:p-8">
            <div className="flex items-center mb-6">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-orange-500 text-white">
                  <DollarSign className="h-6 w-6" />
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Billing Information</h3>
                <p className="text-sm text-gray-600">Billing address and contact details</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <div className="sm:col-span-2">
                <label htmlFor="billing_address_line1" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing Address Line 1
                </label>
                <input
                  type="text"
                  id="billing_address_line1"
                  value={formData.billing_address_line1}
                  onChange={(e) => handleInputChange('billing_address_line1', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Billing street address (if different from organization address)"
                />
              </div>

              <div>
                <label htmlFor="billing_address_line2" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing Address Line 2
                </label>
                <input
                  type="text"
                  id="billing_address_line2"
                  value={formData.billing_address_line2}
                  onChange={(e) => handleInputChange('billing_address_line2', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Apt, suite, unit, etc."
                />
              </div>

              <div>
                <label htmlFor="billing_city" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing City
                </label>
                <input
                  type="text"
                  id="billing_city"
                  value={formData.billing_city}
                  onChange={(e) => handleInputChange('billing_city', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Billing city"
                />
              </div>

              <div>
                <label htmlFor="billing_state" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing State/Province
                </label>
                <input
                  type="text"
                  id="billing_state"
                  value={formData.billing_state}
                  onChange={(e) => handleInputChange('billing_state', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Billing state or province"
                />
              </div>

              <div>
                <label htmlFor="billing_postal_code" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing Postal Code
                </label>
                <input
                  type="text"
                  id="billing_postal_code"
                  value={formData.billing_postal_code}
                  onChange={(e) => handleInputChange('billing_postal_code', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Billing ZIP or postal code"
                />
              </div>

              <div>
                <label htmlFor="billing_country" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing Country
                </label>
                <select
                  id="billing_country"
                  value={formData.billing_country}
                  onChange={(e) => handleInputChange('billing_country', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Same as organization address</option>
                  <option value="United States">United States</option>
                  <option value="Canada">Canada</option>
                  <option value="United Kingdom">United Kingdom</option>
                  <option value="Australia">Australia</option>
                  <option value="Germany">Germany</option>
                  <option value="France">France</option>
                  <option value="Japan">Japan</option>
                  <option value="India">India</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="billing_contact_name" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing Contact Name
                </label>
                <input
                  type="text"
                  id="billing_contact_name"
                  value={formData.billing_contact_name}
                  onChange={(e) => handleInputChange('billing_contact_name', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Billing contact person"
                />
              </div>

              <div>
                <label htmlFor="billing_contact_email" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing Contact Email
                </label>
                <div className="relative">
                  <input
                    type="email"
                    id="billing_contact_email"
                    value={formData.billing_contact_email}
                    onChange={(e) => handleInputChange('billing_contact_email', e.target.value)}
                    className={`block w-full pl-10 pr-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.billing_contact_email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="<EMAIL>"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.billing_contact_email && <p className="mt-2 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.billing_contact_email}</p>}
              </div>

              <div>
                <label htmlFor="billing_contact_phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing Contact Phone
                </label>
                <div className="relative">
                  <input
                    type="tel"
                    id="billing_contact_phone"
                    value={formData.billing_contact_phone}
                    onChange={(e) => handleInputChange('billing_contact_phone', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="+****************"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Organization Settings & Security */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200">
          <div className="px-6 py-6 sm:p-8">
            <div className="flex items-center mb-6">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-teal-500 text-white">
                  <Shield className="h-6 w-6" />
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Organization Settings & Security</h3>
                <p className="text-sm text-gray-600">Localization, security, and system preferences</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              <div>
                <label htmlFor="date_format" className="block text-sm font-medium text-gray-700 mb-2">
                  Date Format
                </label>
                <select
                  id="date_format"
                  value={formData.date_format}
                  onChange={(e) => handleInputChange('date_format', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                  <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                  <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                </select>
              </div>

              <div>
                <label htmlFor="time_format" className="block text-sm font-medium text-gray-700 mb-2">
                  Time Format
                </label>
                <select
                  id="time_format"
                  value={formData.time_format}
                  onChange={(e) => handleInputChange('time_format', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="12h">12 Hour</option>
                  <option value="24h">24 Hour</option>
                </select>
              </div>

              <div>
                <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
                  Currency
                </label>
                <select
                  id="currency"
                  value={formData.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="USD">USD - US Dollar</option>
                  <option value="EUR">EUR - Euro</option>
                  <option value="GBP">GBP - British Pound</option>
                  <option value="CAD">CAD - Canadian Dollar</option>
                  <option value="AUD">AUD - Australian Dollar</option>
                  <option value="JPY">JPY - Japanese Yen</option>
                  <option value="INR">INR - Indian Rupee</option>
                </select>
              </div>

              <div>
                <label htmlFor="locale" className="block text-sm font-medium text-gray-700 mb-2">
                  Locale
                </label>
                <select
                  id="locale"
                  value={formData.locale}
                  onChange={(e) => handleInputChange('locale', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="en_US">English (US)</option>
                  <option value="en_GB">English (UK)</option>
                  <option value="en_CA">English (Canada)</option>
                  <option value="en_AU">English (Australia)</option>
                  <option value="fr_FR">French (France)</option>
                  <option value="de_DE">German (Germany)</option>
                  <option value="es_ES">Spanish (Spain)</option>
                  <option value="ja_JP">Japanese (Japan)</option>
                </select>
              </div>

              <div>
                <label htmlFor="security_level" className="block text-sm font-medium text-gray-700 mb-2">
                  Security Level
                </label>
                <select
                  id="security_level"
                  value={formData.security_level}
                  onChange={(e) => handleInputChange('security_level', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="basic">Basic</option>
                  <option value="standard">Standard</option>
                  <option value="high">High</option>
                  <option value="enterprise">Enterprise</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="two_factor_required"
                  checked={formData.two_factor_required}
                  onChange={(e) => handleInputChange('two_factor_required', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="two_factor_required" className="ml-2 block text-sm text-gray-900">
                  Require Two-Factor Authentication
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200">
          <div className="px-6 py-6 sm:p-8">
            <div className="flex items-center mb-6">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-gray-500 text-white">
                  <Info className="h-6 w-6" />
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Additional Information</h3>
                <p className="text-sm text-gray-600">Logo, notes, and tags</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="logo_url" className="block text-sm font-medium text-gray-700 mb-2">
                  Logo URL
                </label>
                <div className="relative">
                  <input
                    type="url"
                    id="logo_url"
                    value={formData.logo_url}
                    onChange={(e) => handleInputChange('logo_url', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="https://example.com/logo.png"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Link className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="tags"
                    value={formData.tags}
                    onChange={(e) => handleInputChange('tags', e.target.value)}
                    className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="tag1, tag2, tag3"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Tag className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                <p className="mt-1 text-sm text-gray-500">Separate tags with commas</p>
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                  Public Notes
                </label>
                <textarea
                  id="notes"
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Public notes about the organization"
                />
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="internal_notes" className="block text-sm font-medium text-gray-700 mb-2">
                  Internal Notes
                  <span className="text-xs text-gray-500 ml-2">(Admin use only)</span>
                </label>
                <textarea
                  id="internal_notes"
                  rows={3}
                  value={formData.internal_notes}
                  onChange={(e) => handleInputChange('internal_notes', e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Internal notes for administrative use"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Admin Credentials - Only show for new organizations */}
        {!isEditing && (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg rounded-xl border border-blue-100">
            <div className="px-6 py-6 sm:p-8">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-blue-500 text-white">
                    <User className="h-6 w-6" />
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-gray-900">Administrator Account</h3>
                  <p className="text-sm text-blue-600 font-medium">Required for new organizations</p>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 mb-6 border border-blue-200">
                <p className="text-sm text-gray-700 leading-relaxed">
                  <span className="font-medium text-gray-900">Create an administrator account</span> for this organization.
                  This user will have full access to manage the organization, users, locations, and all system features.
                </p>
              </div>

              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="adminFirstName" className="block text-sm font-medium text-gray-700">
                    First Name *
                  </label>
                  <input
                    type="text"
                    id="adminFirstName"
                    value={formData.adminFirstName}
                    onChange={(e) => handleInputChange('adminFirstName', e.target.value)}
                    className={`mt-1 block w-full px-4 py-3 border rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.adminFirstName ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400 focus:bg-white'
                    }`}
                    placeholder="Enter first name"
                  />
                  {errors.adminFirstName && <p className="mt-2 text-sm text-red-600 flex items-center"><span className="mr-1">⚠</span>{errors.adminFirstName}</p>}
                </div>

                <div>
                  <label htmlFor="adminLastName" className="block text-sm font-medium text-gray-700">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    id="adminLastName"
                    value={formData.adminLastName}
                    onChange={(e) => handleInputChange('adminLastName', e.target.value)}
                    className={`mt-1 block w-full px-4 py-3 border rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.adminLastName ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400 focus:bg-white'
                    }`}
                    placeholder="Enter last name"
                  />
                  {errors.adminLastName && <p className="mt-2 text-sm text-red-600 flex items-center"><span className="mr-1">⚠</span>{errors.adminLastName}</p>}
                </div>

                <div>
                  <label htmlFor="adminUsername" className="block text-sm font-medium text-gray-700 mb-1">
                    Username *
                  </label>
                  <input
                    type="text"
                    id="adminUsername"
                    value={formData.adminUsername}
                    onChange={(e) => handleInputChange('adminUsername', e.target.value)}
                    className={`mt-1 block w-full px-4 py-3 border rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.adminUsername ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400 focus:bg-white'
                    }`}
                    placeholder="Choose a username"
                  />
                  {errors.adminUsername && <p className="mt-2 text-sm text-red-600 flex items-center"><span className="mr-1">⚠</span>{errors.adminUsername}</p>}
                </div>

                <div>
                  <label htmlFor="adminPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Password *
                  </label>
                  <input
                    type="password"
                    id="adminPassword"
                    value={formData.adminPassword}
                    onChange={(e) => handleInputChange('adminPassword', e.target.value)}
                    className={`mt-1 block w-full px-4 py-3 border rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.adminPassword ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400 focus:bg-white'
                    }`}
                    placeholder="Minimum 6 characters"
                  />
                  {errors.adminPassword && <p className="mt-2 text-sm text-red-600 flex items-center"><span className="mr-1">⚠</span>{errors.adminPassword}</p>}
                </div>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default OrganizationForm;

