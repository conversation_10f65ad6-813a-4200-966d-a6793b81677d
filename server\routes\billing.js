const express = require('express');
const router = express.Router();
const { pool } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const puppeteer = require('puppeteer');
const nodemailer = require('nodemailer');

// Get all invoices across all organizations (Super Admin only)
router.get('/invoices', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { 
      status = 'all',
      page = 1, 
      limit = 20,
      search = ''
    } = req.query;

    let query = `
      SELECT
        i.*,
        o.name as organization_name,
        o.subscription_plan,
        o.billing_cycle
      FROM invoices i
      JOIN organizations o ON i.organization_id = o.id
      WHERE 1=1
    `;
    
    const params = [];
    let paramIndex = 1;

    if (status !== 'all') {
      query += ` AND i.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (search) {
      query += ` AND (o.name ILIKE $${paramIndex} OR i.invoice_number ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    query += ` ORDER BY i.created_at DESC`;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, (page - 1) * limit);

    const result = await pool.query(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM invoices i
      JOIN organizations o ON i.organization_id = o.id
      WHERE 1=1
    `;
    const countParams = [];
    let countParamIndex = 1;

    if (status !== 'all') {
      countQuery += ` AND i.status = $${countParamIndex}`;
      countParams.push(status);
      countParamIndex++;
    }

    if (search) {
      countQuery += ` AND (o.name ILIKE $${countParamIndex} OR i.invoice_number ILIKE $${countParamIndex})`;
      countParams.push(`%${search}%`);
    }

    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    res.json({
      invoices: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching all invoices:', error);
    res.status(500).json({ error: 'Failed to fetch invoices' });
  }
});

// Get billing statistics (Super Admin only)
router.get('/stats', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    // Get total revenue
    const revenueResult = await pool.query(`
      SELECT COALESCE(SUM(total_amount), 0) as total_revenue
      FROM invoices 
      WHERE status = 'paid'
    `);

    // Get pending payments
    const pendingResult = await pool.query(`
      SELECT COALESCE(SUM(total_amount), 0) as pending_amount
      FROM invoices 
      WHERE status = 'pending' OR status = 'overdue'
    `);

    // Get overdue amount
    const overdueResult = await pool.query(`
      SELECT COALESCE(SUM(total_amount), 0) as overdue_amount
      FROM invoices 
      WHERE status = 'overdue' OR (status = 'pending' AND due_date < CURRENT_DATE)
    `);

    // Get total invoices count
    const countResult = await pool.query(`
      SELECT COUNT(*) as total_invoices
      FROM invoices
    `);

    res.json({
      total_revenue: parseFloat(revenueResult.rows[0].total_revenue),
      pending_amount: parseFloat(pendingResult.rows[0].pending_amount),
      overdue_amount: parseFloat(overdueResult.rows[0].overdue_amount),
      total_invoices: parseInt(countResult.rows[0].total_invoices)
    });
  } catch (error) {
    console.error('Error fetching billing stats:', error);
    res.status(500).json({ error: 'Failed to fetch billing statistics' });
  }
});

// Get single invoice details
router.get('/invoices/:invoiceId', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { invoiceId } = req.params;

    const invoiceResult = await pool.query(`
      SELECT
        i.*,
        o.name as organization_name,
        o.subscription_plan,
        o.billing_cycle,
        o.email as organization_email,
        o.address_line1,
        o.address_line2,
        o.city,
        o.state,
        o.postal_code,
        o.country,
        o.contact_person,
        o.contact_email,
        o.contact_phone
      FROM invoices i
      JOIN organizations o ON i.organization_id = o.id
      WHERE i.id = $1
    `, [invoiceId]);

    if (invoiceResult.rows.length === 0) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    const invoice = invoiceResult.rows[0];

    // Get line items
    const lineItemsResult = await pool.query(`
      SELECT * FROM invoice_line_items
      WHERE invoice_id = $1
      ORDER BY created_at
    `, [invoiceId]);

    res.json({
      ...invoice,
      line_items: lineItemsResult.rows
    });
  } catch (error) {
    console.error('Error fetching invoice details:', error);
    res.status(500).json({ error: 'Failed to fetch invoice details' });
  }
});

// Update invoice
router.put('/invoices/:invoiceId', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const { status, due_date, notes, payment_date, payment_method, subtotal, tax_rate, tax_amount, total_amount } = req.body;

    // Convert empty date strings to null to avoid PostgreSQL type coercion issues
    const cleanDueDate = due_date === '' ? null : due_date;
    const cleanPaymentDate = payment_date === '' ? null : payment_date;
    const cleanPaymentMethod = payment_method === '' ? null : payment_method;

    const updateResult = await pool.query(`
      UPDATE invoices
      SET
        status = COALESCE($1, status),
        due_date = COALESCE($2, due_date),
        notes = COALESCE($3, notes),
        payment_date = COALESCE($4, payment_date),
        payment_method = COALESCE($5, payment_method),
        subtotal = COALESCE($6, subtotal),
        tax_rate = COALESCE($7, tax_rate),
        tax_amount = COALESCE($8, tax_amount),
        total_amount = COALESCE($9, total_amount),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $10
      RETURNING *
    `, [status, cleanDueDate, notes, cleanPaymentDate, cleanPaymentMethod, subtotal, tax_rate, tax_amount, total_amount, invoiceId]);

    if (updateResult.rows.length === 0) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    res.json(updateResult.rows[0]);
  } catch (error) {
    console.error('Error updating invoice:', error);
    res.status(500).json({ error: 'Failed to update invoice' });
  }
});

// Create invoice for organization
router.post('/organizations/:organizationId/invoices', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      billing_period_start, 
      billing_period_end,
      due_date,
      tax_rate = 0,
      notes = ''
    } = req.body;

    // Get organization details with subscription plan pricing
    const orgResult = await pool.query(`
      SELECT
        o.name,
        o.subscription_plan,
        o.billing_cycle,
        o.max_users,
        sp.monthly_price,
        sp.annual_price,
        sp.per_user_fee,
        sp.display_name as plan_display_name
      FROM organizations o
      LEFT JOIN subscription_plans sp ON o.subscription_plan = sp.name
      WHERE o.id = $1
    `, [organizationId]);

    if (orgResult.rows.length === 0) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const org = orgResult.rows[0];

    // Get user count for the billing period
    const userCountResult = await pool.query(`
      SELECT COUNT(DISTINCT id) as active_users
      FROM users 
      WHERE organization_id = $1 
      AND status = 'active'
      AND created_at <= $2
    `, [organizationId, billing_period_end]);

    const activeUsers = parseInt(userCountResult.rows[0].active_users);

    // Calculate billing based on subscription plan
    const isAnnual = org.billing_cycle === 'annual';
    const planPrice = parseFloat(isAnnual ? org.annual_price : org.monthly_price) || 0;
    const perUserFee = parseFloat(org.per_user_fee) || 0;

    // Calculate total: base plan price + (per-user fee * active users)
    const planCost = planPrice;
    const userCost = activeUsers * perUserFee;
    const subtotal = planCost + userCost;
    const taxAmount = subtotal * (tax_rate / 100);
    const totalAmount = subtotal + taxAmount;

    // Generate invoice number
    const invoiceNumber = `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`;

    // Create invoice
    const invoiceResult = await pool.query(`
      INSERT INTO invoices (
        organization_id,
        invoice_number,
        billing_period_start,
        billing_period_end,
        due_date,
        subtotal,
        tax_rate,
        tax_amount,
        total_amount,
        status,
        notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 'pending', $10)
      RETURNING *
    `, [
      organizationId,
      invoiceNumber,
      billing_period_start,
      billing_period_end,
      due_date,
      subtotal,
      tax_rate,
      taxAmount,
      totalAmount,
      notes
    ]);

    const invoice = invoiceResult.rows[0];

    // Create invoice line items using the actual table schema
    const lineItems = [];

    // For now, create a single line item representing the organization's subscription
    // The actual schema expects location-based line items, but we'll use a general approach
    await pool.query(`
      INSERT INTO invoice_line_items (
        invoice_id,
        location_name,
        user_count,
        unit_price,
        line_total
      ) VALUES ($1, $2, $3, $4, $5)
    `, [
      invoice.id,
      `${org.name} - ${org.plan_display_name || org.subscription_plan} Plan`,
      activeUsers,
      subtotal / Math.max(activeUsers, 1), // Calculate effective per-user rate
      subtotal
    ]);

    lineItems.push({
      location_name: `${org.name} - ${org.plan_display_name || org.subscription_plan} Plan`,
      user_count: activeUsers,
      unit_price: subtotal / Math.max(activeUsers, 1),
      line_total: subtotal,
      breakdown: [
        ...(planCost > 0 ? [{
          description: `${org.plan_display_name || org.subscription_plan} - ${isAnnual ? 'Annual' : 'Monthly'} Plan`,
          amount: planCost
        }] : []),
        ...(userCost > 0 ? [{
          description: `Per-user fee (${activeUsers} users × $${perUserFee})`,
          amount: userCost
        }] : [])
      ]
    });

    res.status(201).json({
      ...invoice,
      organization_name: org.name,
      line_items: lineItems
    });
  } catch (error) {
    console.error('Error creating invoice:', error);
    res.status(500).json({ error: 'Failed to create invoice' });
  }
});

// Download invoice as PDF
router.get('/invoices/:invoiceId/download', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { invoiceId } = req.params;

    // Get invoice details
    const invoiceResult = await pool.query(`
      SELECT
        i.*,
        o.name as organization_name,
        o.subscription_plan,
        o.billing_cycle,
        o.email as organization_email,
        o.address_line1,
        o.address_line2,
        o.city,
        o.state,
        o.postal_code,
        o.country,
        o.contact_person,
        o.contact_email,
        o.contact_phone
      FROM invoices i
      JOIN organizations o ON i.organization_id = o.id
      WHERE i.id = $1
    `, [invoiceId]);

    if (invoiceResult.rows.length === 0) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    const invoice = invoiceResult.rows[0];

    // Get line items
    const lineItemsResult = await pool.query(`
      SELECT * FROM invoice_line_items
      WHERE invoice_id = $1
      ORDER BY created_at
    `, [invoiceId]);

    // Generate PDF using Puppeteer
    const pdfBuffer = await generateInvoicePDF(invoice, lineItemsResult.rows);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${invoice.invoice_number}.pdf"`);
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Error downloading invoice:', error);
    res.status(500).json({ error: 'Failed to download invoice' });
  }
});

// Send invoice email
router.post('/invoices/:invoiceId/send-email', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const { to, subject, message } = req.body;

    // Get invoice details
    const invoiceResult = await pool.query(`
      SELECT
        i.*,
        o.name as organization_name,
        o.contact_email,
        o.contact_person
      FROM invoices i
      JOIN organizations o ON i.organization_id = o.id
      WHERE i.id = $1
    `, [invoiceId]);

    if (invoiceResult.rows.length === 0) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    const invoice = invoiceResult.rows[0];

    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      console.error('Email sending is not configured. Set EMAIL_USER and EMAIL_PASS in your .env file.');
      return res.status(500).json({ error: 'Email service is not configured on the server.' });
    }

    // Create email transporter (using Gmail for demo - in production use proper SMTP)
    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER || '<EMAIL>',
        pass: process.env.EMAIL_PASS || 'your-app-password'
      }
    });

    const emailTo = to || invoice.contact_email || '<EMAIL>';
    const emailSubject = subject || `Invoice ${invoice.invoice_number} - Payment Due`;
    const emailMessage = message || `
      Dear ${invoice.contact_person || 'Valued Customer'},

      Please find your invoice details below:

      Invoice Number: ${invoice.invoice_number}
      Amount Due: ${parseFloat(invoice.total_amount).toFixed(2)}
      Due Date: ${new Date(invoice.due_date).toLocaleDateString()}

      ${invoice.notes ? `Notes: ${invoice.notes}` : ''}

      Please process payment at your earliest convenience.

      Thank you for your business!

      OnTheMove Admin Team
      <EMAIL>
    `;

    try {
      // For demo purposes, we'll just log the email instead of actually sending it
      // In production, uncomment the following lines and configure proper SMTP settings

      /*
      await transporter.sendMail({
        from: process.env.EMAIL_USER || '<EMAIL>',
        to: emailTo,
        subject: emailSubject,
        text: emailMessage,
        html: `<pre>${emailMessage}</pre>`
      });
      */

      console.log('Email would be sent:', {
        to: emailTo,
        subject: emailSubject,
        message: emailMessage,
        invoiceNumber: invoice.invoice_number,
        amount: invoice.total_amount
      });

      res.json({
        success: true,
        message: 'Invoice email sent successfully',
        sentTo: emailTo
      });
    } catch (emailError) {
      console.error('Error sending email:', emailError);
      res.status(500).json({ error: 'Failed to send email' });
    }
  } catch (error) {
    console.error('Error sending invoice email:', error);
    res.status(500).json({ error: 'Failed to send invoice email' });
  }
});

// Helper function to generate PDF using Puppeteer
async function generateInvoicePDF(invoice, lineItems) {
  const browser = await puppeteer.launch({
    headless: 'new',
    args: []
  });

  try {
    const page = await browser.newPage();

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invoice ${invoice.invoice_number}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            color: #333;
            line-height: 1.6;
          }
          .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
          }
          .company-info {
            text-align: right;
          }
          .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
          }
          .invoice-title {
            font-size: 36px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
          }
          .invoice-number {
            font-size: 18px;
            color: #6b7280;
          }
          .details-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
          }
          .bill-to, .invoice-details {
            width: 45%;
          }
          .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1f2937;
          }
          .organization-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
          }
          .line-items {
            margin-bottom: 30px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
          }
          th {
            background-color: #f9fafb;
            font-weight: bold;
            color: #1f2937;
          }
          .text-right {
            text-align: right;
          }
          .totals {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 40px;
          }
          .totals-table {
            width: 300px;
          }
          .totals-table td {
            border: none;
            padding: 8px 12px;
          }
          .total-row {
            font-weight: bold;
            font-size: 18px;
            border-top: 2px solid #1f2937;
          }
          .notes {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
          }
          .footer {
            text-align: center;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
          }
          .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
          }
          .status-paid { background-color: #dcfce7; color: #166534; }
          .status-pending { background-color: #fef3c7; color: #92400e; }
          .status-overdue { background-color: #fee2e2; color: #991b1b; }
          .status-draft { background-color: #f3f4f6; color: #374151; }
          .status-cancelled { background-color: #f3f4f6; color: #6b7280; }
        </style>
      </head>
      <body>
        <div class="header">
          <div>
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-number">#${invoice.invoice_number}</div>
          </div>
          <div class="company-info">
            <div class="company-name">OnTheMove Admin</div>
            <div>Super Admin Portal</div>
            <div><EMAIL></div>
            <div>+****************</div>
          </div>
        </div>

        <div class="details-section">
          <div class="bill-to">
            <div class="section-title">Bill To:</div>
            <div class="organization-name">${invoice.organization_name}</div>
            ${invoice.contact_person ? `<div>Attn: ${invoice.contact_person}</div>` : ''}
            ${invoice.address_line1 ? `<div>${invoice.address_line1}</div>` : ''}
            ${invoice.address_line2 ? `<div>${invoice.address_line2}</div>` : ''}
            ${invoice.city || invoice.state || invoice.postal_code ? `
              <div>
                ${invoice.city ? invoice.city + ', ' : ''}
                ${invoice.state ? invoice.state + ' ' : ''}
                ${invoice.postal_code || ''}
              </div>
            ` : ''}
            ${invoice.country ? `<div>${invoice.country}</div>` : ''}
            ${invoice.contact_email ? `<div style="margin-top: 10px;">Email: ${invoice.contact_email}</div>` : ''}
            ${invoice.contact_phone ? `<div>Phone: ${invoice.contact_phone}</div>` : ''}
          </div>

          <div class="invoice-details">
            <div class="section-title">Invoice Details:</div>
            <table style="width: 100%; border: none;">
              <tr><td style="border: none; padding: 4px 0;">Invoice Date:</td><td style="border: none; padding: 4px 0; text-align: right;">${new Date(invoice.created_at).toLocaleDateString()}</td></tr>
              <tr><td style="border: none; padding: 4px 0;">Due Date:</td><td style="border: none; padding: 4px 0; text-align: right;">${new Date(invoice.due_date).toLocaleDateString()}</td></tr>
              <tr><td style="border: none; padding: 4px 0;">Billing Period:</td><td style="border: none; padding: 4px 0; text-align: right;">${new Date(invoice.billing_period_start).toLocaleDateString()} - ${new Date(invoice.billing_period_end).toLocaleDateString()}</td></tr>
              <tr><td style="border: none; padding: 4px 0;">Status:</td><td style="border: none; padding: 4px 0; text-align: right;"><span class="status-badge status-${invoice.status}">${invoice.status}</span></td></tr>
              ${invoice.payment_date ? `<tr><td style="border: none; padding: 4px 0;">Payment Date:</td><td style="border: none; padding: 4px 0; text-align: right;">${new Date(invoice.payment_date).toLocaleDateString()}</td></tr>` : ''}
            </table>
          </div>
        </div>

        <div class="line-items">
          <div class="section-title">Services:</div>
          <table>
            <thead>
              <tr>
                <th>Description</th>
                <th class="text-right">Users</th>
                <th class="text-right">Rate</th>
                <th class="text-right">Amount</th>
              </tr>
            </thead>
            <tbody>
              ${lineItems.map(item => `
                <tr>
                  <td>${item.location_name}</td>
                  <td class="text-right">${item.user_count}</td>
                  <td class="text-right">${parseFloat(item.unit_price).toFixed(2)}</td>
                  <td class="text-right">${parseFloat(item.line_total).toFixed(2)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>

        <div class="totals">
          <table class="totals-table">
            <tr>
              <td>Subtotal:</td>
              <td class="text-right">${parseFloat(invoice.subtotal).toFixed(2)}</td>
            </tr>
            ${parseFloat(invoice.tax_amount) > 0 ? `
              <tr>
                <td>Tax (${invoice.tax_rate}%):</td>
                <td class="text-right">${parseFloat(invoice.tax_amount).toFixed(2)}</td>
              </tr>
            ` : ''}
            <tr class="total-row">
              <td>Total:</td>
              <td class="text-right">${parseFloat(invoice.total_amount).toFixed(2)}</td>
            </tr>
          </table>
        </div>

        ${invoice.notes ? `
          <div class="notes">
            <div class="section-title">Notes:</div>
            <div>${invoice.notes}</div>
          </div>
        ` : ''}

        <div class="footer">
          <p>Thank you for your business!</p>
          <p>For questions about this invoice, <NAME_EMAIL></p>
        </div>
      </body>
      </html>
    `;

    await page.setContent(htmlContent, { waitUntil: 'load' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      margin: {
        top: '20px',
        right: '20px',
        bottom: '20px',
        left: '20px'
      },
      printBackground: true,
      preferCSSPageSize: true
    });

    return pdfBuffer;
  } finally {
    await browser.close();
  }
}

module.exports = router;
