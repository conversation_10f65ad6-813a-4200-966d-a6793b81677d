const express = require('express');
const router = express.Router();
const { pool } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');

// Get all invoices across all organizations (Super Admin only)
router.get('/invoices', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { 
      status = 'all',
      page = 1, 
      limit = 20,
      search = ''
    } = req.query;

    let query = `
      SELECT
        i.*,
        o.name as organization_name,
        o.subscription_plan,
        o.billing_cycle
      FROM invoices i
      JOIN organizations o ON i.organization_id = o.id
      WHERE 1=1
    `;
    
    const params = [];
    let paramIndex = 1;

    if (status !== 'all') {
      query += ` AND i.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (search) {
      query += ` AND (o.name ILIKE $${paramIndex} OR i.invoice_number ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    query += ` ORDER BY i.created_at DESC`;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, (page - 1) * limit);

    const result = await pool.query(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM invoices i
      JOIN organizations o ON i.organization_id = o.id
      WHERE 1=1
    `;
    const countParams = [];
    let countParamIndex = 1;

    if (status !== 'all') {
      countQuery += ` AND i.status = $${countParamIndex}`;
      countParams.push(status);
      countParamIndex++;
    }

    if (search) {
      countQuery += ` AND (o.name ILIKE $${countParamIndex} OR i.invoice_number ILIKE $${countParamIndex})`;
      countParams.push(`%${search}%`);
    }

    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    res.json({
      invoices: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching all invoices:', error);
    res.status(500).json({ error: 'Failed to fetch invoices' });
  }
});

// Get billing statistics (Super Admin only)
router.get('/stats', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    // Get total revenue
    const revenueResult = await pool.query(`
      SELECT COALESCE(SUM(total_amount), 0) as total_revenue
      FROM invoices 
      WHERE status = 'paid'
    `);

    // Get pending payments
    const pendingResult = await pool.query(`
      SELECT COALESCE(SUM(total_amount), 0) as pending_amount
      FROM invoices 
      WHERE status = 'pending'
    `);

    // Get overdue amount
    const overdueResult = await pool.query(`
      SELECT COALESCE(SUM(total_amount), 0) as overdue_amount
      FROM invoices 
      WHERE status = 'overdue' OR (status = 'pending' AND due_date < CURRENT_DATE)
    `);

    // Get total invoices count
    const countResult = await pool.query(`
      SELECT COUNT(*) as total_invoices
      FROM invoices
    `);

    res.json({
      total_revenue: parseFloat(revenueResult.rows[0].total_revenue),
      pending_amount: parseFloat(pendingResult.rows[0].pending_amount),
      overdue_amount: parseFloat(overdueResult.rows[0].overdue_amount),
      total_invoices: parseInt(countResult.rows[0].total_invoices)
    });
  } catch (error) {
    console.error('Error fetching billing stats:', error);
    res.status(500).json({ error: 'Failed to fetch billing statistics' });
  }
});

// Create invoice for organization
router.post('/organizations/:organizationId/invoices', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      billing_period_start, 
      billing_period_end,
      due_date,
      tax_rate = 0,
      notes = ''
    } = req.body;

    // Get organization details with subscription plan pricing
    const orgResult = await pool.query(`
      SELECT
        o.name,
        o.subscription_plan,
        o.billing_cycle,
        o.max_users,
        sp.monthly_price,
        sp.annual_price,
        sp.per_user_fee,
        sp.display_name as plan_display_name
      FROM organizations o
      LEFT JOIN subscription_plans sp ON o.subscription_plan = sp.name
      WHERE o.id = $1
    `, [organizationId]);

    if (orgResult.rows.length === 0) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const org = orgResult.rows[0];

    // Get user count for the billing period
    const userCountResult = await pool.query(`
      SELECT COUNT(DISTINCT id) as active_users
      FROM users 
      WHERE organization_id = $1 
      AND status = 'active'
      AND created_at <= $2
    `, [organizationId, billing_period_end]);

    const activeUsers = parseInt(userCountResult.rows[0].active_users);

    // Calculate billing based on subscription plan
    const isAnnual = org.billing_cycle === 'annual';
    const planPrice = parseFloat(isAnnual ? org.annual_price : org.monthly_price) || 0;
    const perUserFee = parseFloat(org.per_user_fee) || 0;

    // Calculate total: base plan price + (per-user fee * active users)
    const planCost = planPrice;
    const userCost = activeUsers * perUserFee;
    const subtotal = planCost + userCost;
    const taxAmount = subtotal * (tax_rate / 100);
    const totalAmount = subtotal + taxAmount;

    // Generate invoice number
    const invoiceNumber = `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`;

    // Create invoice
    const invoiceResult = await pool.query(`
      INSERT INTO invoices (
        organization_id,
        invoice_number,
        billing_period_start,
        billing_period_end,
        due_date,
        subtotal,
        tax_rate,
        tax_amount,
        total_amount,
        status,
        notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 'pending', $10)
      RETURNING *
    `, [
      organizationId,
      invoiceNumber,
      billing_period_start,
      billing_period_end,
      due_date,
      subtotal,
      tax_rate,
      taxAmount,
      totalAmount,
      notes
    ]);

    const invoice = invoiceResult.rows[0];

    // Create invoice line items using the actual table schema
    const lineItems = [];

    // For now, create a single line item representing the organization's subscription
    // The actual schema expects location-based line items, but we'll use a general approach
    await pool.query(`
      INSERT INTO invoice_line_items (
        invoice_id,
        location_name,
        user_count,
        unit_price,
        line_total
      ) VALUES ($1, $2, $3, $4, $5)
    `, [
      invoice.id,
      `${org.name} - ${org.plan_display_name || org.subscription_plan} Plan`,
      activeUsers,
      subtotal / Math.max(activeUsers, 1), // Calculate effective per-user rate
      subtotal
    ]);

    lineItems.push({
      location_name: `${org.name} - ${org.plan_display_name || org.subscription_plan} Plan`,
      user_count: activeUsers,
      unit_price: subtotal / Math.max(activeUsers, 1),
      line_total: subtotal,
      breakdown: [
        ...(planCost > 0 ? [{
          description: `${org.plan_display_name || org.subscription_plan} - ${isAnnual ? 'Annual' : 'Monthly'} Plan`,
          amount: planCost
        }] : []),
        ...(userCost > 0 ? [{
          description: `Per-user fee (${activeUsers} users × $${perUserFee})`,
          amount: userCost
        }] : [])
      ]
    });

    res.status(201).json({
      ...invoice,
      organization_name: org.name,
      line_items: lineItems
    });
  } catch (error) {
    console.error('Error creating invoice:', error);
    res.status(500).json({ error: 'Failed to create invoice' });
  }
});

module.exports = router;
