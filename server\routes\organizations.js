const express = require('express');
const { body, validationResult } = require('express-validator');
const { pool } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const bcrypt = require('bcryptjs');

const router = express.Router();

// Test endpoint (no auth required)
router.get('/test', async (req, res) => {
  try {
    res.json({ 
      message: 'Organizations API is working!',
      timestamp: new Date().toISOString(),
      database: 'connected'
    });
  } catch (error) {
    console.error('Test endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all organizations (Super Admin only)
router.get('/', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { search = '', status = '' } = req.query;

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereClause += ` AND (name ILIKE ${paramCount} OR email ILIKE ${paramCount})`;
      params.push(`%${search}%`);
    }

    if (status) {
      paramCount++;
      whereClause += ` AND status = ${paramCount}`;
      params.push(status);
    }

    // Get all organizations with user counts
    const result = await pool.query(
      `SELECT o.id, o.name, o.email, o.phone, o.address_line1 as address, o.contact_person, o.contact_email,
              o.subscription_plan, o.status, o.created_at, o.updated_at, o.max_users, o.max_locations,
              o.country, o.city, o.timezone,
              COALESCE(u.user_count, 0) as user_count,
              COALESCE(l.location_count, 0) as location_count,
              CASE
                WHEN o.subscription_plan = 'basic' THEN 29.99
                WHEN o.subscription_plan = 'standard' THEN 59.99
                WHEN o.subscription_plan = 'premium' THEN 99.99
                WHEN o.subscription_plan = 'enterprise' THEN 199.99
                ELSE 0
              END as monthly_revenue,
              CASE
                WHEN EXTRACT(DAY FROM CURRENT_DATE) <= 5 THEN 'paid'
                WHEN EXTRACT(DAY FROM CURRENT_DATE) <= 15 THEN 'pending'
                ELSE 'overdue'
              END as billing_status
       FROM organizations o
       LEFT JOIN (
         SELECT organization_id, COUNT(*) as user_count
         FROM users
         WHERE status = 'active'
         GROUP BY organization_id
       ) u ON o.id = u.organization_id
       LEFT JOIN (
         SELECT organization_id, COUNT(*) as location_count
         FROM locations
         WHERE status = 'active'
         GROUP BY organization_id
       ) l ON o.id = l.organization_id
       ${whereClause}
       ORDER BY o.created_at DESC`,
      params
    );

    res.json({
      organizations: result.rows,
      pagination: null
    });

  } catch (error) {
    console.error('Get organizations error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get organization by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { role, organization_id } = req.user;

    // Super admin can access any organization
    // Admin can only access their own organization
    if (role !== 'super_admin' && organization_id !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const result = await pool.query(
      `SELECT * FROM organizations WHERE id = $1`,
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    res.json(result.rows[0]);

  } catch (error) {
    console.error('Get organization error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get organization statistics
router.get('/:id/stats', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { role, organization_id } = req.user;

    // Super admin can access any organization
    // Admin can only access their own organization
    if (role !== 'super_admin' && organization_id !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get current date for monthly calculations
    const currentDate = new Date();
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

    // Fetch all statistics in parallel
    const [employeeStats, locationStats, assignmentStats, hoursStats, recentActivity] = await Promise.all([
      // Total employees
      pool.query(
        'SELECT COUNT(*) as total FROM users WHERE organization_id = $1 AND status = $2',
        [id, 'active']
      ),

      // Total locations
      pool.query(
        'SELECT COUNT(*) as total FROM locations WHERE organization_id = $1 AND status = $2',
        [id, 'active']
      ),

      // Active assignments (current month)
      pool.query(
        `SELECT COUNT(*) as total FROM assignments
         WHERE organization_id = $1 AND status = $2 AND created_at >= $3`,
        [id, 'active', firstDayOfMonth]
      ),

      // Monthly hours (sum of assignment hours for current month)
      pool.query(
        `SELECT COALESCE(SUM(
           EXTRACT(EPOCH FROM (shift_end - shift_start)) / 3600
         ), 0) as total_hours
         FROM assignments
         WHERE organization_id = $1
         AND shift_start >= $2
         AND shift_end IS NOT NULL
         AND status IN ('active', 'completed')`,
        [id, firstDayOfMonth]
      ),

      // Recent activity (last 5 activities)
      pool.query(
        `(SELECT 'assignment' as type,
                CONCAT('New assignment created for ', l.name) as message,
                a.created_at as timestamp
         FROM assignments a
         JOIN locations l ON a.location_id = l.id
         WHERE a.organization_id = $1
         ORDER BY a.created_at DESC
         LIMIT 2)
         UNION ALL
         (SELECT 'employee' as type,
                CONCAT(u.first_name, ' ', u.last_name, ' updated location') as message,
                ul.timestamp
         FROM user_locations ul
         JOIN users u ON ul.user_id = u.id
         WHERE u.organization_id = $1
         ORDER BY ul.timestamp DESC
         LIMIT 2)
         UNION ALL
         (SELECT 'incident' as type,
                CONCAT('Safety incident reported: ', title) as message,
                created_at as timestamp
         FROM incidents
         WHERE organization_id = $1
         ORDER BY created_at DESC
         LIMIT 1)
         ORDER BY timestamp DESC
         LIMIT 5`,
        [id]
      )
    ]);

    // Format the response
    const stats = {
      totalEmployees: parseInt(employeeStats.rows[0].total) || 0,
      totalLocations: parseInt(locationStats.rows[0].total) || 0,
      activeAssignments: parseInt(assignmentStats.rows[0].total) || 0,
      monthlyHours: Math.round(parseFloat(hoursStats.rows[0].total_hours) || 0)
    };

    // Format recent activity
    const activities = recentActivity.rows.map((activity, index) => {
      const timeDiff = new Date() - new Date(activity.timestamp);
      const hours = Math.floor(timeDiff / (1000 * 60 * 60));
      const days = Math.floor(hours / 24);

      let timeString;
      if (days > 0) {
        timeString = `${days} day${days > 1 ? 's' : ''} ago`;
      } else if (hours > 0) {
        timeString = `${hours} hour${hours > 1 ? 's' : ''} ago`;
      } else {
        timeString = 'Just now';
      }

      return {
        id: index + 1,
        type: activity.type,
        message: activity.message,
        time: timeString
      };
    });

    res.json({
      stats,
      recentActivity: activities
    });

  } catch (error) {
    console.error('Error fetching organization stats:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update organization branding
router.put('/:id/branding', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { role, organization_id } = req.user;

    // Super admin can access any organization
    // Admin can only access their own organization
    if (role !== 'super_admin' && organization_id !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const {
      primaryColor,
      secondaryColor,
      organizationName,
      tagline,
      logo,
      favicon
    } = req.body;

    // Create branding object
    const brandingData = {
      primaryColor: primaryColor || '#3b82f6',
      secondaryColor: secondaryColor || '#10b981',
      organizationName: organizationName || '',
      tagline: tagline || '',
      logo: logo || null,
      favicon: favicon || null,
      updatedAt: new Date().toISOString()
    };

    // Update organization branding
    const result = await pool.query(
      `UPDATE organizations
       SET branding = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2
       RETURNING id, name, branding`,
      [JSON.stringify(brandingData), id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    res.json({
      message: 'Branding updated successfully',
      organization: result.rows[0]
    });

  } catch (error) {
    console.error('Error updating organization branding:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new organization
router.post('/', [
  authenticateToken,
  requireRole(['super_admin']),
  // Basic Organization Information
  body('name').isLength({ min: 1 }).trim(),
  body('legal_name').optional().isLength({ min: 0 }).trim(),
  body('business_type').optional().isIn(['corporation', 'llc', 'partnership', 'sole_proprietorship', 'non_profit', '']),
  body('industry').optional().isLength({ min: 0 }).trim(),
  body('description').optional().isLength({ min: 0 }).trim(),
  body('website').optional().isURL().trim(),
  // Contact Information
  body('email').isEmail().normalizeEmail(),
  body('phone').optional().isLength({ min: 0 }).trim(),
  body('fax').optional().isLength({ min: 0 }).trim(),
  // Address Information
  body('address_line1').optional().isLength({ min: 0 }).trim(),
  body('address_line2').optional().isLength({ min: 0 }).trim(),
  body('city').optional().isLength({ min: 0 }).trim(),
  body('state').optional().isLength({ min: 0 }).trim(),
  body('postal_code').optional().isLength({ min: 0 }).trim(),
  body('country').optional().isLength({ min: 0 }).trim(),
  // Primary Contact Person
  body('contact_person').isLength({ min: 1 }).trim(),
  body('contact_email').isEmail().normalizeEmail(),
  body('contact_phone').optional().isLength({ min: 0 }).trim(),
  body('contact_title').optional().isLength({ min: 0 }).trim(),
  // Business Registration Details
  body('tax_id').optional().isLength({ min: 0 }).trim(),
  body('registration_number').optional().isLength({ min: 0 }).trim(),
  body('registration_date').optional().isISO8601().toDate(),
  // Subscription & Plan Details
  body('subscription_plan').optional().isIn(['basic', 'standard', 'premium', 'enterprise']),
  body('subscription_status').optional().isIn(['active', 'trial', 'suspended', 'cancelled']),
  body('billing_cycle').optional().isIn(['monthly', 'quarterly', 'annual']),
  body('payment_method').optional().isIn(['invoice', 'credit_card', 'bank_transfer', 'other']),
  body('subscription_start_date').optional().isISO8601().toDate(),
  body('subscription_end_date').optional().isISO8601().toDate(),
  // Service Limits
  body('max_users').optional().isInt({ min: 1 }).toInt(),
  body('max_locations').optional().isInt({ min: 1 }).toInt(),
  body('per_user_fee').optional().isFloat({ min: 0 }).toFloat(),
  body('max_monthly_tracked_hours').optional().isInt({ min: 1 }).toInt(),
  body('max_storage_gb').optional().isInt({ min: 1 }).toInt(),
  body('max_api_calls_per_hour').optional().isInt({ min: 1 }).toInt(),
  // Organization Settings
  body('timezone').optional().isLength({ min: 1 }).trim(),
  body('date_format').optional().isIn(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']),
  body('time_format').optional().isIn(['12h', '24h']),
  body('currency').optional().isLength({ min: 3, max: 3 }).trim(),
  body('locale').optional().isLength({ min: 5, max: 5 }).trim(),
  // Compliance & Security
  body('security_level').optional().isIn(['basic', 'standard', 'high', 'enterprise']),
  body('two_factor_required').optional().isBoolean().toBoolean(),
  // Organization Status
  body('status').optional().isIn(['active', 'inactive', 'suspended', 'pending_approval']),
  body('approval_status').optional().isIn(['pending', 'approved', 'rejected']),
  // Metadata
  body('logo_url').optional().isURL().trim(),
  body('notes').optional().isLength({ min: 0 }).trim(),
  body('internal_notes').optional().isLength({ min: 0 }).trim(),
  body('tags').optional().isLength({ min: 0 }).trim(),
  // Admin credentials validation
  body('adminUsername').isLength({ min: 3 }).trim(),
  body('adminPassword').isLength({ min: 6 }),
  body('adminFirstName').isLength({ min: 1 }).trim(),
  body('adminLastName').isLength({ min: 1 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      // Basic Organization Information
      name, legal_name, business_type, industry, description, website,
      // Contact Information
      email, phone, fax,
      // Address Information
      address_line1, address_line2, city, state, postal_code, country,
      // Primary Contact Person
      contact_person, contact_email, contact_phone, contact_title,
      // Business Registration Details
      tax_id, registration_number, registration_date,
      // Billing Information
      billing_address_line1, billing_address_line2, billing_city, billing_state,
      billing_postal_code, billing_country, billing_contact_name, billing_contact_email, billing_contact_phone,
      // Subscription & Plan Details
      subscription_plan, subscription_status, subscription_start_date, subscription_end_date,
      billing_cycle, payment_method,
      // Service Limits
      max_users, max_locations, max_monthly_tracked_hours, max_storage_gb, max_api_calls_per_hour, per_user_fee,
      // Organization Settings
      timezone, date_format, time_format, currency, locale,
      // Compliance & Security
      security_level, two_factor_required,
      // Organization Status
      status, approval_status,
      // Metadata
      logo_url, notes, internal_notes, tags,
      // Admin credentials
      adminUsername, adminPassword, adminFirstName, adminLastName
    } = req.body;

    // Hash the admin password
    const hashedPassword = await bcrypt.hash(adminPassword, 10);

    // Use transaction to create organization and admin user
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Process tags array
      const tagsArray = tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0) : [];

      // Create organization with all fields
      const orgResult = await client.query(
        `INSERT INTO organizations (
          name, legal_name, business_type, industry, description, website,
          email, phone, fax,
          address_line1, address_line2, city, state, postal_code, country,
          contact_person, contact_email, contact_phone, contact_title,
          tax_id, registration_number, registration_date,
          billing_address_line1, billing_address_line2, billing_city, billing_state,
          billing_postal_code, billing_country, billing_contact_name, billing_contact_email, billing_contact_phone,
          subscription_plan, subscription_status, subscription_start_date, subscription_end_date,
          billing_cycle, payment_method,
          max_users, max_locations, max_monthly_tracked_hours, max_storage_gb, max_api_calls_per_hour, per_user_fee,
          timezone, date_format, time_format, currency, locale,
          security_level, two_factor_required,
          status, approval_status,
          logo_url, notes, internal_notes, tags,
          admin_username, admin_password_hash, admin_first_name, admin_last_name
        ) VALUES (
          $1, $2, $3, $4, $5, $6,
          $7, $8, $9,
          $10, $11, $12, $13, $14, $15,
          $16, $17, $18, $19,
          $20, $21, $22,
          $23, $24, $25, $26,
          $27, $28, $29, $30, $31,
          $32, $33, $34, $35,
          $36, $37,
          $38, $39, $40, $41, $42,
          $43, $44, $45, $46, $47,
          $48, $49,
          $50, $51,
          $52, $53, $54, $55,
          $56, $57, $58, $59
        ) RETURNING *`,
        [
          // Basic Organization Information
          name, legal_name, business_type, industry, description, website,
          // Contact Information
          email, phone, fax,
          // Address Information
          address_line1, address_line2, city, state, postal_code, country || 'United States',
          // Primary Contact Person
          contact_person, contact_email, contact_phone, contact_title,
          // Business Registration Details
          tax_id, registration_number, registration_date,
          // Billing Information
          billing_address_line1, billing_address_line2, billing_city, billing_state,
          billing_postal_code, billing_country, billing_contact_name, billing_contact_email, billing_contact_phone,
          // Subscription & Plan Details
          subscription_plan || 'basic', subscription_status || 'active', subscription_start_date, subscription_end_date,
          billing_cycle || 'monthly', payment_method || 'invoice',
          // Service Limits
          max_users || 100, max_locations || 10, max_monthly_tracked_hours || 5000, max_storage_gb || 10, max_api_calls_per_hour || 1000, per_user_fee || 5.00,
          // Organization Settings
          timezone || 'America/New_York', date_format || 'MM/DD/YYYY', time_format || '12h', currency || 'USD', locale || 'en_US',
          // Compliance & Security
          security_level || 'standard', two_factor_required || false,
          // Organization Status
          status || 'active', approval_status || 'approved',
          // Metadata
          logo_url, notes, internal_notes, tagsArray,
          // Admin credentials
          adminUsername, hashedPassword, adminFirstName, adminLastName
        ]
      );

      const organization = orgResult.rows[0];

      // Create admin user in users table
      await client.query(
        `INSERT INTO users (
          organization_id, email, password_hash, first_name, last_name,
          role, status, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())`,
        [
          organization.id,
          `${adminUsername}@${organization.name.toLowerCase().replace(/\s+/g, '')}.admin`,
          hashedPassword,
          adminFirstName,
          adminLastName,
          'admin',
          'active'
        ]
      );

      await client.query('COMMIT');

      // Remove sensitive data before sending response
      delete organization.admin_password_hash;

      res.status(201).json(organization);

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Create organization error:', error);
    if (error.code === '23505') { // Unique constraint violation
      res.status(409).json({ error: 'Email already exists' });
    } else {
      res.status(500).json({ error: 'Internal server error' });
    }
  }
});

// Update organization
router.put('/:id', [
  authenticateToken,
  requireRole(['super_admin']),
  // Basic Organization Information
  body('name').optional().isLength({ min: 1 }).trim(),
  body('legal_name').optional().isLength({ min: 0 }).trim(),
  body('business_type').optional().isIn(['corporation', 'llc', 'partnership', 'sole_proprietorship', 'non_profit', '']),
  body('industry').optional().isLength({ min: 0 }).trim(),
  body('description').optional().isLength({ min: 0 }).trim(),
  body('website').optional().isURL().trim(),
  // Contact Information
  body('email').optional().isEmail().normalizeEmail(),
  body('phone').optional().isLength({ min: 0 }).trim(),
  body('fax').optional().isLength({ min: 0 }).trim(),
  // Address Information
  body('address_line1').optional().isLength({ min: 0 }).trim(),
  body('address_line2').optional().isLength({ min: 0 }).trim(),
  body('city').optional().isLength({ min: 0 }).trim(),
  body('state').optional().isLength({ min: 0 }).trim(),
  body('postal_code').optional().isLength({ min: 0 }).trim(),
  body('country').optional().isLength({ min: 0 }).trim(),
  // Primary Contact Person
  body('contact_person').optional().isLength({ min: 0 }).trim(),
  body('contact_email').optional().isEmail().normalizeEmail(),
  body('contact_phone').optional().isLength({ min: 0 }).trim(),
  body('contact_title').optional().isLength({ min: 0 }).trim(),
  // Business Registration Details
  body('tax_id').optional().isLength({ min: 0 }).trim(),
  body('registration_number').optional().isLength({ min: 0 }).trim(),
  body('registration_date').optional().isISO8601().toDate(),
  // Subscription & Plan Details
  body('subscription_plan').optional().isIn(['basic', 'standard', 'premium', 'enterprise']),
  body('subscription_status').optional().isIn(['active', 'trial', 'suspended', 'cancelled']),
  body('billing_cycle').optional().isIn(['monthly', 'quarterly', 'annual']),
  body('payment_method').optional().isIn(['invoice', 'credit_card', 'bank_transfer', 'other']),
  body('subscription_start_date').optional().isISO8601().toDate(),
  body('subscription_end_date').optional().isISO8601().toDate(),
  // Service Limits
  body('max_users').optional().isInt({ min: 1 }).toInt(),
  body('max_locations').optional().isInt({ min: 1 }).toInt(),
  body('max_monthly_tracked_hours').optional().isInt({ min: 1 }).toInt(),
  body('max_storage_gb').optional().isInt({ min: 1 }).toInt(),
  body('max_api_calls_per_hour').optional().isInt({ min: 1 }).toInt(),
  // Organization Settings
  body('timezone').optional().isLength({ min: 1 }).trim(),
  body('date_format').optional().isIn(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']),
  body('time_format').optional().isIn(['12h', '24h']),
  body('currency').optional().isLength({ min: 3, max: 3 }).trim(),
  body('locale').optional().isLength({ min: 5, max: 5 }).trim(),
  // Compliance & Security
  body('security_level').optional().isIn(['basic', 'standard', 'high', 'enterprise']),
  body('two_factor_required').optional().isBoolean().toBoolean(),
  // Organization Status
  body('status').optional().isIn(['active', 'inactive', 'suspended', 'pending_approval']),
  body('approval_status').optional().isIn(['pending', 'approved', 'rejected']),
  // Metadata
  body('logo_url').optional().isURL().trim(),
  body('notes').optional().isLength({ min: 0 }).trim(),
  body('internal_notes').optional().isLength({ min: 0 }).trim(),
  body('tags').optional().isLength({ min: 0 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.error('Validation errors:', errors.array());
      console.error('Request body:', req.body);
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const updates = req.body;

    // Map frontend field names to database field names
    const fieldMapping = {
      // Basic Organization Information
      name: 'name',
      legal_name: 'legal_name',
      business_type: 'business_type',
      industry: 'industry',
      description: 'description',
      website: 'website',
      // Contact Information
      email: 'email',
      phone: 'phone',
      fax: 'fax',
      // Address Information
      address_line1: 'address_line1',
      address_line2: 'address_line2',
      city: 'city',
      state: 'state',
      postal_code: 'postal_code',
      country: 'country',
      // Primary Contact Person
      contact_person: 'contact_person',
      contact_email: 'contact_email',
      contact_phone: 'contact_phone',
      contact_title: 'contact_title',
      // Business Registration Details
      tax_id: 'tax_id',
      registration_number: 'registration_number',
      registration_date: 'registration_date',
      // Billing Information
      billing_address_line1: 'billing_address_line1',
      billing_address_line2: 'billing_address_line2',
      billing_city: 'billing_city',
      billing_state: 'billing_state',
      billing_postal_code: 'billing_postal_code',
      billing_country: 'billing_country',
      billing_contact_name: 'billing_contact_name',
      billing_contact_email: 'billing_contact_email',
      billing_contact_phone: 'billing_contact_phone',
      // Subscription & Plan Details
      subscription_plan: 'subscription_plan',
      subscription_status: 'subscription_status',
      subscription_start_date: 'subscription_start_date',
      subscription_end_date: 'subscription_end_date',
      billing_cycle: 'billing_cycle',
      payment_method: 'payment_method',
      // Service Limits
      max_users: 'max_users',
      max_locations: 'max_locations',
      max_monthly_tracked_hours: 'max_monthly_tracked_hours',
      max_storage_gb: 'max_storage_gb',
      max_api_calls_per_hour: 'max_api_calls_per_hour',
      // Organization Settings
      timezone: 'timezone',
      date_format: 'date_format',
      time_format: 'time_format',
      currency: 'currency',
      locale: 'locale',
      // Compliance & Security
      security_level: 'security_level',
      two_factor_required: 'two_factor_required',
      // Organization Status
      status: 'status',
      approval_status: 'approval_status',
      // Metadata
      logo_url: 'logo_url',
      notes: 'notes',
      internal_notes: 'internal_notes',
      tags: 'tags'
    };

    // Build dynamic query
    const setClause = [];
    const params = [];
    let paramCount = 0;

    Object.keys(updates).forEach(key => {
      if (updates[key] !== undefined && fieldMapping[key]) {
        paramCount++;
        setClause.push(`${fieldMapping[key]} = $${paramCount}`);

        // Special handling for tags field
        if (key === 'tags') {
          const tagsArray = updates[key] ? updates[key].split(',').map(tag => tag.trim()).filter(tag => tag.length > 0) : [];
          params.push(tagsArray);
        } else {
          params.push(updates[key]);
        }
      }
    });

    if (setClause.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    params.push(id);
    const result = await pool.query(
      `UPDATE organizations SET ${setClause.join(', ')}, updated_at = CURRENT_TIMESTAMP
       WHERE id = $${paramCount + 1}
       RETURNING *`,
      params
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    res.json(result.rows[0]);

  } catch (error) {
    console.error('Update organization error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete organization
router.delete('/:id', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      `DELETE FROM organizations WHERE id = $1 RETURNING id`,
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    res.json({ message: 'Organization deleted successfully' });

  } catch (error) {
    console.error('Delete organization error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router; 