import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CreditCard,
  Plus,
  Search,
  Filter,
  Download,
  DollarSign,
  Calendar,
  FileText,
  Eye,
  Send,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { billingAPI, organizationAPI } from '../../services/api';

const BillingManagement = () => {
  const navigate = useNavigate();
  const [invoices, setInvoices] = useState([]);
  const [filteredInvoices, setFilteredInvoices] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [showNewInvoiceModal, setShowNewInvoiceModal] = useState(false);
  const [showEditInvoiceModal, setShowEditInvoiceModal] = useState(false);
  const [showViewInvoiceModal, setShowViewInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [billingStats, setBillingStats] = useState({
    total_revenue: 0,
    pending_amount: 0,
    overdue_amount: 0,
    total_invoices: 0
  });
  const [organizations, setOrganizations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [createInvoiceData, setCreateInvoiceData] = useState({
    organizationId: '',
    billing_period_start: '',
    billing_period_end: '',
    due_date: '',
    tax_rate: 0,
    notes: ''
  });
  const [editInvoiceData, setEditInvoiceData] = useState({
    status: '',
    due_date: '',
    notes: '',
    payment_date: '',
    payment_method: ''
  });

  // Load billing data from API
  const fetchBillingData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch billing statistics
      const statsResponse = await billingAPI.getBillingStats();
      setBillingStats(statsResponse.data);

      // Fetch all invoices
      const invoicesResponse = await billingAPI.getAllInvoices({
        status: statusFilter === 'all' ? undefined : statusFilter,
        search: searchTerm,
        limit: 50
      });
      setInvoices(invoicesResponse.data.invoices || []);

      // Fetch organizations for the create invoice modal
      const orgsResponse = await organizationAPI.getOrganizations();
      setOrganizations(orgsResponse.data.organizations || []);

    } catch (err) {
      console.error('Error fetching billing data:', err);
      setError('Failed to load billing data');
      // Set empty data on error
      setInvoices([]);
      setBillingStats({
        total_revenue: 0,
        pending_amount: 0,
        overdue_amount: 0,
        total_invoices: 0
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBillingData();
  }, [statusFilter, searchTerm]);

  // Handle create invoice
  const handleCreateInvoice = async (e) => {
    e.preventDefault();
    try {
      await billingAPI.createInvoice(createInvoiceData.organizationId, createInvoiceData);
      setShowNewInvoiceModal(false);
      setCreateInvoiceData({
        organizationId: '',
        billing_period_start: '',
        billing_period_end: '',
        due_date: '',
        tax_rate: 0,
        notes: ''
      });
      // Refresh data
      fetchBillingData();
    } catch (err) {
      console.error('Error creating invoice:', err);
      setError('Failed to create invoice');
    }
  };

  // Handle view invoice
  const handleViewInvoice = async (invoice) => {
    try {
      const response = await billingAPI.getInvoiceDetails(invoice.id);
      setSelectedInvoice(response.data);
      setShowViewInvoiceModal(true);
    } catch (err) {
      console.error('Error fetching invoice details:', err);
      setError('Failed to load invoice details');
    }
  };

  // Handle edit invoice
  const handleEditInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setEditInvoiceData({
      status: invoice.status,
      due_date: invoice.due_date ? invoice.due_date.split('T')[0] : '',
      notes: invoice.notes || '',
      payment_date: invoice.payment_date ? invoice.payment_date.split('T')[0] : '',
      payment_method: invoice.payment_method || ''
    });
    setShowEditInvoiceModal(true);
  };

  // Handle update invoice
  const handleUpdateInvoice = async (e) => {
    e.preventDefault();
    try {
      await billingAPI.updateInvoice(selectedInvoice.id, editInvoiceData);
      setShowEditInvoiceModal(false);
      setSelectedInvoice(null);
      // Refresh data
      fetchBillingData();
    } catch (err) {
      console.error('Error updating invoice:', err);
      setError('Failed to update invoice');
    }
  };

  // Handle download invoice
  const handleDownloadInvoice = async (invoice) => {
    try {
      const response = await billingAPI.downloadInvoice(invoice.id);
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${invoice.invoice_number}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading invoice:', err);
      setError('Failed to download invoice');
    }
  };

  // Handle send invoice email
  const handleSendInvoiceEmail = async (invoice) => {
    try {
      await billingAPI.sendInvoiceEmail(invoice.id, {
        to: invoice.organization_email || '<EMAIL>',
        subject: `Invoice ${invoice.invoice_number} - Payment Reminder`,
        message: `Please find attached your invoice ${invoice.invoice_number} for the amount of $${parseFloat(invoice.total_amount).toFixed(2)}.`
      });
      alert('Invoice email sent successfully!');
    } catch (err) {
      console.error('Error sending invoice email:', err);
      setError('Failed to send invoice email');
    }
  };

  useEffect(() => {
    let filtered = invoices;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(invoice =>
        (invoice.organization_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (invoice.invoice_number || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (invoice.notes || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(invoice => invoice.status === statusFilter);
    }

    setFilteredInvoices(filtered);
  }, [invoices, searchTerm, statusFilter]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'paid': return <CheckCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'overdue': return <AlertTriangle className="h-4 w-4" />;
      case 'draft': return <FileText className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  // Use billing stats from API instead of calculating from invoices
  const getTotalRevenue = () => {
    return billingStats.total_revenue || 0;
  };

  const getPendingAmount = () => {
    return billingStats.pending_amount || 0;
  };

  const getOverdueAmount = () => {
    return billingStats.overdue_amount || 0;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="space-y-6 p-6">
        {/* Enhanced Header */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="flex items-center justify-center h-12 w-12 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg">
              <CreditCard className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Billing & Invoicing</h1>
              <p className="text-gray-600 font-medium">Manage billing, invoices, and payment tracking</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 flex items-center gap-2 font-medium"
            >
              <Filter className="h-4 w-4" />
              Filters
            </button>
            <button
              onClick={() => setShowNewInvoiceModal(true)}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg shadow-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 flex items-center gap-2 font-medium"
            >
              <Plus className="h-4 w-4" />
              Create Invoice
            </button>
          </div>
        </div>

        {/* Enhanced Revenue Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white shadow-lg rounded-xl border border-gray-200 overflow-hidden">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-12 w-12 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg">
                    <DollarSign className="h-6 w-6" />
                  </div>
                </div>
                <div className="ml-4 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                    <dd className="text-2xl font-bold text-gray-900">${getTotalRevenue().toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 overflow-hidden">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-12 w-12 rounded-xl bg-gradient-to-r from-yellow-500 to-orange-600 text-white shadow-lg">
                    <Clock className="h-6 w-6" />
                  </div>
                </div>
                <div className="ml-4 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pending Payments</dt>
                    <dd className="text-2xl font-bold text-gray-900">${getPendingAmount().toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 overflow-hidden">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-12 w-12 rounded-xl bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg">
                    <AlertTriangle className="h-6 w-6" />
                  </div>
                </div>
                <div className="ml-4 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Overdue Amount</dt>
                    <dd className="text-2xl font-bold text-gray-900">${getOverdueAmount().toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 overflow-hidden">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-12 w-12 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg">
                    <FileText className="h-6 w-6" />
                  </div>
                </div>
                <div className="ml-4 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Invoices</dt>
                    <dd className="text-2xl font-bold text-gray-900">{billingStats.total_invoices || 0}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Search and Filters */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search invoices, organizations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-3 w-full border border-gray-300 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400"
                />
              </div>
            </div>

            {showFilters && (
              <div className="flex gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400"
              >
                <option value="all">All Status</option>
                <option value="paid">Paid</option>
                <option value="pending">Pending</option>
                <option value="overdue">Overdue</option>
                <option value="draft">Draft</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Invoices Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoice ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Organization
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Issue Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Due Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                    Loading invoices...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center text-red-500">
                    {error}
                  </td>
                </tr>
              ) : filteredInvoices.length === 0 ? (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                    No invoices found
                  </td>
                </tr>
              ) : (
                filteredInvoices.map((invoice) => (
                  <tr key={invoice.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{invoice.invoice_number}</div>
                      <div className="text-sm text-gray-500">
                        {invoice.billing_period_start && invoice.billing_period_end ?
                          `${new Date(invoice.billing_period_start).toLocaleDateString()} - ${new Date(invoice.billing_period_end).toLocaleDateString()}` :
                          'N/A'
                        }
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{invoice.organization_name}</div>
                      <div className="text-sm text-gray-500">{invoice.subscription_plan} plan</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-semibold text-gray-900">
                        ${parseFloat(invoice.total_amount || 0).toLocaleString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                        {getStatusIcon(invoice.status)}
                        {invoice.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {invoice.created_at ? new Date(invoice.created_at).toLocaleDateString() : 'N/A'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {invoice.due_date ? new Date(invoice.due_date).toLocaleDateString() : 'N/A'}
                      </div>
                    </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewInvoice(invoice)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                        title="View Invoice"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleEditInvoice(invoice)}
                        className="text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50"
                        title="Edit Invoice"
                      >
                        <FileText className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDownloadInvoice(invoice)}
                        className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                        title="Download PDF"
                      >
                        <Download className="h-4 w-4" />
                      </button>
                      {(invoice.status === 'pending' || invoice.status === 'overdue') && (
                        <button
                          onClick={() => handleSendInvoiceEmail(invoice)}
                          className="text-purple-600 hover:text-purple-900 p-1 rounded hover:bg-purple-50"
                          title="Send Reminder"
                        >
                          <Send className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Invoice Modal */}
      {showNewInvoiceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-md mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Invoice</h3>
              <form onSubmit={handleCreateInvoice}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Organization
                    </label>
                    <select
                      value={createInvoiceData.organizationId}
                      onChange={(e) => setCreateInvoiceData({...createInvoiceData, organizationId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="">Select Organization</option>
                      {organizations.map(org => (
                        <option key={org.id} value={org.id}>
                          {org.name} ({org.subscription_plan})
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Billing Period Start
                      </label>
                      <input
                        type="date"
                        value={createInvoiceData.billing_period_start}
                        onChange={(e) => setCreateInvoiceData({...createInvoiceData, billing_period_start: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Billing Period End
                      </label>
                      <input
                        type="date"
                        value={createInvoiceData.billing_period_end}
                        onChange={(e) => setCreateInvoiceData({...createInvoiceData, billing_period_end: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Due Date
                    </label>
                    <input
                      type="date"
                      value={createInvoiceData.due_date}
                      onChange={(e) => setCreateInvoiceData({...createInvoiceData, due_date: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tax Rate (%)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      value={createInvoiceData.tax_rate}
                      onChange={(e) => setCreateInvoiceData({...createInvoiceData, tax_rate: parseFloat(e.target.value) || 0})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Notes (Optional)
                    </label>
                    <textarea
                      value={createInvoiceData.notes}
                      onChange={(e) => setCreateInvoiceData({...createInvoiceData, notes: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows="3"
                      placeholder="Additional notes for this invoice..."
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    type="button"
                    onClick={() => setShowNewInvoiceModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Invoice
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Invoice Modal */}
      {showEditInvoiceModal && selectedInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-md mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Edit Invoice {selectedInvoice.invoice_number}
              </h3>
              <form onSubmit={handleUpdateInvoice}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      value={editInvoiceData.status}
                      onChange={(e) => setEditInvoiceData({...editInvoiceData, status: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="draft">Draft</option>
                      <option value="pending">Pending</option>
                      <option value="paid">Paid</option>
                      <option value="overdue">Overdue</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Due Date
                    </label>
                    <input
                      type="date"
                      value={editInvoiceData.due_date}
                      onChange={(e) => setEditInvoiceData({...editInvoiceData, due_date: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  {editInvoiceData.status === 'paid' && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Payment Date
                        </label>
                        <input
                          type="date"
                          value={editInvoiceData.payment_date}
                          onChange={(e) => setEditInvoiceData({...editInvoiceData, payment_date: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Payment Method
                        </label>
                        <select
                          value={editInvoiceData.payment_method}
                          onChange={(e) => setEditInvoiceData({...editInvoiceData, payment_method: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">Select Payment Method</option>
                          <option value="credit_card">Credit Card</option>
                          <option value="bank_transfer">Bank Transfer</option>
                          <option value="check">Check</option>
                          <option value="cash">Cash</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                    </>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Notes
                    </label>
                    <textarea
                      value={editInvoiceData.notes}
                      onChange={(e) => setEditInvoiceData({...editInvoiceData, notes: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows="3"
                      placeholder="Additional notes..."
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    type="button"
                    onClick={() => setShowEditInvoiceModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Update Invoice
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* View Invoice Modal */}
      {showViewInvoiceModal && selectedInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-8">
              {/* Invoice Header */}
              <div className="flex justify-between items-start mb-8">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">INVOICE</h1>
                  <p className="text-lg text-gray-600">#{selectedInvoice.invoice_number}</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600 mb-2">OnTheMove Admin</div>
                  <div className="text-sm text-gray-600">
                    <p>Super Admin Portal</p>
                    <p><EMAIL></p>
                    <p>+1 (555) 123-4567</p>
                  </div>
                </div>
              </div>

              {/* Invoice Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Bill To:</h3>
                  <div className="text-gray-700">
                    <p className="font-medium text-lg">{selectedInvoice.organization_name}</p>
                    {selectedInvoice.contact_person && (
                      <p>Attn: {selectedInvoice.contact_person}</p>
                    )}
                    {selectedInvoice.address_line1 && (
                      <>
                        <p>{selectedInvoice.address_line1}</p>
                        {selectedInvoice.address_line2 && <p>{selectedInvoice.address_line2}</p>}
                        <p>
                          {selectedInvoice.city && `${selectedInvoice.city}, `}
                          {selectedInvoice.state && `${selectedInvoice.state} `}
                          {selectedInvoice.postal_code}
                        </p>
                        {selectedInvoice.country && <p>{selectedInvoice.country}</p>}
                      </>
                    )}
                    {selectedInvoice.contact_email && (
                      <p className="mt-2">Email: {selectedInvoice.contact_email}</p>
                    )}
                    {selectedInvoice.contact_phone && (
                      <p>Phone: {selectedInvoice.contact_phone}</p>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Invoice Details:</h3>
                  <div className="space-y-2 text-gray-700">
                    <div className="flex justify-between">
                      <span>Invoice Date:</span>
                      <span>{new Date(selectedInvoice.invoice_date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Due Date:</span>
                      <span>{new Date(selectedInvoice.due_date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Billing Period:</span>
                      <span>
                        {new Date(selectedInvoice.billing_period_start).toLocaleDateString()} - {' '}
                        {new Date(selectedInvoice.billing_period_end).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(selectedInvoice.status)}`}>
                        {selectedInvoice.status.toUpperCase()}
                      </span>
                    </div>
                    {selectedInvoice.payment_date && (
                      <div className="flex justify-between">
                        <span>Payment Date:</span>
                        <span>{new Date(selectedInvoice.payment_date).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Line Items */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Services:</h3>
                <div className="overflow-x-auto">
                  <table className="w-full border border-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 border-b">Description</th>
                        <th className="px-4 py-3 text-right text-sm font-medium text-gray-900 border-b">Users</th>
                        <th className="px-4 py-3 text-right text-sm font-medium text-gray-900 border-b">Rate</th>
                        <th className="px-4 py-3 text-right text-sm font-medium text-gray-900 border-b">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedInvoice.line_items && selectedInvoice.line_items.map((item, index) => (
                        <tr key={index} className="border-b">
                          <td className="px-4 py-3 text-sm text-gray-900">{item.location_name}</td>
                          <td className="px-4 py-3 text-sm text-gray-900 text-right">{item.user_count}</td>
                          <td className="px-4 py-3 text-sm text-gray-900 text-right">
                            ${parseFloat(item.unit_price).toFixed(2)}
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900 text-right">
                            ${parseFloat(item.line_total).toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Totals */}
              <div className="flex justify-end mb-8">
                <div className="w-64">
                  <div className="space-y-2">
                    <div className="flex justify-between text-gray-700">
                      <span>Subtotal:</span>
                      <span>${parseFloat(selectedInvoice.subtotal).toFixed(2)}</span>
                    </div>
                    {parseFloat(selectedInvoice.tax_amount) > 0 && (
                      <div className="flex justify-between text-gray-700">
                        <span>Tax ({selectedInvoice.tax_rate}%):</span>
                        <span>${parseFloat(selectedInvoice.tax_amount).toFixed(2)}</span>
                      </div>
                    )}
                    <div className="border-t pt-2">
                      <div className="flex justify-between text-lg font-bold text-gray-900">
                        <span>Total:</span>
                        <span>${parseFloat(selectedInvoice.total_amount).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              {selectedInvoice.notes && (
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Notes:</h3>
                  <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{selectedInvoice.notes}</p>
                </div>
              )}

              {/* Footer */}
              <div className="border-t pt-6 text-center text-sm text-gray-500">
                <p>Thank you for your business!</p>
                <p className="mt-2">For questions about this invoice, <NAME_EMAIL></p>
              </div>

              {/* Modal Actions */}
              <div className="flex justify-end gap-3 mt-8 pt-6 border-t">
                <button
                  onClick={() => setShowViewInvoiceModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => handleDownloadInvoice(selectedInvoice)}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Download PDF
                </button>
                <button
                  onClick={() => {
                    setShowViewInvoiceModal(false);
                    handleEditInvoice(selectedInvoice);
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Edit Invoice
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default BillingManagement; 