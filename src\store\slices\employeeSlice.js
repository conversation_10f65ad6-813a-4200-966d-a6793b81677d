import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { userAPI } from '../../services/api';

// Async thunks
export const fetchEmployees = createAsyncThunk(
  'employees/fetchEmployees',
  async ({ organizationId, filters = {} }, { rejectWithValue }) => {
    try {
      const response = await userAPI.getUsers(organizationId, filters);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch employees');
    }
  }
);

export const createEmployee = createAsyncThunk(
  'employees/createEmployee',
  async ({ organizationId, employeeData }, { rejectWithValue }) => {
    try {
      const response = await userAPI.createUser(organizationId, employeeData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create employee');
    }
  }
);

export const updateEmployee = createAsyncThunk(
  'employees/updateEmployee',
  async ({ organizationId, employeeId, employeeData }, { rejectWithValue }) => {
    try {
      const response = await userAPI.updateUser(organizationId, employeeId, employeeData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update employee');
    }
  }
);

export const deleteEmployee = createAsyncThunk(
  'employees/deleteEmployee',
  async ({ organizationId, employeeId }, { rejectWithValue }) => {
    try {
      await userAPI.deleteUser(organizationId, employeeId);
      return employeeId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete employee');
    }
  }
);

// Initial state
const initialState = {
  employees: [],
  loading: false,
  error: null,
  filters: {
    search: '',
    department: 'all',
    role: 'all',
    status: 'all'
  }
};

// Slice
const employeeSlice = createSlice({
  name: 'employees',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    updateEmployeeStatus: (state, action) => {
      const { employeeId, status } = action.payload;
      const employee = state.employees.find(emp => emp.id === employeeId);
      if (employee) {
        employee.status = status;
        employee.updatedAt = new Date().toISOString();
      }
    }
  },
  extraReducers: (builder) => {
    // Fetch employees
    builder
      .addCase(fetchEmployees.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEmployees.fulfilled, (state, action) => {
        state.loading = false;
        state.employees = action.payload;
      })
      .addCase(fetchEmployees.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create employee
      .addCase(createEmployee.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createEmployee.fulfilled, (state, action) => {
        state.loading = false;
        state.employees.unshift(action.payload);
      })
      .addCase(createEmployee.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Update employee
      .addCase(updateEmployee.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateEmployee.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.employees.findIndex(emp => emp.id === action.payload.id);
        if (index !== -1) {
          state.employees[index] = action.payload;
        }
      })
      .addCase(updateEmployee.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Delete employee
      .addCase(deleteEmployee.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteEmployee.fulfilled, (state, action) => {
        state.loading = false;
        state.employees = state.employees.filter(emp => emp.id !== action.payload);
      })
      .addCase(deleteEmployee.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Actions
export const { clearError, setFilters, clearFilters, updateEmployeeStatus } = employeeSlice.actions;

// Selectors
export const selectEmployees = (state) => state.employees.employees;
export const selectEmployeeLoading = (state) => state.employees.loading;
export const selectEmployeeError = (state) => state.employees.error;
export const selectEmployeeFilters = (state) => state.employees.filters;

export const selectFilteredEmployees = (state) => {
  const { employees } = state.employees;
  const { search, department, role, status } = state.employees.filters;
  
  return employees.filter(employee => {
    if (search && !employee.name.toLowerCase().includes(search.toLowerCase()) &&
        !employee.email.toLowerCase().includes(search.toLowerCase()) &&
        !employee.employeeId?.toLowerCase().includes(search.toLowerCase())) {
      return false;
    }
    if (department !== 'all' && employee.department !== department) {
      return false;
    }
    if (role !== 'all' && employee.role !== role) {
      return false;
    }
    if (status !== 'all' && employee.status !== status) {
      return false;
    }
    return true;
  });
};

export const selectEmployeeById = (state, employeeId) => 
  state.employees.employees.find(emp => emp.id === employeeId);

export default employeeSlice.reducer; 