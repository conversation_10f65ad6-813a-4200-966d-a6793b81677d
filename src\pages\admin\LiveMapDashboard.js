import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  MapPin, 
  Users, 
  Navigation, 
  AlertTriangle, 
  Clock, 
  Battery, 
  Signal,
  Filter,
  Maximize2,
  CheckCircle,
  Coffee,
  Wifi,
  WifiOff,
  RefreshCw
} from 'lucide-react';
import { useTenant } from '../../tenant/TenantProvider';
import LiveMapFilters from '../../components/map/LiveMapFilters';
import EmployeeStatusCard from '../../components/map/EmployeeStatusCard';
import BrandedButton from '../../components/BrandedButton';
import webSocketService from '../../services/websocket';
import { 
  fetchEmployeeLocations, 
  updateEmployeeLocation, 
  setSelectedEmployee, 
  clearSelectedEmployee 
} from '../../store/slices/mapSlice';
import Map, { Marker, Popup, NavigationControl, GeolocateControl } from 'react-map-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

// Mapbox access token - replace with your actual token
const MAPBOX_ACCESS_TOKEN = 'pk.eyJ1Ijoib250aGVtb3ZlIiwiYSI6ImNrc2Z6Z2Z6Z2Z6Z2Z6Z2Z6Z2Z6Z2Z6Z2Z6In0.example';

const LiveMapDashboard = () => {
  const dispatch = useDispatch();
  const { tenant } = useTenant();
  const { user } = useSelector((state) => state.auth);
  const token = localStorage.getItem('token');

  // Local state for demo
  const [employees, setEmployees] = useState([]);
  const [employeeLocations, setEmployeeLocations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Local state
  const [viewState, setViewState] = useState({
    longitude: -74.006,
    latitude: 40.7128,
    zoom: 10
  });
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    department: 'all',
    search: ''
  });
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [connectionStatus, setConnectionStatus] = useState({
    isConnected: false,
    reconnectAttempts: 0
  });

  // Refs
  const mapRef = useRef();
  const refreshIntervalRef = useRef();

  // Load initial data and connect WebSocket
  useEffect(() => {
    if (tenant?.id && user?.id && token) {
      // Fetch initial employee locations
      dispatch(fetchEmployeeLocations());
      
      // Set demo data
      setEmployees([
        { id: 1, name: 'John Doe', status: 'on_duty', battery: 85, signalStrength: 90 },
        { id: 2, name: 'Jane Smith', status: 'on_break', battery: 60, signalStrength: 75 },
        { id: 3, name: 'Mike Johnson', status: 'offline', battery: 30, signalStrength: 40 }
      ]);
      
      // Connect to WebSocket for real-time updates
      webSocketService.connect(tenant.id, user.id, token);
      
      // Join tenant room after connection
      setTimeout(() => {
        webSocketService.joinTenantRoom();
      }, 1000);
    }

    return () => {
      // Cleanup WebSocket connection
      webSocketService.leaveTenantRoom();
      webSocketService.disconnect();
    };
  }, [dispatch, tenant?.id, user?.id, token]);

  // Connection status effect
  useEffect(() => {
    const updateConnectionStatus = () => {
      const status = webSocketService.getConnectionStatus();
      setConnectionStatus(status.isConnected ? 'connected' : 'disconnected');
    };

    // Update status immediately
    updateConnectionStatus();

    // Set up interval to monitor connection
    const statusInterval = setInterval(updateConnectionStatus, 5000);

    return () => clearInterval(statusInterval);
  }, []);

  // Auto-refresh functionality (fallback for when WebSocket is not available)
  useEffect(() => {
    if (autoRefresh && tenant?.id && !connectionStatus.isConnected) {
      refreshIntervalRef.current = setInterval(() => {
        dispatch(fetchEmployeeLocations({ organizationId: tenant.id }));
        setLastUpdate(new Date());
      }, 30000); // Refresh every 30 seconds
    }

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [autoRefresh, tenant?.id, dispatch, connectionStatus.isConnected]);

  // Manual refresh
  const handleRefresh = () => {
    if (tenant?.id) {
      dispatch(fetchEmployeeLocations({ organizationId: tenant.id }));
      setLastUpdate(new Date());
    }
  };

  // Filter employees based on current filters
  const filteredEmployees = employees.filter(employee => {
    const matchesStatus = filters.status === 'all' || employee.status === filters.status;
    const matchesDepartment = filters.department === 'all' || employee.department === filters.department;
    const matchesSearch = !filters.search || 
      employee.name.toLowerCase().includes(filters.search.toLowerCase()) ||
      employee.email.toLowerCase().includes(filters.search.toLowerCase());
    
    return matchesStatus && matchesDepartment && matchesSearch;
  });

  // Get status color and icon
  const getStatusConfig = (status) => {
    const configs = {
      on_duty: { color: 'bg-green-500', icon: CheckCircle, label: 'On Duty' },
      late: { color: 'bg-yellow-500', icon: Clock, label: 'Late' },
      on_break: { color: 'bg-blue-500', icon: Coffee, label: 'On Break' },
      out_of_zone: { color: 'bg-red-500', icon: AlertTriangle, label: 'Out of Zone' },
      offline: { color: 'bg-gray-500', icon: WifiOff, label: 'Offline' },
      online: { color: 'bg-green-500', icon: Wifi, label: 'Online' }
    };
    return configs[status] || configs.offline;
  };

  // Get employee location
  const getEmployeeLocation = (employeeId) => {
    return employeeLocations.find(loc => loc.employeeId === employeeId);
  };

  // Handle marker click
  const handleMarkerClick = (employee) => {
    setSelectedEmployee(employee);
  };

  // Close popup
  const handleClosePopup = () => {
    setSelectedEmployee(null);
  };

  // Get status counts
  const getStatusCounts = () => {
    const counts = {};
    employees.forEach(employee => {
      counts[employee.status] = (counts[employee.status] || 0) + 1;
    });
    return counts;
  };

  // Get connection status icon
  const getConnectionIcon = () => {
    if (connectionStatus.isConnected) {
      return <Signal className="h-4 w-4 text-green-500" />;
    } else if (connectionStatus.reconnectAttempts > 0) {
      return <Signal className="h-4 w-4 text-yellow-500" />;
    } else {
      return <Signal className="h-4 w-4 text-red-500" />;
    }
  };

  const statusCounts = getStatusCounts();

  if (!tenant?.id) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Enhanced Header */}
      <div className="bg-white shadow-lg border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl shadow-lg">
              <MapPin className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Live Employee Map
              </h1>
              <p className="text-gray-600 mt-1">
                Real-time location tracking for {tenant.name}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Connection Status */}
            <div className="flex items-center gap-2 text-sm bg-white px-4 py-2 rounded-lg shadow-sm border border-gray-200">
              {getConnectionIcon()}
              <span className={connectionStatus.isConnected ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                {connectionStatus.isConnected ? 'Live' : 'Offline'}
              </span>
            </div>

            {/* Status Summary */}
            <div className="flex items-center gap-2 text-sm text-gray-600 bg-white px-4 py-2 rounded-lg shadow-sm border border-gray-200">
              <Users className="h-5 w-5" />
              <span className="font-medium">{employees.length} employees</span>
            </div>

            {/* Last Update */}
            <div className="flex items-center gap-2 text-sm text-gray-600 bg-white px-4 py-2 rounded-lg shadow-sm border border-gray-200">
              <Clock className="h-5 w-5" />
              <span>Last update: {lastUpdate.toLocaleTimeString()}</span>
            </div>

            {/* Refresh Button */}
            <BrandedButton
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center gap-2 px-6 py-3 shadow-lg"
            >
              <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </BrandedButton>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-6 py-3 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 transition-all duration-200 hover:border-gray-400"
            >
              <Filter className="h-5 w-5" />
              Filters
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <LiveMapFilters
              filters={filters}
              onFiltersChange={setFilters}
              employees={employees}
            />
          </div>
        )}
      </div>

      {/* Enhanced Status Summary Cards */}
      <div className="bg-white shadow-lg border-b border-gray-200 p-6">
        <div className="flex flex-wrap gap-6">
          {Object.entries(statusCounts).map(([status, count]) => {
            const config = getStatusConfig(status);
            const Icon = config.icon;

            return (
              <div key={status} className="flex items-center gap-3 bg-gray-50 px-6 py-4 rounded-xl shadow-sm border border-gray-200">
                <div className={`w-4 h-4 rounded-full ${config.color}`}></div>
                <Icon className="h-6 w-6 text-gray-600" />
                <span className="text-base font-medium text-gray-900">{config.label}</span>
                <span className="text-base text-gray-600 font-semibold">({count})</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Map Container */}
      <div className="flex-1 relative">
        <Map
          ref={mapRef}
          mapboxAccessToken={MAPBOX_ACCESS_TOKEN}
          initialViewState={viewState}
          onMove={evt => setViewState(evt.viewState)}
          style={{ width: '100%', height: '100%' }}
          mapStyle="mapbox://styles/mapbox/streets-v11"
        >
          {/* Navigation Controls */}
          <NavigationControl position="top-right" />
          <GeolocateControl position="top-left" />

          {/* Employee Markers */}
          {filteredEmployees.map(employee => {
            const location = getEmployeeLocation(employee.id);
            if (!location) return null;

            const statusConfig = getStatusConfig(employee.status);
            const StatusIcon = statusConfig.icon;

            return (
              <Marker
                key={employee.id}
                longitude={location.longitude}
                latitude={location.latitude}
                anchor="bottom"
                onClick={() => handleMarkerClick(employee)}
              >
                <div className="relative">
                  {/* Status indicator */}
                  <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full ${statusConfig.color} border-2 border-white shadow-sm`}>
                    <StatusIcon className="w-2 h-2 text-white mx-auto mt-0.5" />
                  </div>
                  
                  {/* Main marker */}
                  <div className="w-8 h-8 bg-white rounded-full border-2 border-gray-300 shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
                    <MapPin className="w-4 h-4 text-gray-600" />
                  </div>
                </div>
              </Marker>
            );
          })}

          {/* Employee Popup */}
          {selectedEmployee && (
            <Popup
              longitude={getEmployeeLocation(selectedEmployee.id)?.longitude || 0}
              latitude={getEmployeeLocation(selectedEmployee.id)?.latitude || 0}
              anchor="bottom"
              onClose={handleClosePopup}
              closeButton={true}
              closeOnClick={false}
              className="z-10"
            >
              <EmployeeStatusCard
                employee={selectedEmployee}
                location={getEmployeeLocation(selectedEmployee.id)}
                onClose={handleClosePopup}
              />
            </Popup>
          )}
        </Map>

        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-20">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Updating locations...</p>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="absolute top-4 left-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-20">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              <span>Error loading map data: {error}</span>
            </div>
          </div>
        )}

        {/* Connection Status */}
        {!connectionStatus.isConnected && (
          <div className="absolute top-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded z-20">
            <div className="flex items-center gap-2">
              <WifiOff className="h-4 w-4" />
              <span>Using fallback updates (WebSocket offline)</span>
            </div>
          </div>
        )}

        {/* Auto-refresh Toggle */}
        <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 z-10">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span>Auto-refresh (30s)</span>
          </label>
        </div>
      </div>
    </div>
  );
};

export default LiveMapDashboard; 