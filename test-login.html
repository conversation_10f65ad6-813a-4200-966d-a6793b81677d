<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
</head>
<body>
    <h1>Test Super Admin Login</h1>
    <button onclick="testLogin()">Test Login API</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            try {
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123',
                        loginType: 'super_admin'
                    })
                });

                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                console.log('Login result:', data);
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
                console.error('Login error:', error);
            }
        }
    </script>
</body>
</html>
