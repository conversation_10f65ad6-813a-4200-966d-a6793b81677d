import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  Shield, 
  Plus, 
  MapPin, 
  Search, 
  Filter, 
  Settings, 
  Users, 
  Clock, 
  AlertTriangle,
  Eye,
  EyeOff,
  Edit,
  Bell,
  CheckCircle,
  XCircle,
  Zap
} from 'lucide-react';
import { useTenant } from '../../tenant/TenantProvider';
import { useBranding } from '../../tenant/BrandingProvider';
import BrandedButton from '../../components/BrandedButton';
import GeofenceForm from '../../components/geofencing/GeofenceForm';
import GeofenceList from '../../components/geofencing/GeofenceList';
import GeofenceMap from '../../components/geofencing/GeofenceMap';
import GeofenceAlerts from '../../components/geofencing/GeofenceAlerts';
import { 
  fetchGeofences, 
  createGeofence, 
  updateGeofence, 
  deleteGeofence, 
  toggleGeofenceStatus 
} from '../../store/slices/geofenceSlice';

const GeofencingManagement = () => {
  const dispatch = useDispatch();
  const { tenant } = useTenant();
  const { branding } = useBranding();
  const { user } = useSelector((state) => state.auth);

  // Local state for demo purposes
  const [geofences, setGeofences] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [alerts, setAlerts] = useState([]);

  const [activeView, setActiveView] = useState('list');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedGeofence, setSelectedGeofence] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    loadGeofenceData();
  }, []);

  const loadGeofenceData = async () => {
    try {
      setLoading(true);
      // Demo data
      setGeofences([
        { id: 1, name: 'Warehouse Zone', type: 'entry', status: 'active', alerts: 2 },
        { id: 2, name: 'Restricted Area', type: 'exit', status: 'active', alerts: 0 },
        { id: 3, name: 'Safe Zone', type: 'entry', status: 'inactive', alerts: 1 }
      ]);
      setAlerts([
        { id: 1, geofenceId: 1, message: 'Employee entered restricted area', acknowledged: false },
        { id: 2, geofenceId: 3, message: 'Geofence breach detected', acknowledged: false }
      ]);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGeofence = async (geofenceData) => {
    try {
      await dispatch(createGeofence({ organizationId: tenant.id, geofence: geofenceData }));
      setShowCreateForm(false);
      loadGeofenceData();
    } catch (error) {
      console.error('Error creating geofence:', error);
    }
  };

  const handleUpdateGeofence = async (geofenceData) => {
    try {
      await dispatch(updateGeofence({ organizationId: tenant.id, geofence: geofenceData }));
      setShowCreateForm(false);
      setSelectedGeofence(null);
      loadGeofenceData();
    } catch (error) {
      console.error('Error updating geofence:', error);
    }
  };

  const handleDeleteGeofence = async (geofenceId) => {
    if (window.confirm('Are you sure you want to delete this geofence?')) {
      try {
        await dispatch(deleteGeofence({ organizationId: tenant.id, geofenceId }));
        loadGeofenceData();
      } catch (error) {
        console.error('Error deleting geofence:', error);
      }
    }
  };

  const handleEditGeofence = (geofence) => {
    setSelectedGeofence(geofence);
    setShowCreateForm(true);
  };

  const handleAcknowledgeAlert = (alertId) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  };

  const getGeofenceStats = () => {
    const stats = {
      total: geofences.length,
      active: geofences.filter(g => g.status === 'active').length,
      inactive: geofences.filter(g => g.status === 'inactive').length,
      alerts: alerts.filter(a => !a.acknowledged).length
    };
    return stats;
  };

  const stats = getGeofenceStats();

  if (!tenant?.id) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="p-8">
        {/* Enhanced Header */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-8 mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl shadow-lg">
              <Shield className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Geofencing Management
              </h1>
              <p className="text-gray-600 mt-1">
                Define safety zones and monitor employee movements for {tenant.name}
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Geofences</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.total}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-xl">
                <MapPin className="h-8 w-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Zones</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.active}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-xl">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Inactive Zones</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.inactive}</p>
              </div>
              <div className="p-3 bg-gray-100 rounded-xl">
                <EyeOff className="h-8 w-8 text-gray-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Alerts</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.alerts}</p>
              </div>
              <div className="p-3 bg-red-100 rounded-xl">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Actions Bar */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6 mb-8">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-3 flex-1">
              {/* View Mode Toggle */}
              <div className="flex items-center space-x-1 bg-gray-100 p-1 rounded-lg">
                <button
                  onClick={() => setActiveView('list')}
                  className={`px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                    activeView === 'list'
                      ? 'bg-white text-blue-600 shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  List
                </button>
                <button
                  onClick={() => setActiveView('map')}
                  className={`px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                    activeView === 'map'
                      ? 'bg-white text-blue-600 shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Map
                </button>
                <button
                  onClick={() => setActiveView('alerts')}
                  className={`px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                    activeView === 'alerts'
                      ? 'bg-white text-blue-600 shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Alerts
                </button>
              </div>

              {/* Filters */}
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              <BrandedButton
                onClick={loadGeofenceData}
                disabled={loading}
                className="flex items-center gap-2 px-6 py-3 shadow-lg"
              >
                <Zap className="h-5 w-5" />
                Refresh
              </BrandedButton>

              <BrandedButton
                onClick={() => {
                  setSelectedGeofence(null);
                  setShowCreateForm(true);
                }}
                className="flex items-center gap-2 px-6 py-3 shadow-lg"
              >
                <Plus className="h-5 w-5" />
                Add Geofence
              </BrandedButton>
            </div>
          </div>
      </div>

        {/* Main Content */}
        <div className="space-y-8">
          {/* Geofence Form Modal */}
          {showCreateForm && (
            <div className="fixed inset-0 z-50 overflow-y-auto">
              <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowCreateForm(false)} />
                <div className="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                  <GeofenceForm
                    geofence={selectedGeofence}
                    onSubmit={selectedGeofence ? handleUpdateGeofence : handleCreateGeofence}
                    onCancel={() => {
                      setShowCreateForm(false);
                      setSelectedGeofence(null);
                    }}
                  />
                </div>
              </div>
            </div>
          )}

          {/* View Content */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-200">
            {activeView === 'list' && (
              <GeofenceList
                geofences={geofences}
                loading={loading}
                onEdit={handleEditGeofence}
                onDelete={handleDeleteGeofence}
                onSelect={setSelectedGeofence}
                selectedGeofence={selectedGeofence}
                filters={{ status: filterStatus }}
              />
            )}

            {activeView === 'map' && (
              <GeofenceMap
                geofences={geofences}
                selectedGeofence={selectedGeofence}
                onGeofenceSelect={setSelectedGeofence}
                loading={loading}
              />
            )}

            {activeView === 'alerts' && (
              <GeofenceAlerts
                alerts={alerts}
                onAcknowledge={handleAcknowledgeAlert}
                loading={loading}
                filters={{ status: filterStatus }}
              />
            )}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              <span>Error: {error}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GeofencingManagement; 