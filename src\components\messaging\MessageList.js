import React from 'react';
import { User, Circle, Clock, AlertTriangle, MapPin, MessageSquare } from 'lucide-react';

const MessageList = ({ 
  conversations, 
  selectedConversationId, 
  onConversationSelect, 
  loading 
}) => {
  const formatLastMessageTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) return `${diffDays}d ago`;
    if (diffHours > 0) return `${diffHours}h ago`;
    return 'Just now';
  };

  const getOnlineStatus = (user) => {
    if (!user) return 'offline';
    
    const lastSeen = new Date(user.lastSeen);
    const now = new Date();
    const diffMs = now - lastSeen;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffMinutes < 5) return 'online';
    if (diffMinutes < 30) return 'away';
    return 'offline';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'online': return 'text-green-500';
      case 'away': return 'text-yellow-500';
      case 'offline': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'online': return 'Online';
      case 'away': return 'Away';
      case 'offline': return 'Offline';
      default: return 'Offline';
    }
  };

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mt-1"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-300" />
        <p className="text-sm">No conversations yet</p>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      {conversations.map((conversation) => {
        const isSelected = selectedConversationId === conversation.id;
        const onlineStatus = getOnlineStatus(conversation.participant);
        const hasUnread = conversation.unreadCount > 0;
        
        return (
          <div
            key={conversation.id}
            onClick={() => onConversationSelect(conversation.id)}
            className={`p-4 cursor-pointer transition-colors ${
              isSelected 
                ? 'bg-blue-50 border-r-2 border-blue-500' 
                : 'hover:bg-gray-50'
            }`}
          >
            <div className="flex items-start space-x-3">
              {/* Avatar */}
              <div className="relative flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                  {conversation.participant?.avatar ? (
                    <img 
                      src={conversation.participant.avatar} 
                      alt={conversation.participant.name}
                      className="h-10 w-10 rounded-full object-cover"
                    />
                  ) : (
                    <User className="h-5 w-5 text-gray-500" />
                  )}
                </div>
                
                {/* Online Status Indicator */}
                <div className={`absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-white ${getStatusColor(onlineStatus)}`}>
                  <Circle className="h-3 w-3 fill-current" />
                </div>
              </div>

              {/* Conversation Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-900 truncate">
                    {conversation.participant?.name || 'Unknown User'}
                  </h4>
                  <div className="flex items-center space-x-1">
                    {hasUnread && (
                      <span className="bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {conversation.unreadCount > 9 ? '9+' : conversation.unreadCount}
                      </span>
                    )}
                    <span className="text-xs text-gray-500">
                      {formatLastMessageTime(conversation.lastMessage?.timestamp)}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2 mt-1">
                  {/* Status Text */}
                  <span className={`text-xs ${getStatusColor(onlineStatus)}`}>
                    {getStatusText(onlineStatus)}
                  </span>

                  {/* Last Message Preview */}
                  {conversation.lastMessage && (
                    <p className={`text-sm truncate ${
                      hasUnread ? 'font-medium text-gray-900' : 'text-gray-500'
                    }`}>
                      {conversation.lastMessage.senderId === conversation.participant?.id ? '' : 'You: '}
                      {conversation.lastMessage.content}
                    </p>
                  )}
                </div>

                {/* Special Indicators */}
                {conversation.participant?.status === 'emergency' && (
                  <div className="flex items-center space-x-1 mt-1">
                    <AlertTriangle className="h-3 w-3 text-red-500" />
                    <span className="text-xs text-red-500">Emergency</span>
                  </div>
                )}

                {conversation.participant?.status === 'out_of_zone' && (
                  <div className="flex items-center space-x-1 mt-1">
                    <MapPin className="h-3 w-3 text-orange-500" />
                    <span className="text-xs text-orange-500">Out of Zone</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default MessageList; 