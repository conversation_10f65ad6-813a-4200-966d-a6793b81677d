import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Save,
  Palette,
  Building2,
  Upload,
  Eye,
  Settings,
  Image,
  Loader2
} from 'lucide-react';
import { useTenant } from '../../tenant/TenantProvider';
import { updateOrganizationBrandingAsync } from '../../store/slices/organizationSlice';
import { showToast } from '../../store/slices/uiSlice';

const BrandingManagement = () => {
  const { tenant } = useTenant();
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.organization);

  const [branding, setBranding] = useState({
    primaryColor: '#3b82f6',
    secondaryColor: '#10b981',
    logo: null,
    organizationName: '',
    tagline: '',
    favicon: null
  });

  // Initialize branding data from tenant
  useEffect(() => {
    if (tenant) {
      setBranding({
        primaryColor: tenant.branding?.primaryColor || '#3b82f6',
        secondaryColor: tenant.branding?.secondaryColor || '#10b981',
        logo: tenant.branding?.logo || null,
        organizationName: tenant.branding?.organizationName || tenant.name || '',
        tagline: tenant.branding?.tagline || '',
        favicon: tenant.branding?.favicon || null
      });
    }
  }, [tenant]);

  const handleColorChange = (key, value) => {
    setBranding(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleInputChange = (key, value) => {
    setBranding(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleFileUpload = (key, file) => {
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setBranding(prev => ({
          ...prev,
          [key]: e.target.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = async () => {
    if (!tenant?.id) {
      dispatch(showToast({ message: 'Organization not found', type: 'error' }));
      return;
    }

    try {
      await dispatch(updateOrganizationBrandingAsync({
        organizationId: tenant.id,
        brandingData: branding
      })).unwrap();

      dispatch(showToast({
        message: 'Branding settings saved successfully!',
        type: 'success'
      }));
    } catch (error) {
      dispatch(showToast({
        message: error || 'Error saving branding settings',
        type: 'error'
      }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-purple-500 text-white">
              <Palette className="h-6 w-6" />
            </div>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Branding Management</h1>
            <p className="mt-1 text-sm text-gray-500">
              Customize your organization's visual identity and branding
            </p>
          </div>
        </div>

        <button
          onClick={handleSave}
          disabled={isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </>
          )}
        </button>
      </div>

      {/* Organization Details */}
      <div className="bg-white shadow-lg rounded-xl border border-gray-200">
        <div className="px-6 py-6 sm:p-8">
          <div className="flex items-center mb-6">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-blue-500 text-white">
                <Building2 className="h-6 w-6" />
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-xl font-semibold text-gray-900">Organization Details</h3>
              <p className="text-sm text-gray-600">Basic information about your organization</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Organization Name
              </label>
              <input
                type="text"
                className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400"
                value={branding.organizationName}
                onChange={(e) => handleInputChange('organizationName', e.target.value)}
                placeholder="Enter organization name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tagline
              </label>
              <input
                type="text"
                className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400"
                value={branding.tagline}
                onChange={(e) => handleInputChange('tagline', e.target.value)}
                placeholder="Enter organization tagline"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Color Scheme */}
      <div className="bg-white shadow-lg rounded-xl border border-gray-200">
        <div className="px-6 py-6 sm:p-8">
          <div className="flex items-center mb-6">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-purple-500 text-white">
                <Palette className="h-6 w-6" />
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-xl font-semibold text-gray-900">Color Scheme</h3>
              <p className="text-sm text-gray-600">Define your brand colors and visual identity</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Primary Color
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="color"
                  value={branding.primaryColor}
                  onChange={(e) => handleColorChange('primaryColor', e.target.value)}
                  className="w-12 h-12 border border-gray-300 rounded-lg cursor-pointer shadow-sm"
                />
                <input
                  type="text"
                  value={branding.primaryColor}
                  onChange={(e) => handleColorChange('primaryColor', e.target.value)}
                  className="block flex-1 px-4 py-3 border border-gray-300 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400"
                  placeholder="#3b82f6"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Secondary Color
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="color"
                  value={branding.secondaryColor}
                  onChange={(e) => handleColorChange('secondaryColor', e.target.value)}
                  className="w-12 h-12 border border-gray-300 rounded-lg cursor-pointer shadow-sm"
                />
                <input
                  type="text"
                  value={branding.secondaryColor}
                  onChange={(e) => handleColorChange('secondaryColor', e.target.value)}
                  className="block flex-1 px-4 py-3 border border-gray-300 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400"
                  placeholder="#10b981"
                />
              </div>
            </div>
          </div>

          <div className="mt-8">
            <div className="flex items-center mb-4">
              <Eye className="h-5 w-5 text-gray-400 mr-2" />
              <h4 className="text-lg font-medium text-gray-900">Color Preview</h4>
            </div>
            <div className="flex space-x-4">
              <div className="text-center">
                <div
                  className="w-24 h-24 rounded-xl flex items-center justify-center text-white font-bold shadow-lg"
                  style={{ backgroundColor: branding.primaryColor }}
                >
                  Primary
                </div>
                <p className="text-xs text-gray-500 mt-2">Primary Color</p>
              </div>
              <div className="text-center">
                <div
                  className="w-24 h-24 rounded-xl flex items-center justify-center text-white font-bold shadow-lg"
                  style={{ backgroundColor: branding.secondaryColor }}
                >
                  Secondary
                </div>
                <p className="text-xs text-gray-500 mt-2">Secondary Color</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Logo & Assets */}
      <div className="bg-white shadow-lg rounded-xl border border-gray-200">
        <div className="px-6 py-6 sm:p-8">
          <div className="flex items-center mb-6">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-green-500 text-white">
                <Image className="h-6 w-6" />
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-xl font-semibold text-gray-900">Logo & Assets</h3>
              <p className="text-sm text-gray-600">Upload your organization's visual assets</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Organization Logo
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-xl hover:border-gray-400 transition-colors">
                <div className="space-y-1 text-center">
                  {branding.logo ? (
                    <div className="space-y-2">
                      <img
                        src={branding.logo}
                        alt="Organization Logo"
                        className="mx-auto h-20 w-20 object-contain rounded-lg border border-gray-200"
                      />
                      <button
                        onClick={() => setBranding(prev => ({ ...prev, logo: null }))}
                        className="text-sm text-red-600 hover:text-red-800"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <>
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                          <span>Upload a file</span>
                          <input
                            type="file"
                            className="sr-only"
                            accept="image/*"
                            onChange={(e) => handleFileUpload('logo', e.target.files[0])}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                    </>
                  )}
                </div>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Favicon
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-xl hover:border-gray-400 transition-colors">
                <div className="space-y-1 text-center">
                  {branding.favicon ? (
                    <div className="space-y-2">
                      <img
                        src={branding.favicon}
                        alt="Favicon"
                        className="mx-auto h-8 w-8 object-contain rounded border border-gray-200"
                      />
                      <button
                        onClick={() => setBranding(prev => ({ ...prev, favicon: null }))}
                        className="text-sm text-red-600 hover:text-red-800"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <>
                      <Settings className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                          <span>Upload favicon</span>
                          <input
                            type="file"
                            className="sr-only"
                            accept=".ico,.png"
                            onChange={(e) => handleFileUpload('favicon', e.target.files[0])}
                          />
                        </label>
                      </div>
                      <p className="text-xs text-gray-500">ICO or PNG, 32x32px</p>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Live Preview */}
      <div className="bg-white shadow-lg rounded-xl border border-gray-200">
        <div className="px-6 py-6 sm:p-8">
          <div className="flex items-center mb-6">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-orange-500 text-white">
                <Eye className="h-6 w-6" />
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-xl font-semibold text-gray-900">Live Preview</h3>
              <p className="text-sm text-gray-600">See how your branding will look in the application</p>
            </div>
          </div>

          <div className="bg-gray-50 border border-gray-200 rounded-xl p-6">
            <div className="flex items-center space-x-4 mb-6">
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg"
                style={{ backgroundColor: branding.primaryColor }}
              >
                {branding.organizationName.charAt(0)}
              </div>
              <div>
                <h4 className="text-lg font-semibold text-gray-900">{branding.organizationName}</h4>
                <p className="text-sm text-gray-600">{branding.tagline}</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button
                className="px-6 py-3 rounded-lg text-white font-medium shadow-lg hover:shadow-xl transition-shadow"
                style={{ backgroundColor: branding.primaryColor }}
              >
                Primary Button
              </button>
              <button
                className="px-6 py-3 rounded-lg text-white font-medium shadow-lg hover:shadow-xl transition-shadow"
                style={{ backgroundColor: branding.secondaryColor }}
              >
                Secondary Button
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrandingManagement; 