-- Migration: Add admin username and password to organizations table
-- This allows each organization to have an admin user created automatically

-- Add admin credentials columns to organizations table
ALTER TABLE organizations 
ADD COLUMN IF NOT EXISTS admin_username VARCHAR(100),
ADD COLUMN IF NOT EXISTS admin_password_hash VARCHAR(255),
ADD COLUMN IF NOT EXISTS admin_first_name VA<PERSON>HAR(100),
ADD COLUMN IF NOT EXISTS admin_last_name VA<PERSON>HAR(100);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_organizations_admin_username ON organizations(admin_username);

-- Add comments for documentation
COMMENT ON COLUMN organizations.admin_username IS 'Username for the organization admin user';
COMMENT ON COLUMN organizations.admin_password_hash IS 'Hashed password for the organization admin user';
COMMENT ON COLUMN organizations.admin_first_name IS 'First name of the organization admin user';
COMMENT ON COLUMN organizations.admin_last_name IS 'Last name of the organization admin user';
