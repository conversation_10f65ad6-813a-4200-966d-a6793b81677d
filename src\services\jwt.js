import { jwtDecode } from 'jwt-decode';

export function decodeJWT(token) {
  if (!token) return null;
  try {
    return jwtDecode(token);
  } catch (e) {
    return null;
  }
}

export function hasRole(user, role) {
  if (!user || !user.roles) return false;
  return user.roles.includes(role);
}

export function getTenantIdFromToken(token) {
  const decoded = decodeJWT(token);
  return decoded?.tenantId || null;
}

export function getUserRolesFromToken(token) {
  const decoded = decodeJWT(token);
  return decoded?.roles || [];
} 