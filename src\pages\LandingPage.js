import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  MapPin, 
  Users, 
  Calendar, 
  Shield, 
  MessageSquare, 
  BarChart3, 
  Clock, 
  CheckCircle,
  Globe,
  Building,
  Smartphone,
  Zap,
  Mail,
  Phone,
  MapPin as LocationIcon,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

const LandingPage = () => {
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });
  const [openFaq, setOpenFaq] = useState(null);

  const handleContactSubmit = (e) => {
    e.preventDefault();
    // Handle form submission here
    alert('Thank you for your message! We will get back to you soon.');
    setContactForm({ name: '', email: '', company: '', message: '' });
  };

  const handleContactChange = (e) => {
    setContactForm({
      ...contactForm,
      [e.target.name]: e.target.value
    });
  };

  const toggleFaq = (index) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const features = [
    {
      icon: <Users className="h-8 w-8 text-blue-600" />,
      title: "Employee Management",
      description: "Comprehensive employee tracking, scheduling, and management system with role-based access control."
    },
    {
      icon: <MapPin className="h-8 w-8 text-blue-600" />,
      title: "Live Map Dashboard",
      description: "Real-time location tracking and monitoring of employees with geofencing capabilities."
    },
    {
      icon: <Calendar className="h-8 w-8 text-blue-600" />,
      title: "Assignment Management",
      description: "Efficient task and location assignment system with calendar integration and scheduling."
    },
    {
      icon: <Shield className="h-8 w-8 text-blue-600" />,
      title: "Incident Management",
      description: "Track, report, and manage safety incidents with comprehensive reporting and analytics."
    },
    {
      icon: <MessageSquare className="h-8 w-8 text-blue-600" />,
      title: "Secure Messaging",
      description: "Built-in communication system for teams with real-time messaging and notifications."
    },
    {
      icon: <BarChart3 className="h-8 w-8 text-blue-600" />,
      title: "Analytics & Reporting",
      description: "Detailed reports on attendance, safety, performance, and operational metrics."
    }
  ];

  const benefits = [
    {
      icon: <Clock className="h-6 w-6 text-green-600" />,
      title: "Real-time Tracking",
      description: "Monitor employee locations and status in real-time"
    },
    {
      icon: <CheckCircle className="h-6 w-6 text-green-600" />,
      title: "Improved Safety",
      description: "Enhanced safety protocols with incident tracking"
    },
    {
      icon: <Globe className="h-6 w-6 text-green-600" />,
      title: "Multi-tenant Support",
      description: "White-label solution for multiple organizations"
    },
    {
      icon: <Smartphone className="h-6 w-6 text-green-600" />,
      title: "Mobile Responsive",
      description: "Access from any device, anywhere"
    }
  ];

  const faqs = [
    {
      question: "What is OnTheMove?",
      answer: "OnTheMove is a comprehensive, multi-tenant workforce management platform designed to help organizations efficiently manage their employees, track locations, handle assignments, and maintain safety standards."
    },
    {
      question: "How does the multi-tenant system work?",
      answer: "Our white-label solution supports multiple client organizations with isolated data, users, and custom branding. Each tenant has their own secure environment with customizable branding and features."
    },
    {
      question: "What are the different user roles?",
      answer: "The system supports multiple roles: Super Admin (system-wide access), Client Admin (organization management), Dispatcher (employee and location management), and Supervisor (assignment and incident oversight)."
    },
    {
      question: "How do I try the demo?",
      answer: "Use these demo credentials - Admin: <EMAIL> / admin123 | Super Admin: <EMAIL> / super123. These will give you full access to explore all features."
    },
    {
      question: "Is the system secure?",
      answer: "Yes, we implement enterprise-grade security with JWT authentication, role-based access control, data encryption, and tenant isolation to ensure your data is protected."
    },
    {
      question: "Can I customize the branding?",
      answer: "Absolutely! Each tenant can customize their branding including logos, colors, themes, and even custom domain names for a complete white-label experience."
    },
    {
      question: "What kind of reporting is available?",
      answer: "The system provides comprehensive reports on attendance, safety incidents, performance metrics, location analytics, and custom reports that can be exported in various formats."
    },
    {
      question: "Is mobile access supported?",
      answer: "Yes, the platform is fully responsive and works seamlessly on desktop, tablet, and mobile devices. We also offer mobile apps for field workers."
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
                <span className="text-white font-bold text-lg">O</span>
              </div>
              <span className="ml-3 text-xl font-bold text-gray-900">OnTheMove</span>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">Features</a>
              <a href="#benefits" className="text-gray-600 hover:text-gray-900 transition-colors">Benefits</a>
              <a href="#about" className="text-gray-600 hover:text-gray-900 transition-colors">About</a>
              <a href="#faq" className="text-gray-600 hover:text-gray-900 transition-colors">FAQ</a>
              <a href="#contact" className="text-gray-600 hover:text-gray-900 transition-colors">Contact</a>
            </nav>

            {/* Login Buttons */}
            <div className="flex space-x-4">
              <Link
                to="/login"
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Admin Login
              </Link>
              <Link
                to="/super-admin-login"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors"
              >
                Super Admin
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Streamline Your Workforce Management
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
              OnTheMove is a comprehensive admin dashboard for managing employees, tracking locations, 
              handling assignments, and ensuring workplace safety across multiple organizations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/login"
                className="px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
              >
                Try Demo
              </Link>
              <a
                href="#features"
                className="px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
              >
                Learn More
              </a>
            </div>
            <div className="mt-8 p-4 bg-blue-800 bg-opacity-50 rounded-lg max-w-2xl mx-auto">
              <h3 className="text-lg font-semibold mb-2">🚀 Try the Demo!</h3>
              <div className="text-sm space-y-1">
                <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                <p><strong>Super Admin:</strong> <EMAIL> / super123</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Powerful Features for Modern Workforce Management
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to manage your workforce efficiently and safely
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow">
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose OnTheMove?
            </h2>
            <p className="text-xl text-gray-600">
              Built for scalability, security, and ease of use
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">{benefit.icon}</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
              About OnTheMove
            </h2>
            <div className="text-lg text-gray-600 space-y-6">
              <p>
                OnTheMove is a comprehensive, multi-tenant workforce management platform designed 
                to help organizations efficiently manage their employees, track locations, handle 
                assignments, and maintain safety standards.
              </p>
              <p>
                Our white-label solution supports multiple client organizations with isolated data, 
                users, and custom branding, making it perfect for service providers managing 
                multiple clients or large enterprises with multiple divisions.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                <div className="text-center">
                  <Building className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900">Multi-Tenant</h3>
                  <p className="text-sm text-gray-600 mt-2">Support for multiple organizations</p>
                </div>
                <div className="text-center">
                  <Zap className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900">Real-Time</h3>
                  <p className="text-sm text-gray-600 mt-2">Live updates and notifications</p>
                </div>
                <div className="text-center">
                  <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900">Secure</h3>
                  <p className="text-sm text-gray-600 mt-2">Enterprise-grade security</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600">
              Get answers to common questions about OnTheMove
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border">
                <button
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                  onClick={() => toggleFaq(index)}
                >
                  <span className="font-semibold text-gray-900">{faq.question}</span>
                  {openFaq === index ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Get in Touch
            </h2>
            <p className="text-xl text-gray-600">
              Ready to transform your workforce management? Contact us today.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Info */}
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Contact Information</h3>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <Mail className="h-6 w-6 text-blue-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900">Email</h4>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <Phone className="h-6 w-6 text-blue-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900">Phone</h4>
                    <p className="text-gray-600">+****************</p>
                    <p className="text-gray-600">+****************</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <LocationIcon className="h-6 w-6 text-blue-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900">Address</h4>
                    <p className="text-gray-600">123 Business Avenue</p>
                    <p className="text-gray-600">Tech City, TC 12345</p>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-6 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Demo Credentials</h4>
                <div className="text-sm text-gray-700 space-y-1">
                  <p><strong>Admin Login:</strong> <EMAIL> / admin123</p>
                  <p><strong>Super Admin:</strong> <EMAIL> / super123</p>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div>
              <form onSubmit={handleContactSubmit} className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    value={contactForm.name}
                    onChange={handleContactChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Your full name"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value={contactForm.email}
                    onChange={handleContactChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                    Company Name
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={contactForm.company}
                    onChange={handleContactChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Your company name"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={5}
                    value={contactForm.message}
                    onChange={handleContactChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Tell us about your workforce management needs..."
                  />
                </div>

                <button
                  type="submit"
                  className="w-full px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                >
                  Send Message
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Logo and Description */}
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-4">
                <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                  <span className="text-white font-bold">O</span>
                </div>
                <span className="ml-2 text-xl font-bold">OnTheMove</span>
              </div>
              <p className="text-gray-400 mb-4">
                Comprehensive workforce management platform for modern organizations. 
                Track, manage, and optimize your workforce with real-time insights.
              </p>
            </div>

            {/* Features */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Features</h3>
              <ul className="space-y-2 text-gray-400">
                <li>Employee Management</li>
                <li>Live Map Tracking</li>
                <li>Assignment Scheduling</li>
                <li>Incident Management</li>
                <li>Secure Messaging</li>
                <li>Analytics & Reports</li>
              </ul>
            </div>

            {/* Access */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Access</h3>
              <div className="space-y-2">
                <Link
                  to="/login"
                  className="block text-gray-400 hover:text-white transition-colors"
                >
                  Admin Login
                </Link>
                <Link
                  to="/super-admin-login"
                  className="block text-gray-400 hover:text-white transition-colors"
                >
                  Super Admin Login
                </Link>
                <a
                  href="#contact"
                  className="block text-gray-400 hover:text-white transition-colors"
                >
                  Contact Us
                </a>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 OnTheMove. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage; 