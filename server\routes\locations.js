const express = require('express');
const { authenticateToken, requireTenantAccess } = require('../middleware/auth');
const { pool } = require('../config/database');
const router = express.Router({ mergeParams: true });

// Get all locations for an organization
router.get('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      search = '', 
      type = 'all', 
      status = 'all',
      page = 1, 
      limit = 50 
    } = req.query;

    let query = `
      SELECT 
        l.id,
        l.name,
        l.address,
        l.latitude,
        l.longitude,
        l.type,
        l.status,
        l.contact_person,
        l.contact_phone,
        l.notes,
        l.created_at,
        l.updated_at,
        COUNT(a.id) as active_assignments
      FROM locations l
      LEFT JOIN assignments a ON l.id = a.location_id AND a.status = 'active'
      WHERE l.organization_id = $1
    `;
    
    const params = [organizationId];
    let paramIndex = 2;

    // Apply filters
    if (search) {
      query += ` AND (l.name ILIKE $${paramIndex} OR l.address ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (type !== 'all') {
      query += ` AND l.type = $${paramIndex}`;
      params.push(type);
      paramIndex++;
    }

    if (status !== 'all') {
      query += ` AND l.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    query += ` 
      GROUP BY l.id, l.name, l.address, l.latitude, l.longitude, l.type, l.status, l.contact_person, l.contact_phone, l.notes, l.created_at, l.updated_at
      ORDER BY l.name
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(limit, (page - 1) * limit);

    const result = await pool.query(query, params);
    const locations = result.rows.map(location => ({
      id: location.id,
      name: location.name,
      address: location.address,
      latitude: parseFloat(location.latitude),
      longitude: parseFloat(location.longitude),
      type: location.type,
      status: location.status,
      contactPerson: location.contact_person,
      contactPhone: location.contact_phone,
      notes: location.notes,
      activeAssignments: parseInt(location.active_assignments),
      createdAt: location.created_at,
      updatedAt: location.updated_at
    }));

    res.json(locations);
  } catch (error) {
    console.error('Error fetching locations:', error);
    res.status(500).json({ error: 'Failed to fetch locations' });
  }
});

// Get location by ID
router.get('/:locationId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, locationId } = req.params;

    const result = await pool.query(`
      SELECT 
        l.*,
        COUNT(a.id) as active_assignments
      FROM locations l
      LEFT JOIN assignments a ON l.id = a.location_id AND a.status = 'active'
      WHERE l.organization_id = $1 AND l.id = $2
      GROUP BY l.id
    `, [organizationId, locationId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Location not found' });
    }

    const location = result.rows[0];
    
    // Get current assignments
    const assignmentsResult = await pool.query(`
      SELECT 
        a.id,
        a.shift_start,
        a.shift_end,
        a.status,
        a.notes,
        u.first_name,
        u.last_name,
        u.email,
        u.role
      FROM assignments a
      JOIN users u ON a.user_id = u.id
      WHERE a.location_id = $1 AND a.status = 'active'
      ORDER BY a.shift_start
    `, [locationId]);

    res.json({
      id: location.id,
      name: location.name,
      address: location.address,
      latitude: parseFloat(location.latitude),
      longitude: parseFloat(location.longitude),
      type: location.type,
      status: location.status,
      contactPerson: location.contact_person,
      contactPhone: location.contact_phone,
      notes: location.notes,
      activeAssignments: parseInt(location.active_assignments),
      createdAt: location.created_at,
      updatedAt: location.updated_at,
      assignments: assignmentsResult.rows.map(a => ({
        id: a.id,
        shiftStart: a.shift_start,
        shiftEnd: a.shift_end,
        status: a.status,
        notes: a.notes,
        employee: {
          name: `${a.first_name} ${a.last_name}`,
          email: a.email,
          role: a.role
        }
      }))
    });
  } catch (error) {
    console.error('Error fetching location:', error);
    res.status(500).json({ error: 'Failed to fetch location' });
  }
});

// Create new location
router.post('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      name, 
      address, 
      latitude, 
      longitude, 
      type = 'office', 
      contactPerson, 
      contactPhone, 
      notes 
    } = req.body;

    // Validate required fields
    if (!name || !address) {
      return res.status(400).json({ error: 'Name and address are required' });
    }

    // Create location
    const result = await pool.query(`
      INSERT INTO locations (
        organization_id, name, address, latitude, longitude, type, status,
        contact_person, contact_phone, notes
      ) VALUES ($1, $2, $3, $4, $5, $6, 'active', $7, $8, $9)
      RETURNING *
    `, [organizationId, name, address, latitude, longitude, type, contactPerson, contactPhone, notes]);

    const newLocation = result.rows[0];

    res.status(201).json({
      id: newLocation.id,
      name: newLocation.name,
      address: newLocation.address,
      latitude: parseFloat(newLocation.latitude),
      longitude: parseFloat(newLocation.longitude),
      type: newLocation.type,
      status: newLocation.status,
      contactPerson: newLocation.contact_person,
      contactPhone: newLocation.contact_phone,
      notes: newLocation.notes,
      createdAt: newLocation.created_at
    });
  } catch (error) {
    console.error('Error creating location:', error);
    res.status(500).json({ error: 'Failed to create location' });
  }
});

// Update location
router.put('/:locationId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, locationId } = req.params;
    const { name, address, latitude, longitude, type, status, contactPerson, contactPhone, notes } = req.body;

    // Check if location exists and belongs to organization
    const existingLocation = await pool.query(`
      SELECT id FROM locations WHERE id = $1 AND organization_id = $2
    `, [locationId, organizationId]);

    if (existingLocation.rows.length === 0) {
      return res.status(404).json({ error: 'Location not found' });
    }

    // Build update query dynamically
    const updates = [];
    const params = [];
    let paramIndex = 1;

    const fieldMapping = {
        name: 'name',
        address: 'address',
        latitude: 'latitude',
        longitude: 'longitude',
        type: 'type',
        status: 'status',
        contactPerson: 'contact_person',
        contactPhone: 'contact_phone',
        notes: 'notes'
    };

    for (const key in req.body) {
        if (fieldMapping[key]) {
            updates.push(`${fieldMapping[key]} = ${paramIndex}`);
            params.push(req.body[key]);
            paramIndex++;
        }
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updates.push(`updated_at = NOW()`);
    params.push(locationId, organizationId);

    const result = await pool.query(`
      UPDATE locations 
      SET ${updates.join(', ')}
      WHERE id = ${paramIndex} AND organization_id = ${paramIndex + 1}
      RETURNING *
    `, params);

    const updatedLocation = result.rows[0];

    res.json({
      id: updatedLocation.id,
      name: updatedLocation.name,
      address: updatedLocation.address,
      latitude: parseFloat(updatedLocation.latitude),
      longitude: parseFloat(updatedLocation.longitude),
      type: updatedLocation.type,
      status: updatedLocation.status,
      contactPerson: updatedLocation.contact_person,
      contactPhone: updatedLocation.contact_phone,
      notes: updatedLocation.notes,
      updatedAt: updatedLocation.updated_at
    });
  } catch (error) {
    console.error('Error updating location:', error);
    res.status(500).json({ error: 'Failed to update location' });
  }
});

// Delete location
router.delete('/:locationId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, locationId } = req.params;

    // Check for active assignments
    const activeAssignments = await pool.query(`
      SELECT COUNT(*) as count FROM assignments 
      WHERE location_id = $1 AND status = 'active'
    `, [locationId]);

    if (parseInt(activeAssignments.rows[0].count) > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete location with active assignments' 
      });
    }

    // Soft delete by setting status to inactive
    await pool.query(`
      UPDATE locations 
      SET status = 'inactive', updated_at = NOW()
      WHERE id = $1 AND organization_id = $2
    `, [locationId, organizationId]);

    res.json({ message: 'Location deactivated successfully' });
  } catch (error) {
    console.error('Error deleting location:', error);
    res.status(500).json({ error: 'Failed to delete location' });
  }
});

module.exports = router; 