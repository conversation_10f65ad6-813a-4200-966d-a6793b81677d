const bcrypt = require('bcryptjs');
const { pool } = require('../config/database');

async function fixAdminUser() {
  try {
    console.log('Fixing admin user...');
    
    // Check if admin user exists
    const existingAdmin = await pool.query(
      'SELECT id, email, role, organization_id FROM users WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (existingAdmin.rows.length === 0) {
      console.log('Admin user does not exist, creating it...');
      
      // Hash the password
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      // Create admin user associated with the first organization
      const result = await pool.query(`
        INSERT INTO users (
          organization_id,
          email,
          password_hash,
          first_name,
          last_name,
          role,
          status,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id, email, role, organization_id
      `, [
        '00000000-0000-0000-0000-000000000001', // Associate with Acme Security
        '<EMAIL>',
        hashedPassword,
        'Admin',
        'User',
        'admin',
        'active'
      ]);
      
      console.log('Admin user created successfully:', result.rows[0]);
    } else {
      const admin = existingAdmin.rows[0];
      console.log('Admin user exists:', admin);
      
      if (!admin.organization_id) {
        console.log('Admin user has no organization, updating...');
        
        // Update admin user to associate with first organization
        const result = await pool.query(`
          UPDATE users 
          SET organization_id = $1, updated_at = CURRENT_TIMESTAMP
          WHERE email = $2
          RETURNING id, email, role, organization_id
        `, [
          '00000000-0000-0000-0000-000000000001', // Associate with Acme Security
          '<EMAIL>'
        ]);
        
        console.log('Admin user updated successfully:', result.rows[0]);
      } else {
        console.log('Admin user already has organization association');
      }
    }
    
    // Verify the fix
    const verifyResult = await pool.query(
      `SELECT u.id, u.email, u.role, u.organization_id, o.name as organization_name
       FROM users u 
       LEFT JOIN organizations o ON u.organization_id = o.id 
       WHERE u.email = $1`,
      ['<EMAIL>']
    );
    
    if (verifyResult.rows.length > 0) {
      console.log('Final admin user state:', verifyResult.rows[0]);
    }
    
  } catch (error) {
    console.error('Error fixing admin user:', error);
  } finally {
    await pool.end();
  }
}

// Run the script
fixAdminUser();
