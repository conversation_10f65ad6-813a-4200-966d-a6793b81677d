-- Add location fields to organizations table
-- This migration adds country, city, and timezone fields to the organizations table

-- Add the new columns
ALTER TABLE organizations 
ADD COLUMN IF NOT EXISTS country VARCHAR(100),
ADD COLUMN IF NOT EXISTS city VARCHAR(100),
ADD COLUMN IF NOT EXISTS timezone VARCHAR(100) DEFAULT 'UTC';

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_organizations_country ON organizations(country);
CREATE INDEX IF NOT EXISTS idx_organizations_city ON organizations(city);
CREATE INDEX IF NOT EXISTS idx_organizations_timezone ON organizations(timezone);

-- Add comments for documentation
COMMENT ON COLUMN organizations.country IS 'Country where the organization is located';
COMMENT ON COLUMN organizations.city IS 'City where the organization is located';
COMMENT ON COLUMN organizations.timezone IS 'Timezone for the organization (e.g., America/New_York, Europe/London)';

-- Update existing organizations with default values if needed
UPDATE organizations 
SET 
    country = 'United States',
    city = 'New York',
    timezone = 'America/New_York'
WHERE country IS NULL OR city IS NULL;

-- Add check constraint for timezone format (basic validation)
ALTER TABLE organizations 
ADD CONSTRAINT chk_timezone_format 
CHECK (timezone ~ '^[A-Za-z_]+/[A-Za-z_]+$' OR timezone = 'UTC');
