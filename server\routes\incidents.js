const express = require('express');
const { authenticateToken, requireTenantAccess } = require('../middleware/auth');
const { pool } = require('../config/database');
const router = express.Router({ mergeParams: true });

// Get all incidents for an organization
router.get('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      search = '', 
      status = 'all', 
      severity = 'all',
      type = 'all',
      locationId = 'all',
      reporterId = 'all',
      dateFrom,
      dateTo,
      page = 1, 
      limit = 50 
    } = req.query;

    let query = `
      SELECT
        i.id,
        i.title,
        i.description,
        i.type,
        i.severity,
        i.status,
        i.latitude,
        i.longitude,
        i.attachments,
        i.created_at,
        i.updated_at,
        i.resolved_at,
        i.resolution_notes,
        l.id as location_id,
        l.name as location_name,
        l.address as location_address,
        reporter.id as reporter_id,
        reporter.first_name as reporter_first_name,
        reporter.last_name as reporter_last_name,
        reporter.email as reporter_email,
        assigned.id as assigned_id,
        assigned.first_name as assigned_first_name,
        assigned.last_name as assigned_last_name,
        assigned.email as assigned_email,
        resolver.first_name as resolver_first_name,
        resolver.last_name as resolver_last_name
      FROM incidents i
      LEFT JOIN locations l ON i.location_id = l.id
      LEFT JOIN users reporter ON i.reporter_id = reporter.id
      LEFT JOIN users assigned ON i.assigned_to = assigned.id
      LEFT JOIN users resolver ON i.resolved_by = resolver.id
      WHERE i.organization_id = $1
    `;
    
    const params = [organizationId];
    let paramIndex = 2;

    // Apply filters
    if (search) {
      query += ` AND (i.title ILIKE $${paramIndex} OR i.description ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (status !== 'all') {
      query += ` AND i.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (severity !== 'all') {
      query += ` AND i.severity = $${paramIndex}`;
      params.push(severity);
      paramIndex++;
    }

    if (type !== 'all') {
      query += ` AND i.type = $${paramIndex}`;
      params.push(type);
      paramIndex++;
    }

    if (locationId !== 'all') {
      query += ` AND i.location_id = $${paramIndex}`;
      params.push(locationId);
      paramIndex++;
    }

    if (reporterId !== 'all') {
      query += ` AND i.reporter_id = $${paramIndex}`;
      params.push(reporterId);
      paramIndex++;
    }

    if (dateFrom) {
      query += ` AND i.created_at >= $${paramIndex}`;
      params.push(dateFrom);
      paramIndex++;
    }

    if (dateTo) {
      query += ` AND i.created_at <= $${paramIndex}`;
      params.push(dateTo);
      paramIndex++;
    }

    query += ` 
      ORDER BY i.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(limit, (page - 1) * limit);

    const result = await pool.query(query, params);
    const incidents = result.rows.map(incident => ({
      id: incident.id,
      title: incident.title,
      description: incident.description,
      type: incident.type,
      severity: incident.severity,
      status: incident.status,
      latitude: incident.latitude ? parseFloat(incident.latitude) : null,
      longitude: incident.longitude ? parseFloat(incident.longitude) : null,
      attachments: incident.attachments || [],
      createdAt: incident.created_at,
      updatedAt: incident.updated_at,
      resolvedAt: incident.resolved_at,
      resolutionNotes: incident.resolution_notes,
      location: incident.location_id ? {
        id: incident.location_id,
        name: incident.location_name,
        address: incident.location_address
      } : null,
      reporter: incident.reporter_id ? {
        id: incident.reporter_id,
        name: `${incident.reporter_first_name} ${incident.reporter_last_name}`,
        email: incident.reporter_email
      } : null,
      assignedEmployee: incident.assigned_id ? {
        id: incident.assigned_id,
        name: `${incident.assigned_first_name} ${incident.assigned_last_name}`,
        email: incident.assigned_email
      } : null,
      resolver: incident.resolver_first_name ? {
        name: `${incident.resolver_first_name} ${incident.resolver_last_name}`
      } : null
    }));

    res.json(incidents);
  } catch (error) {
    console.error('Error fetching incidents:', error);
    res.status(500).json({ error: 'Failed to fetch incidents' });
  }
});

// Get incident by ID
router.get('/:incidentId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, incidentId } = req.params;

    const result = await pool.query(`
      SELECT 
        i.*,
        l.name as location_name,
        l.address as location_address,
        reporter.first_name as reporter_first_name,
        reporter.last_name as reporter_last_name,
        reporter.email as reporter_email,
        resolver.first_name as resolver_first_name,
        resolver.last_name as resolver_last_name
      FROM incidents i
      LEFT JOIN locations l ON i.location_id = l.id
      LEFT JOIN users reporter ON i.reporter_id = reporter.id
      LEFT JOIN users resolver ON i.resolved_by = resolver.id
      WHERE i.organization_id = $1 AND i.id = $2
    `, [organizationId, incidentId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Incident not found' });
    }

    const incident = result.rows[0];

    res.json({
      id: incident.id,
      title: incident.title,
      description: incident.description,
      type: incident.type,
      severity: incident.severity,
      status: incident.status,
      latitude: incident.latitude ? parseFloat(incident.latitude) : null,
      longitude: incident.longitude ? parseFloat(incident.longitude) : null,
      createdAt: incident.created_at,
      updatedAt: incident.updated_at,
      resolvedAt: incident.resolved_at,
      resolutionNotes: incident.resolution_notes,
      location: incident.location_id ? {
        id: incident.location_id,
        name: incident.location_name,
        address: incident.location_address
      } : null,
      reporter: incident.reporter_id ? {
        id: incident.reporter_id,
        name: `${incident.reporter_first_name} ${incident.reporter_last_name}`,
        email: incident.reporter_email
      } : null,
      resolver: incident.resolver_first_name ? {
        name: `${incident.resolver_first_name} ${incident.resolver_last_name}`
      } : null
    });
  } catch (error) {
    console.error('Error fetching incident:', error);
    res.status(500).json({ error: 'Failed to fetch incident' });
  }
});

// Create new incident
router.post('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      title, 
      description, 
      type = 'general', 
      severity = 'medium',
      locationId,
      reporterId,
      latitude,
      longitude
    } = req.body;

    // Validate required fields
    if (!title || !description) {
      return res.status(400).json({ error: 'Title and description are required' });
    }

    // Verify reporter belongs to organization if provided
    if (reporterId) {
      const reporterCheck = await pool.query(`
        SELECT id FROM users WHERE id = $1 AND organization_id = $2
      `, [reporterId, organizationId]);

      if (reporterCheck.rows.length === 0) {
        return res.status(400).json({ error: 'Reporter not found or does not belong to organization' });
      }
    }

    // Verify location belongs to organization if provided
    if (locationId) {
      const locationCheck = await pool.query(`
        SELECT id FROM locations WHERE id = $1 AND organization_id = $2
      `, [locationId, organizationId]);

      if (locationCheck.rows.length === 0) {
        return res.status(400).json({ error: 'Location not found or does not belong to organization' });
      }
    }

    // Create incident
    const result = await pool.query(`
      INSERT INTO incidents (
        organization_id, title, description, type, severity, status, location_id, 
        reporter_id, latitude, longitude
      ) VALUES ($1, $2, $3, $4, $5, 'open', $6, $7, $8, $9)
      RETURNING *
    `, [organizationId, title, description, type, severity, locationId, reporterId, latitude, longitude]);

    const newIncident = result.rows[0];

    // Get full incident details
    const fullIncident = await pool.query(`
      SELECT 
        i.*,
        l.name as location_name,
        l.address as location_address,
        reporter.first_name as reporter_first_name,
        reporter.last_name as reporter_last_name,
        reporter.email as reporter_email
      FROM incidents i
      LEFT JOIN locations l ON i.location_id = l.id
      LEFT JOIN users reporter ON i.reporter_id = reporter.id
      WHERE i.id = $1
    `, [newIncident.id]);

    const incident = fullIncident.rows[0];

    res.status(201).json({
      id: incident.id,
      title: incident.title,
      description: incident.description,
      type: incident.type,
      severity: incident.severity,
      status: incident.status,
      latitude: incident.latitude ? parseFloat(incident.latitude) : null,
      longitude: incident.longitude ? parseFloat(incident.longitude) : null,
      createdAt: incident.created_at,
      location: incident.location_id ? {
        id: incident.location_id,
        name: incident.location_name,
        address: incident.location_address
      } : null,
      reporter: incident.reporter_id ? {
        id: incident.reporter_id,
        name: `${incident.reporter_first_name} ${incident.reporter_last_name}`,
        email: incident.reporter_email
      } : null
    });
  } catch (error) {
    console.error('Error creating incident:', error);
    res.status(500).json({ error: 'Failed to create incident' });
  }
});

// Update incident
router.put('/:incidentId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, incidentId } = req.params;
    const { title, description, type, severity, status, resolutionNotes } = req.body;

    // Check if incident exists and belongs to organization
    const existingIncident = await pool.query(`
      SELECT id FROM incidents WHERE id = $1 AND organization_id = $2
    `, [incidentId, organizationId]);

    if (existingIncident.rows.length === 0) {
      return res.status(404).json({ error: 'Incident not found' });
    }

    // Build update query dynamically
    const updates = [];
    const params = [];
    let paramIndex = 1;

    if (title) {
      updates.push(`title = $${paramIndex}`);
      params.push(title);
      paramIndex++;
    }
    if (description) {
      updates.push(`description = $${paramIndex}`);
      params.push(description);
      paramIndex++;
    }
    if (type) {
      updates.push(`type = $${paramIndex}`);
      params.push(type);
      paramIndex++;
    }
    if (severity) {
      updates.push(`severity = $${paramIndex}`);
      params.push(severity);
      paramIndex++;
    }
    if (status) {
      updates.push(`status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
      
      // If resolving incident, set resolved timestamp and user
      if (status === 'resolved') {
        updates.push(`resolved_at = NOW()`);
        updates.push(`resolved_by = $${paramIndex}`);
        params.push(req.user.id);
        paramIndex++;
      }
    }
    if (resolutionNotes) {
      updates.push(`resolution_notes = $${paramIndex}`);
      params.push(resolutionNotes);
      paramIndex++;
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updates.push(`updated_at = NOW()`);
    params.push(incidentId, organizationId);

    const result = await pool.query(`
      UPDATE incidents 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex} AND organization_id = $${paramIndex + 1}
      RETURNING *
    `, params);

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating incident:', error);
    res.status(500).json({ error: 'Failed to update incident' });
  }
});

// Delete incident
router.delete('/:incidentId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, incidentId } = req.params;

    // Set incident status to cancelled instead of deleting
    await pool.query(`
      UPDATE incidents 
      SET status = 'cancelled', updated_at = NOW()
      WHERE id = $1 AND organization_id = $2
    `, [incidentId, organizationId]);

    res.json({ message: 'Incident cancelled successfully' });
  } catch (error) {
    console.error('Error deleting incident:', error);
    res.status(500).json({ error: 'Failed to delete incident' });
  }
});

// Get incident statistics
router.get('/stats/overview', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { dateFrom, dateTo } = req.query;

    let dateFilter = '';
    const params = [organizationId];
    
    if (dateFrom && dateTo) {
      dateFilter = 'AND created_at BETWEEN $2 AND $3';
      params.push(dateFrom, dateTo);
    }

    const result = await pool.query(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'open' THEN 1 END) as open,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
        COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved,
        COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed,
        COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical,
        COUNT(CASE WHEN severity = 'high' THEN 1 END) as high,
        COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium,
        COUNT(CASE WHEN severity = 'low' THEN 1 END) as low
      FROM incidents 
      WHERE organization_id = $1 ${dateFilter}
    `, params);

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching incident stats:', error);
    res.status(500).json({ error: 'Failed to fetch incident statistics' });
  }
});

module.exports = router; 