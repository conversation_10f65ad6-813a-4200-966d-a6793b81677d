import React from 'react';

const BrandedButton = ({ 
  children, 
  variant = 'primary', 
  size = 'md',
  className = '',
  primaryColor = '#3b82f6', // Default blue color
  ...props 
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200';
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };
  
  const variantClasses = {
    primary: 'text-white border border-transparent',
    secondary: 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50',
    outline: 'bg-transparent border-2 text-gray-700 hover:bg-gray-50',
    ghost: 'bg-transparent text-gray-700 hover:bg-gray-100'
  };

  const getBrandedStyles = () => {
    if (variant === 'primary') {
      return {
        backgroundColor: primaryColor,
        borderColor: primaryColor,
        '--tw-ring-color': primaryColor
      };
    }
    return {};
  };

  return (
    <button
      className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
      style={getBrandedStyles()}
      {...props}
    >
      {children}
    </button>
  );
};

export default BrandedButton; 