-- Dummy Data Script for Multi-Tenant Admin System
-- Run this after creating the schema to populate all tables with sample data

-- Organizations
INSERT INTO organizations (id, name, email, contact_person, subscription_plan, max_users, max_locations, status, created_at, updated_at)
VALUES
  ('00000000-0000-0000-0000-000000000001', 'Acme Security', '<EMAIL>', 'Alice Manager', 'premium', 100, 10, 'active', NOW(), NOW()),
  ('00000000-0000-0000-0000-000000000002', 'City Guard', '<EMAIL>', 'Bob Supervisor', 'standard', 50, 5, 'active', NOW(), NOW()),
  ('00000000-0000-0000-0000-000000000003', 'Metro Protection', '<EMAIL>', 'Carol Admin', 'enterprise', 200, 20, 'active', NOW(), NOW());

-- Users
INSERT INTO users (id, organization_id, email, password_hash, first_name, last_name, role, status, created_at, updated_at)
VALUES
  ('10000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '<EMAIL>', 'hash1', 'Alice', 'Manager', 'admin', 'active', NOW(), NOW()),
  ('10000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000001', '<EMAIL>', 'hash2', 'Eve', 'Employee', 'employee', 'active', NOW(), NOW()),
  ('10000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000002', '<EMAIL>', 'hash3', 'Bob', 'Supervisor', 'admin', 'active', NOW(), NOW()),
  ('10000000-0000-0000-0000-000000000004', '00000000-0000-0000-0000-000000000002', '<EMAIL>', 'hash4', 'Dave', 'Guard', 'employee', 'active', NOW(), NOW()),
  ('10000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000003', '<EMAIL>', 'hash5', 'Carol', 'Admin', 'admin', 'active', NOW(), NOW());

-- Locations
INSERT INTO locations (id, organization_id, name, address, latitude, longitude, status, created_at, updated_at)
VALUES
  ('20000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'Acme HQ', '123 Main St, City', 40.7128, -74.0060, 'active', NOW(), NOW()),
  ('20000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', 'City Guard Post', '456 Elm St, City', 34.0522, -118.2437, 'active', NOW(), NOW()),
  ('20000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000003', 'Metro HQ', '789 Oak St, City', 41.8781, -87.6298, 'active', NOW(), NOW());

-- Assignments
INSERT INTO assignments (id, organization_id, user_id, location_id, shift_start, shift_end, status, created_at, updated_at)
VALUES
  ('30000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000002', '20000000-0000-0000-0000-000000000001', NOW(), NOW() + INTERVAL '8 hours', 'scheduled', NOW(), NOW()),
  ('30000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000004', '20000000-0000-0000-0000-000000000002', NOW(), NOW() + INTERVAL '8 hours', 'scheduled', NOW(), NOW());

-- User Locations
INSERT INTO user_locations (id, user_id, location_id, latitude, longitude, timestamp, created_at)
VALUES
  ('40000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000002', '20000000-0000-0000-0000-000000000001', 40.7128, -74.0060, NOW(), NOW()),
  ('40000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000004', '20000000-0000-0000-0000-000000000002', 34.0522, -118.2437, NOW(), NOW());

-- Incidents
INSERT INTO incidents (id, organization_id, location_id, reporter_id, title, type, severity, status, created_at, updated_at)
VALUES
  ('50000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '20000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000002', 'Unauthorized Entry', 'trespassing', 'high', 'open', NOW(), NOW()),
  ('50000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', '20000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000004', 'Alarm Triggered', 'safety', 'medium', 'open', NOW(), NOW());

-- Messages
INSERT INTO messages (id, organization_id, sender_id, recipient_id, subject, content, created_at)
VALUES
  ('60000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000002', 'Welcome', 'Welcome to Acme Security!', NOW()),
  ('60000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000004', 'Shift Reminder', 'Your shift starts at 8am.', NOW());

-- Geofences
INSERT INTO geofences (id, organization_id, location_id, name, center_latitude, center_longitude, radius, status, created_at, updated_at)
VALUES
  ('70000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '20000000-0000-0000-0000-000000000001', 'Acme HQ Perimeter', 40.7128, -74.0060, 100, 'active', NOW(), NOW()),
  ('70000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', '20000000-0000-0000-0000-000000000002', 'City Guard Post Perimeter', 34.0522, -118.2437, 150, 'active', NOW(), NOW());

-- Audit Logs
INSERT INTO audit_logs (id, organization_id, user_id, action, resource_type, created_at)
VALUES
  ('80000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', 'login', 'user', NOW()),
  ('80000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000003', 'create', 'assignment', NOW());

-- Notifications
INSERT INTO notifications (id, organization_id, user_id, type, title, message, created_at)
VALUES
  ('90000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000002', 'assignment', 'New Assignment', 'You have a new assignment.', NOW()),
  ('90000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000004', 'incident', 'Incident Reported', 'A new incident has been reported.', NOW());

-- Organization Branding
INSERT INTO organization_branding (id, organization_id, logo_url, primary_color, created_at, updated_at)
VALUES
  ('a0000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'https://example.com/logo1.png', '#3B82F6', NOW(), NOW()),
  ('a0000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', 'https://example.com/logo2.png', '#1E40AF', NOW(), NOW());

-- Reports
INSERT INTO reports (id, organization_id, created_by, name, report_type, status, created_at, updated_at)
VALUES
  ('b0000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', 'Daily Activity', 'activity', 'generated', NOW(), NOW()),
  ('b0000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000003', 'Incident Summary', 'incident', 'draft', NOW(), NOW());

-- System Logs
INSERT INTO system_logs (id, organization_id, level, category, message, created_at)
VALUES
  ('c0000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'info', 'auth', 'User login successful', NOW()),
  ('c0000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', 'error', 'incident', 'Incident creation failed', NOW());

-- Add more dummy data as needed for other tables (e.g., check_ins, recurring_assignments, etc.) 