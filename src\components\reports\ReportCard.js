import React, { useState } from 'react';
import { ChevronDown, ChevronRight, BarChart3, TrendingUp, Users, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

const ReportCard = ({ 
  category, 
  onReportSelect, 
  selectedReport, 
  data, 
  loading 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const isSelected = selectedReport?.categoryId === category.id;

  const getCategoryIcon = (categoryId) => {
    switch (categoryId) {
      case 'attendance':
        return Clock;
      case 'incidents':
        return AlertTriangle;
      case 'performance':
        return TrendingUp;
      case 'compliance':
        return CheckCircle;
      default:
        return BarChart3;
    }
  };

  const getCategoryStats = () => {
    if (!data) return null;

    switch (category.id) {
      case 'attendance':
        return {
          totalEmployees: data.totalEmployees || 0,
          averageAttendance: data.averageAttendance || 0,
          lateArrivals: data.lateArrivals || 0
        };
      case 'incidents':
        return {
          totalIncidents: data.totalIncidents || 0,
          resolvedIncidents: data.resolvedIncidents || 0,
          averageResolutionTime: data.averageResolutionTime || 0
        };
      case 'performance':
        return {
          averagePerformance: data.averagePerformance || 0,
          topPerformers: data.topPerformers || 0,
          improvementAreas: data.improvementAreas || 0
        };
      case 'compliance':
        return {
          complianceScore: data.complianceScore || 0,
          violations: data.violations || 0,
          trainingCompletion: data.trainingCompletion || 0
        };
      default:
        return null;
    }
  };

  const Icon = getCategoryIcon(category.id);
  const stats = getCategoryStats();

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Category Header */}
      <div 
        className={`p-4 cursor-pointer transition-colors ${
          isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50'
        }`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${category.color}`}>
              <Icon className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{category.title}</h3>
              <p className="text-sm text-gray-600">{category.description}</p>
            </div>
          </div>
          {isExpanded ? (
            <ChevronDown className="h-5 w-5 text-gray-400" />
          ) : (
            <ChevronRight className="h-5 w-5 text-gray-400" />
          )}
        </div>

        {/* Quick Stats */}
        {stats && (
          <div className="mt-3 grid grid-cols-3 gap-4">
            {Object.entries(stats).map(([key, value]) => (
              <div key={key} className="text-center">
                <p className="text-lg font-semibold text-gray-900">
                  {typeof value === 'number' && key.includes('Score') || key.includes('Attendance') || key.includes('Performance') || key.includes('Completion')
                    ? `${value}%`
                    : value}
                </p>
                <p className="text-xs text-gray-500 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Reports List */}
      {isExpanded && (
        <div className="border-t border-gray-200">
          <div className="p-4 space-y-2">
            {category.reports.map((report) => {
              const isReportSelected = selectedReport?.reportId === report.id;
              
              return (
                <div
                  key={report.id}
                  onClick={() => onReportSelect(category.id, report.id)}
                  className={`p-3 rounded-md cursor-pointer transition-colors ${
                    isReportSelected
                      ? 'bg-blue-100 border border-blue-200'
                      : 'hover:bg-gray-50 border border-transparent'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{report.title}</h4>
                      <p className="text-sm text-gray-600">{report.description}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {/* Report Type Badge */}
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        report.type === 'chart'
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {report.type === 'chart' ? 'Chart' : 'Table'}
                      </span>
                      
                      {/* Loading Indicator */}
                      {loading && isReportSelected && (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportCard; 