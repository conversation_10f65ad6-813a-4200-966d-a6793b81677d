import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useTenant } from '../../tenant/TenantProvider';
import BrandedButton from '../BrandedButton';
import { 
  X, 
  MapPin, 
  AlertTriangle, 
  Bell, 
  Users, 
  Clock,
  Shield,
  Settings,
  Plus,
  Trash2
} from 'lucide-react';

const GeofenceForm = ({ geofence, onSubmit, onCancel }) => {
  const { tenant } = useTenant();
  const { locations, employees } = useSelector((state) => ({
    locations: state.locations?.locations || [],
    employees: state.employees?.employees || []
  }));

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    locationId: '',
    radius: 100, // meters
    center: { lat: 0, lng: 0 },
    alertRules: {
      entryAlerts: true,
      exitAlerts: true,
      unauthorizedEntry: true,
      unauthorizedExit: true,
      dwellTimeAlerts: false,
      dwellTimeMinutes: 30
    },
    notificationSettings: {
      email: false,
      sms: false,
      inApp: true,
      webhook: false,
      webhookUrl: ''
    },
    allowedEmployees: [],
    restrictedEmployees: [],
    schedule: {
      enabled: false,
      startTime: '08:00',
      endTime: '18:00',
      daysOfWeek: [1, 2, 3, 4, 5] // Monday to Friday
    },
    status: 'active'
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with geofence data if editing
  useEffect(() => {
    if (geofence) {
      setFormData({
        ...geofence,
        center: geofence.center || { lat: 0, lng: 0 }
      });
    }
  }, [geofence]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const handleNestedChange = (parent, field, value) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }));
  };

  const handleEmployeeToggle = (employeeId, listType) => {
    setFormData(prev => ({
      ...prev,
      [listType]: prev[listType].includes(employeeId)
        ? prev[listType].filter(id => id !== employeeId)
        : [...prev[listType], employeeId]
    }));
  };

  const handleDayToggle = (day) => {
    setFormData(prev => ({
      ...prev,
      schedule: {
        ...prev.schedule,
        daysOfWeek: prev.schedule.daysOfWeek.includes(day)
          ? prev.schedule.daysOfWeek.filter(d => d !== day)
          : [...prev.schedule.daysOfWeek, day]
      }
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Geofence name is required';
    }

    if (!formData.locationId) {
      newErrors.locationId = 'Location is required';
    }

    if (formData.radius < 10 || formData.radius > 10000) {
      newErrors.radius = 'Radius must be between 10 and 10,000 meters';
    }

    if (formData.center.lat === 0 && formData.center.lng === 0) {
      newErrors.center = 'Geofence center must be set';
    }

    if (formData.notificationSettings.webhook && !formData.notificationSettings.webhookUrl) {
      newErrors.webhookUrl = 'Webhook URL is required when webhook notifications are enabled';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({
        ...formData,
        organizationId: tenant.id,
        id: geofence?.id
      });
    } catch (error) {
      console.error('Error submitting geofence:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const daysOfWeek = [
    { value: 1, label: 'Mon' },
    { value: 2, label: 'Tue' },
    { value: 3, label: 'Wed' },
    { value: 4, label: 'Thu' },
    { value: 5, label: 'Fri' },
    { value: 6, label: 'Sat' },
    { value: 0, label: 'Sun' }
  ];

  return (
    <div className="bg-white rounded-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            {geofence ? 'Edit Geofence' : 'Create New Geofence'}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="px-6 py-4 space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Basic Information
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Geofence Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`block w-full border rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter geofence name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Location *
              </label>
              <select
                value={formData.locationId}
                onChange={(e) => handleInputChange('locationId', e.target.value)}
                className={`block w-full border rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.locationId ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select a location</option>
                {locations.map(location => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
              {errors.locationId && (
                <p className="mt-1 text-sm text-red-600">{errors.locationId}</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe the purpose of this geofence"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Radius (meters) *
              </label>
              <input
                type="number"
                value={formData.radius}
                onChange={(e) => handleInputChange('radius', parseInt(e.target.value))}
                min="10"
                max="10000"
                className={`block w-full border rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.radius ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.radius && (
                <p className="mt-1 text-sm text-red-600">{errors.radius}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </div>

        {/* Alert Rules */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Alert Rules
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.alertRules.entryAlerts}
                  onChange={(e) => handleNestedChange('alertRules', 'entryAlerts', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Entry Alerts</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.alertRules.exitAlerts}
                  onChange={(e) => handleNestedChange('alertRules', 'exitAlerts', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Exit Alerts</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.alertRules.unauthorizedEntry}
                  onChange={(e) => handleNestedChange('alertRules', 'unauthorizedEntry', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Unauthorized Entry</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.alertRules.unauthorizedExit}
                  onChange={(e) => handleNestedChange('alertRules', 'unauthorizedExit', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Unauthorized Exit</span>
              </label>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.alertRules.dwellTimeAlerts}
                  onChange={(e) => handleNestedChange('alertRules', 'dwellTimeAlerts', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Dwell Time Alerts</span>
              </label>

              {formData.alertRules.dwellTimeAlerts && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Dwell Time (minutes)
                  </label>
                  <input
                    type="number"
                    value={formData.alertRules.dwellTimeMinutes}
                    onChange={(e) => handleNestedChange('alertRules', 'dwellTimeMinutes', parseInt(e.target.value))}
                    min="1"
                    max="1440"
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notification Settings
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.notificationSettings.email}
                  onChange={(e) => handleNestedChange('notificationSettings', 'email', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Email Notifications</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.notificationSettings.sms}
                  onChange={(e) => handleNestedChange('notificationSettings', 'sms', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">SMS Notifications</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.notificationSettings.inApp}
                  onChange={(e) => handleNestedChange('notificationSettings', 'inApp', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">In-App Notifications</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.notificationSettings.webhook}
                  onChange={(e) => handleNestedChange('notificationSettings', 'webhook', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Webhook Notifications</span>
              </label>
            </div>

            {formData.notificationSettings.webhook && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Webhook URL
                </label>
                <input
                  type="url"
                  value={formData.notificationSettings.webhookUrl}
                  onChange={(e) => handleNestedChange('notificationSettings', 'webhookUrl', e.target.value)}
                  className={`block w-full border rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.webhookUrl ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="https://your-webhook-url.com"
                />
                {errors.webhookUrl && (
                  <p className="mt-1 text-sm text-red-600">{errors.webhookUrl}</p>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Employee Access Control */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Employee Access Control
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">Allowed Employees</h5>
              <div className="max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                {employees.map(employee => (
                  <label key={employee.id} className="flex items-center py-1">
                    <input
                      type="checkbox"
                      checked={formData.allowedEmployees.includes(employee.id)}
                      onChange={() => handleEmployeeToggle(employee.id, 'allowedEmployees')}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">{employee.name}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">Restricted Employees</h5>
              <div className="max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                {employees.map(employee => (
                  <label key={employee.id} className="flex items-center py-1">
                    <input
                      type="checkbox"
                      checked={formData.restrictedEmployees.includes(employee.id)}
                      onChange={() => handleEmployeeToggle(employee.id, 'restrictedEmployees')}
                      className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">{employee.name}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Schedule */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Schedule (Optional)
          </h4>

          <div className="space-y-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.schedule.enabled}
                onChange={(e) => handleNestedChange('schedule', 'enabled', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Enable Schedule</span>
            </label>

            {formData.schedule.enabled && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Start Time
                  </label>
                  <input
                    type="time"
                    value={formData.schedule.startTime}
                    onChange={(e) => handleNestedChange('schedule', 'startTime', e.target.value)}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    End Time
                  </label>
                  <input
                    type="time"
                    value={formData.schedule.endTime}
                    onChange={(e) => handleNestedChange('schedule', 'endTime', e.target.value)}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Days of Week
                  </label>
                  <div className="flex flex-wrap gap-1">
                    {daysOfWeek.map(day => (
                      <button
                        key={day.value}
                        type="button"
                        onClick={() => handleDayToggle(day.value)}
                        className={`px-2 py-1 text-xs rounded-md border ${
                          formData.schedule.daysOfWeek.includes(day.value)
                            ? 'bg-blue-500 text-white border-blue-500'
                            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {day.label}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <BrandedButton
            type="submit"
            disabled={isSubmitting}
            className="flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Saving...
              </>
            ) : (
              <>
                <Shield className="h-4 w-4" />
                {geofence ? 'Update Geofence' : 'Create Geofence'}
              </>
            )}
          </BrandedButton>
        </div>
      </form>
    </div>
  );
};

export default GeofenceForm; 