import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { login } from './store/slices/authSlice';

const TestLogin = () => {
  const dispatch = useDispatch();
  const { isLoading, error, isAuthenticated, user } = useSelector((state) => state.auth);
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('admin123');

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('TestLogin: Form submitted');
    console.log('TestLogin: Email:', email);
    console.log('TestLogin: Password:', password);
    
    const credentials = {
      email,
      password,
      loginType: 'super_admin'
    };
    
    console.log('TestLogin: Dispatching login with:', credentials);
    dispatch(login(credentials));
  };

  return (
    <div style={{ padding: '20px', maxWidth: '400px', margin: '0 auto' }}>
      <h2>Test Super Admin Login</h2>
      
      {error && (
        <div style={{ color: 'red', marginBottom: '10px' }}>
          Error: {JSON.stringify(error)}
        </div>
      )}
      
      {isAuthenticated && user && (
        <div style={{ color: 'green', marginBottom: '10px' }}>
          Success! Logged in as: {user.email} (Role: {user.role})
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '10px' }}>
          <label>Email:</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            style={{ width: '100%', padding: '5px' }}
          />
        </div>
        
        <div style={{ marginBottom: '10px' }}>
          <label>Password:</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            style={{ width: '100%', padding: '5px' }}
          />
        </div>
        
        <button 
          type="submit" 
          disabled={isLoading}
          style={{ padding: '10px 20px', width: '100%' }}
        >
          {isLoading ? 'Logging in...' : 'Login'}
        </button>
      </form>
      
      <div style={{ marginTop: '20px', fontSize: '12px' }}>
        <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
        <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
        <p>User: {user ? JSON.stringify(user, null, 2) : 'None'}</p>
        <p>Error: {error ? JSON.stringify(error, null, 2) : 'None'}</p>
      </div>
    </div>
  );
};

export default TestLogin;
