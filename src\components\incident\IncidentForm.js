import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import BrandedButton from '../BrandedButton';
import { X, Upload, AlertTriangle, MapPin, User, Calendar, FileText } from 'lucide-react';

const IncidentForm = ({ incident, employees, locations, tenantId, onSubmit, onCancel }) => {
  const isEditing = !!incident;
  const [attachments, setAttachments] = useState(incident?.attachments || []);
  const [uploading, setUploading] = useState(false);

  // Helper function to get form defaults
  const getFormDefaults = () => {
    if (incident) {
      return {
        title: incident.title || '',
        description: incident.description || '',
        severity: incident.severity || 'medium',
        locationId: incident.locationId || '',
        assignedEmployeeId: incident.assignedEmployeeId || '',
        status: incident.status || 'open',
        category: incident.category || incident.type || 'general',
        priority: incident.priority || 'normal',
        attachments: incident.attachments || []
      };
    }

    return {
      title: '',
      description: '',
      severity: 'medium',
      locationId: '',
      assignedEmployeeId: '',
      status: 'open',
      category: 'general',
      priority: 'normal',
      attachments: []
    };
  };

  const { register, handleSubmit, formState: { errors, isSubmitting }, watch, setValue } = useForm({
    defaultValues: getFormDefaults()
  });

  const handleFormSubmit = async (data) => {
    try {
      const incidentData = {
        ...data,
        attachments,
        submittedAt: incident?.submittedAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      onSubmit(incidentData);
    } catch (error) {
      console.error('Error saving incident:', error);
    }
  };

  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    setUploading(true);
    
    try {
      // Simulate file upload - replace with actual upload logic
      const uploadedFiles = await Promise.all(
        files.map(async (file) => {
          // Simulate upload delay
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          return {
            id: Date.now() + Math.random(),
            name: file.name,
            size: file.size,
            type: file.type,
            url: URL.createObjectURL(file), // In real app, this would be the uploaded URL
            uploadedAt: new Date().toISOString()
          };
        })
      );
      
      setAttachments([...attachments, ...uploadedFiles]);
    } catch (error) {
      console.error('Error uploading files:', error);
    } finally {
      setUploading(false);
    }
  };

  const removeAttachment = (attachmentId) => {
    setAttachments(attachments.filter(att => att.id !== attachmentId));
  };

  const severityOptions = [
    { value: 'low', label: 'Low', color: 'text-green-600', bgColor: 'bg-green-100' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
    { value: 'high', label: 'High', color: 'text-orange-600', bgColor: 'bg-orange-100' },
    { value: 'critical', label: 'Critical', color: 'text-red-600', bgColor: 'bg-red-100' }
  ];

  const statusOptions = [
    { value: 'open', label: 'Open', color: 'text-red-600', bgColor: 'bg-red-100' },
    { value: 'in_progress', label: 'In Progress', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
    { value: 'resolved', label: 'Resolved', color: 'text-green-600', bgColor: 'bg-green-100' },
    { value: 'closed', label: 'Closed', color: 'text-gray-600', bgColor: 'bg-gray-100' }
  ];

  const categoryOptions = [
    { value: 'general', label: 'General' },
    { value: 'safety', label: 'Safety' },
    { value: 'equipment', label: 'Equipment' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'security', label: 'Security' },
    { value: 'environmental', label: 'Environmental' },
    { value: 'quality', label: 'Quality' },
    { value: 'other', label: 'Other' }
  ];

  const priorityOptions = [
    { value: 'low', label: 'Low' },
    { value: 'normal', label: 'Normal' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' }
  ];

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          {isEditing ? 'Edit Incident' : 'Report New Incident'}
        </h2>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-gray-600"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Incident Details
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Title *
              </label>
              <input
                type="text"
                {...register('title', { required: 'Title is required' })}
                className="input"
                placeholder="Brief description of the incident"
              />
              {errors.title && (
                <span className="text-red-600 text-xs">{errors.title.message}</span>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                {...register('category')}
                className="input"
              >
                {categoryOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Priority
              </label>
              <select
                {...register('priority')}
                className="input"
              >
                {priorityOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Severity *
              </label>
              <select
                {...register('severity', { required: 'Severity is required' })}
                className="input"
              >
                {severityOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.severity && (
                <span className="text-red-600 text-xs">{errors.severity.message}</span>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                {...register('status')}
                className="input"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <textarea
                {...register('description', { required: 'Description is required' })}
                rows={4}
                className="input"
                placeholder="Detailed description of the incident, including what happened, when, where, and any relevant details"
              />
              {errors.description && (
                <span className="text-red-600 text-xs">{errors.description.message}</span>
              )}
            </div>
          </div>
        </div>

        {/* Location and Assignment */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Location & Assignment
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Location *
              </label>
              <select
                {...register('locationId', { required: 'Location is required' })}
                className="input"
              >
                <option value="">Select location</option>
                {locations.map(location => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
              {errors.locationId && (
                <span className="text-red-600 text-xs">{errors.locationId.message}</span>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Assigned Employee
              </label>
              <select
                {...register('assignedEmployeeId')}
                className="input"
              >
                <option value="">Select employee</option>
                {employees.map(employee => (
                  <option key={employee.id} value={employee.id}>
                    {employee.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Attachments */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Attachments
          </h3>
          
          <div className="space-y-4">
            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload Files
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 mb-2">
                  Drag and drop files here, or click to select
                </p>
                <input
                  type="file"
                  multiple
                  onChange={handleFileUpload}
                  disabled={uploading}
                  className="hidden"
                  id="file-upload"
                  accept="image/*,.pdf,.doc,.docx,.txt"
                />
                <label
                  htmlFor="file-upload"
                  className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  {uploading ? 'Uploading...' : 'Choose Files'}
                </label>
              </div>
            </div>

            {/* Attached Files */}
            {attachments.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Attached Files</h4>
                <div className="space-y-2">
                  {attachments.map((attachment) => (
                    <div key={attachment.id} className="flex items-center justify-between bg-white p-3 rounded-md border">
                      <div className="flex items-center gap-3">
                        <FileText className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{attachment.name}</p>
                          <p className="text-xs text-gray-500">{formatFileSize(attachment.size)}</p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeAttachment(attachment.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <BrandedButton
            type="submit"
            disabled={isSubmitting || uploading}
          >
            {isSubmitting ? 'Saving...' : (isEditing ? 'Update Incident' : 'Submit Incident')}
          </BrandedButton>
        </div>
      </form>
    </div>
  );
};

export default IncidentForm; 