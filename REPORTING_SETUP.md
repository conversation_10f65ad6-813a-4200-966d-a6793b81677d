# Reporting & Analytics Module Setup Guide

This guide covers the setup and configuration of the comprehensive reporting and analytics module for the OnTheMove admin dashboard.

## Overview

The reporting and analytics module provides:
- **Attendance Reports**: Daily attendance, trends, late arrivals, overtime analysis
- **Incident Reports**: Incident patterns, severity analysis, resolution times
- **Performance Reports**: Employee productivity, task completion, performance comparison
- **Compliance Reports**: Safety compliance, training status, audit results
- **Export Functionality**: PDF, CSV, and Excel export capabilities
- **Advanced Filtering**: Date ranges, employees, locations, departments, incident types
- **Real-time Data**: Live updates via WebSocket integration

## Features

### 1. Report Categories

#### Attendance Reports
- **Daily Attendance**: Employee attendance summary by date
- **Attendance Summary**: Monthly overview with trends
- **Late Arrivals**: Employees with frequent late arrivals
- **Overtime Analysis**: Overtime hours and cost analysis

#### Incident Reports
- **Incident Summary**: Overview by type and severity
- **Incident Trends**: Frequency and patterns over time
- **Resolution Times**: Average time to resolve by type
- **Incident Details**: Detailed incident information

#### Performance Reports
- **Productivity Metrics**: Employee efficiency scores
- **Task Completion**: Completion rates and quality metrics
- **Performance Comparison**: Cross-team comparisons
- **Performance Details**: Individual employee breakdowns

#### Compliance Reports
- **Safety Compliance**: Protocol adherence and violations
- **Training Compliance**: Certification status
- **Audit Summary**: Compliance audit results
- **Violation Tracking**: Compliance violation analysis

### 2. Export Formats

- **PDF**: Professional formatted reports with charts and tables
- **CSV**: Raw data export for external analysis
- **Excel**: Formatted spreadsheets with multiple sheets

### 3. Advanced Filtering

- **Date Ranges**: Today, week, month, quarter, year, custom
- **Employees**: Filter by specific employees or all
- **Locations**: Filter by specific locations or all
- **Departments**: Operations, maintenance, security, etc.
- **Incident Types**: Safety, equipment, maintenance, etc.
- **Severity Levels**: Low, medium, high, critical

## API Endpoints

### Base URL Structure
```
/tenants/{tenantId}/reports/{reportType}
```

### Attendance Reports
```javascript
// Get attendance data
GET /tenants/{tenantId}/reports/attendance
Query Parameters:
- startDate: YYYY-MM-DD
- endDate: YYYY-MM-DD
- employees: string[] (employee IDs)
- locations: string[] (location IDs)
- departments: string[] (department names)

Response:
{
  totalEmployees: number,
  averageAttendance: number,
  lateArrivals: number,
  attendanceData: {
    dailyTrend: [
      { date: string, attendance: number, expected: number }
    ],
    byDepartment: [
      { department: string, attendance: number, expected: number }
    ]
  },
  tableData: {
    headers: string[],
    rows: any[][]
  },
  summary: {
    totalDays: number,
    averageAttendance: number,
    lateArrivals: number,
    overtimeHours: number
  }
}
```

### Incident Reports
```javascript
// Get incident data
GET /tenants/{tenantId}/reports/incidents
Query Parameters:
- startDate: YYYY-MM-DD
- endDate: YYYY-MM-DD
- employees: string[]
- locations: string[]
- incidentTypes: string[]
- severityLevels: string[]

Response:
{
  totalIncidents: number,
  resolvedIncidents: number,
  averageResolutionTime: number,
  incidentData: {
    trends: [
      { date: string, incidents: number }
    ],
    byType: [
      { name: string, value: number }
    ],
    resolutionTimes: [
      { type: string, hours: number }
    ]
  },
  tableData: {
    headers: string[],
    rows: any[][]
  },
  summary: {
    totalIncidents: number,
    resolvedIncidents: number,
    averageResolutionTime: number,
    criticalIncidents: number
  }
}
```

### Performance Reports
```javascript
// Get performance data
GET /tenants/{tenantId}/reports/performance
Query Parameters:
- startDate: YYYY-MM-DD
- endDate: YYYY-MM-DD
- employees: string[]
- departments: string[]

Response:
{
  averagePerformance: number,
  topPerformers: number,
  improvementAreas: number,
  performanceData: {
    metrics: [
      { employee: string, productivity: number, quality: number, efficiency: number }
    ],
    trends: [
      { month: string, average: number, target: number }
    ]
  },
  tableData: {
    headers: string[],
    rows: any[][]
  },
  summary: {
    averagePerformance: number,
    topPerformers: number,
    improvementAreas: number,
    totalTasks: number
  }
}
```

### Compliance Reports
```javascript
// Get compliance data
GET /tenants/{tenantId}/reports/compliance
Query Parameters:
- startDate: YYYY-MM-DD
- endDate: YYYY-MM-DD
- employees: string[]
- departments: string[]

Response:
{
  complianceScore: number,
  violations: number,
  trainingCompletion: number,
  complianceData: {
    scores: [
      { category: string, score: number }
    ],
    violations: [
      { name: string, value: number }
    ]
  },
  tableData: {
    headers: string[],
    rows: any[][]
  },
  summary: {
    complianceScore: number,
    violations: number,
    trainingCompletion: number,
    auditScore: number
  }
}
```

### Report Generation
```javascript
// Generate report
POST /tenants/{tenantId}/reports/generate
Body:
{
  reportType: string,
  filters: object,
  format: 'pdf' | 'csv' | 'excel'
}

Response:
{
  reportId: string,
  downloadUrl: string,
  generatedAt: string,
  reportType: string,
  format: string
}
```

### Report Export
```javascript
// Export report
POST /tenants/{tenantId}/reports/export
Body:
{
  reportType: string,
  filters: object,
  format: 'pdf' | 'csv' | 'excel'
}

Response: Blob (file download)
```

## Data Structure

### Report Filters
```javascript
const filters = {
  dateRange: 'month', // today, week, month, quarter, year, custom
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  employees: [], // employee IDs
  locations: [], // location IDs
  departments: [], // department names
  incidentTypes: [], // incident type names
  severityLevels: [] // severity levels
};
```

### Chart Data Structure
```javascript
// Line/Area Chart Data
const chartData = [
  { date: '2024-01-01', value: 85, target: 90 },
  { date: '2024-01-02', value: 88, target: 90 }
];

// Bar Chart Data
const barData = [
  { category: 'Operations', value: 92, expected: 90 },
  { category: 'Maintenance', value: 87, expected: 90 }
];

// Pie Chart Data
const pieData = [
  { name: 'Safety', value: 45 },
  { name: 'Equipment', value: 30 },
  { name: 'Maintenance', value: 25 }
];
```

### Table Data Structure
```javascript
const tableData = {
  headers: ['Employee', 'Department', 'Attendance %', 'Late Arrivals'],
  rows: [
    ['John Doe', 'Operations', '95%', '2'],
    ['Jane Smith', 'Maintenance', '88%', '5']
  ]
};
```

## Frontend Components

### 1. ReportingAnalytics.js
Main page component with:
- Report category cards
- Quick stats dashboard
- Filter controls
- Report preview
- Export functionality

### 2. ReportCard.js
Displays report categories with:
- Category icons and descriptions
- Available reports list
- Quick stats preview
- Expandable report list

### 3. ReportFilters.js
Advanced filtering component with:
- Date range selection
- Employee/location filters
- Department/incident type filters
- Active filter display
- Reset/apply functionality

### 4. ReportPreview.js
Report visualization component with:
- Chart rendering (using Recharts)
- Table display
- View mode toggle (chart/table)
- Export buttons
- Summary statistics

## Redux Store

### Report Slice Structure
```javascript
const reportSlice = {
  // Report data
  attendance: null,
  incidents: null,
  performance: null,
  compliance: null,
  
  // Loading states
  loading: false,
  generating: false,
  exporting: false,
  
  // Error handling
  error: null,
  
  // Generated reports history
  generatedReports: [],
  
  // Current filters
  currentFilters: {}
};
```

### Async Thunks
- `fetchAttendanceData`: Load attendance report data
- `fetchIncidentData`: Load incident report data
- `fetchPerformanceData`: Load performance report data
- `fetchComplianceData`: Load compliance report data
- `generateReport`: Generate formatted report
- `exportReport`: Export report to file

## Export Functionality

### PDF Export
Uses jsPDF and html2canvas for:
- Professional formatting
- Charts and tables
- Branded headers
- Page breaks
- Custom styling

### CSV Export
Uses Papa Parse for:
- Raw data export
- Custom field mapping
- UTF-8 encoding
- Excel compatibility

### Excel Export
Uses SheetJS for:
- Multiple worksheets
- Formatted cells
- Charts and graphs
- Custom styling

## Configuration

### Environment Variables
```bash
# Report generation
REPORT_GENERATION_ENABLED=true
REPORT_STORAGE_PATH=/reports
REPORT_RETENTION_DAYS=30

# Export settings
PDF_EXPORT_ENABLED=true
CSV_EXPORT_ENABLED=true
EXCEL_EXPORT_ENABLED=true

# Chart library
CHART_LIBRARY=recharts
```

### Tenant Configuration
```javascript
const tenantConfig = {
  reports: {
    enabled: true,
    categories: ['attendance', 'incidents', 'performance', 'compliance'],
    exportFormats: ['pdf', 'csv', 'excel'],
    retentionDays: 30,
    maxFileSize: '10MB'
  }
};
```

## Role-Based Access

### Report Access by Role
- **Super Admin**: All reports and exports
- **Client Admin**: All reports and exports for their tenant
- **Dispatcher**: Attendance, incidents, performance reports
- **Supervisor**: Limited access to performance reports

### Export Permissions
- **PDF Export**: Admin, Client Admin, Dispatcher
- **CSV Export**: Admin, Client Admin, Dispatcher
- **Excel Export**: Admin, Client Admin

## Integration Points

### 1. WebSocket Integration
Real-time updates for:
- Live attendance data
- Incident notifications
- Performance metrics
- Compliance alerts

### 2. Messaging Integration
Report sharing via:
- In-app messaging
- Email notifications
- Scheduled reports

### 3. Notification Integration
Real-time alerts for:
- Report generation completion
- Export failures
- Data anomalies

## Performance Considerations

### 1. Data Caching
- Cache report data for 5 minutes
- Implement pagination for large datasets
- Use lazy loading for charts

### 2. Export Optimization
- Background report generation
- Progress indicators
- File size limits
- Compression for large reports

### 3. Chart Rendering
- Virtual scrolling for large datasets
- Debounced filter updates
- Optimized chart configurations

## Security

### 1. Data Access
- Tenant isolation for all reports
- Role-based data filtering
- Audit logging for report access

### 2. Export Security
- File type validation
- Size limits
- Virus scanning
- Secure file storage

### 3. API Security
- Rate limiting
- Input validation
- SQL injection prevention
- XSS protection

## Troubleshooting

### Common Issues

1. **Charts Not Rendering**
   - Check Recharts installation
   - Verify data structure
   - Check console for errors

2. **Export Fails**
   - Verify file permissions
   - Check storage space
   - Validate export format

3. **Slow Performance**
   - Implement data pagination
   - Add loading states
   - Optimize queries

4. **Filter Not Working**
   - Check API endpoint
   - Verify filter parameters
   - Test with minimal filters

### Debug Mode
```javascript
// Enable debug logging
const DEBUG_MODE = process.env.NODE_ENV === 'development';

if (DEBUG_MODE) {
  console.log('Report data:', data);
  console.log('Filters:', filters);
}
```

## Future Enhancements

### Planned Features
1. **Scheduled Reports**: Automated report generation
2. **Custom Dashboards**: User-defined report layouts
3. **Advanced Analytics**: Machine learning insights
4. **Mobile Reports**: Responsive report viewing
5. **Report Templates**: Pre-built report formats
6. **Data Visualization**: Interactive charts and graphs
7. **Report Sharing**: Collaborative report features
8. **API Integration**: Third-party data sources

### Analytics Features
1. **Predictive Analytics**: Trend forecasting
2. **Anomaly Detection**: Automatic alerting
3. **Performance Scoring**: Automated evaluations
4. **Benchmarking**: Industry comparisons
5. **ROI Analysis**: Cost-benefit calculations

This comprehensive reporting and analytics module provides powerful insights for managing OnTheMove operations with multi-tenant support, role-based access, and extensive export capabilities. 