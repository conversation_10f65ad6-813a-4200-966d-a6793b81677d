# Architecture Overview

This document provides a comprehensive overview of the FootOnStreet platform architecture, including system design, data flow, and technical decisions.

## 🏗️ System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   Mobile App    │    │   Admin Panel   │
│   (Employee)    │    │   (Employee)    │    │   (Managers)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Load Balancer │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Web Server    │
                    │   (Express.js)  │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │   Redis Cache   │    │   File Storage  │
│   Database      │    │   (Sessions)    │    │   (Media)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

**Frontend:**
- React 19.1.0
- Redux Toolkit
- React Router DOM
- Tailwind CSS
- Mapbox GL JS
- Socket.IO Client

**Backend:**
- Node.js
- Express.js
- PostgreSQL
- JWT Authentication
- Socket.IO
- Multer (File Upload)

**DevOps & Infrastructure:**
- Docker
- Nginx (Reverse Proxy)
- Redis (Caching)
- PM2 (Process Manager)

## 🔐 Security Architecture

### Authentication & Authorization

```
┌─────────────────┐
│   Client Login  │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   JWT Creation  │
│   (Server)      │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   Token Storage │
│   (LocalStorage)│
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   API Requests  │
│   (With Token)  │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   Middleware    │
│   Validation    │
└─────────────────┘
```

**Security Layers:**
1. **Authentication**: JWT tokens with expiration
2. **Authorization**: Role-based access control (RBAC)
3. **Input Validation**: Express-validator middleware
4. **Rate Limiting**: IP-based request limiting
5. **Data Encryption**: Bcrypt for passwords, HTTPS for transmission
6. **CORS Protection**: Cross-origin request security

### Multi-tenant Security

```
┌─────────────────┐
│   Organization  │
│   Context       │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   Data Isolation│
│   (Org ID)      │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   Permission    │
│   Checking      │
└─────────────────┘
```

## 📊 Data Architecture

### Database Schema

```sql
-- Core Tables
organizations (id, name, domain, settings, created_at, updated_at)
users (id, email, password_hash, first_name, last_name, role, organization_id, created_at, updated_at)
locations (id, name, address, coordinates, contact_details, organization_id, created_at, updated_at)
assignments (id, user_id, location_id, start_time, end_time, status, notes, created_at, updated_at)
incidents (id, title, description, severity, status, reported_by, location_id, coordinates, media, organization_id, created_at, updated_at)
geofences (id, name, type, coordinates, radius, alert_type, is_active, organization_id, created_at, updated_at)
messages (id, sender_id, recipient_id, content, timestamp, is_read, organization_id)
attendance_logs (id, user_id, location_id, check_in, check_out, hours_worked, status, created_at)
```

### Data Relationships

```
Organizations (1:N) Users
Organizations (1:N) Locations
Organizations (1:N) Incidents
Organizations (1:N) Geofences
Organizations (1:N) Messages
Users (1:N) Assignments
Users (1:N) Incidents (reported_by)
Users (1:N) Attendance_logs
Locations (1:N) Assignments
Locations (1:N) Incidents
```

### Data Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Side   │───▶│   API Layer     │───▶│   Database      │
│   (React)       │    │   (Express)     │    │   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   Validation    │              │
         │              │   & Business    │              │
         │              │   Logic         │              │
         │              └─────────────────┘              │
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         └──────────────│   Response      │◄─────────────┘
                        │   Formatting    │
                        └─────────────────┘
```

## 🌐 Multi-tenant Architecture

### Tenant Isolation

**Data Isolation:**
- Each organization has isolated data through `organization_id`
- Database queries always include organization context
- No cross-tenant data access

**Branding Isolation:**
- Custom themes per organization
- Logo and color customization
- White-label ready design

**Feature Isolation:**
- Configurable features per organization
- Role-based access within organizations
- Custom settings per tenant

### Tenant Context Flow

```
┌─────────────────┐
│   User Login    │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   Organization  │
│   Detection     │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   Tenant Context│
│   Provider      │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   Branding      │
│   Application   │
└─────────────────┘
```

## 📱 Real-time Architecture

### WebSocket Communication

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Web Client    │    │   Admin Panel   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Socket.IO     │
                    │   Server        │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Location      │    │   Messaging     │    │   Incidents     │
│   Updates       │    │   System        │    │   & Alerts      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Real-time Features:**
- Live GPS tracking
- Instant messaging
- Incident notifications
- Geofence alerts
- Status updates

### Event-Driven Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Event Source  │───▶│   Event Bus     │───▶│   Event Handler │
│   (Client)      │    │   (Socket.IO)   │    │   (Broadcast)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │   Connected     │
                        │   Clients       │
                        └─────────────────┘
```

## 🗺️ Geospatial Architecture

### Location Processing

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GPS Data      │───▶│   Validation    │───▶│   Storage       │
│   (Lat, Lng)    │    │   & Processing  │    │   (PostGIS)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   Geofence      │              │
         │              │   Checking      │              │
         │              └─────────────────┘              │
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         └──────────────│   Alert         │◄─────────────┘
                        │   Processing    │
                        └─────────────────┘
```

**Geospatial Features:**
- Real-time location tracking
- Geofence monitoring
- Route history
- Location-based analytics

### Mapping Integration

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mapbox API    │    │   Application   │    │   Location Data │
│   (Map Tiles)   │◄───│   Layer         │───▶│   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   Interactive   │              │
         │              │   Map UI        │              │
         │              └─────────────────┘              │
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         └──────────────│   User          │◄─────────────┘
                        │   Interaction   │
                        └─────────────────┘
```

## 🔄 State Management Architecture

### Redux Store Structure

```
┌─────────────────┐
│   Redux Store   │
├─────────────────┤
│   auth          │ ← User authentication state
│   user          │ ← User management state
│   location      │ ← Location management state
│   assignment    │ ← Assignment management state
│   incident      │ ← Incident management state
│   geofence      │ ← Geofence management state
│   message       │ ← Messaging state
│   ui            │ ← UI state (modals, loading, etc.)
│   tenant        │ ← Multi-tenant state
└─────────────────┘
```

### State Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Component     │───▶│   Action        │───▶│   Reducer       │
│   (Dispatch)    │    │   (Type/Payload)│    │   (State Update)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                                             │
         │                                             ▼
         │                                    ┌─────────────────┐
         │                                    │   Store         │
         │                                    │   (New State)   │
         │                                    └─────────────────┘
         │                                             │
         │                                             ▼
         │                                    ┌─────────────────┐
         └────────────────────────────────────│   Component     │
                                              │   (Re-render)   │
                                              └─────────────────┘
```

## 🚀 Performance Architecture

### Caching Strategy

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Cache  │    │   CDN Cache     │    │   Server Cache  │
│   (React Query) │    │   (Static Files)│    │   (Redis)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Database      │
                    │   (PostgreSQL)  │
                    └─────────────────┘
```

**Performance Optimizations:**
- React Query for client-side caching
- Redis for server-side caching
- CDN for static assets
- Database query optimization
- Lazy loading for components

### Scalability Considerations

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   App Server 1  │    │   App Server 2  │
│   (Nginx)       │───▶│   (Express)     │    │   (Express)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       └───────────────────────┘
         │                                 │
         ▼                                 ▼
┌─────────────────┐                ┌─────────────────┐
│   Database      │                │   Redis Cluster │
│   (PostgreSQL)  │                │   (Cache)       │
└─────────────────┘                └─────────────────┘
```

## 🛡️ Error Handling Architecture

### Error Boundary Pattern

```
┌─────────────────┐
│   App Component │
├─────────────────┤
│   Error         │
│   Boundary      │
├─────────────────┤
│   Route         │
│   Components    │
├─────────────────┤
│   Page          │
│   Components    │
├─────────────────┤
│   Child         │
│   Components    │
└─────────────────┘
```

### Error Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Component     │───▶│   Error Occurs  │───▶│   Error         │
│   Error         │    │   (Try/Catch)   │    │   Boundary      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   Error         │              │
         │              │   Logging       │              │
         │              └─────────────────┘              │
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         └──────────────│   Fallback UI   │◄─────────────┘
                        │   Display       │
                        └─────────────────┘
```

## 📊 Monitoring & Observability

### Logging Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│   Morgan        │───▶│   Log Files     │
│   Logs          │    │   (HTTP Logger) │    │   (JSON)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   Winston       │              │
         │              │   (App Logger)  │              │
         │              └─────────────────┘              │
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         └──────────────│   Centralized   │◄─────────────┘
                        │   Logging       │
                        └─────────────────┘
```

### Health Monitoring

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Health Check  │───▶│   Database      │    │   External APIs │
│   Endpoint      │    │   Connection    │    │   (Mapbox, etc) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   Service       │              │
         │              │   Health        │              │
         │              └─────────────────┘              │
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         └──────────────│   Health        │◄─────────────┘
                        │   Response      │
                        └─────────────────┘
```

## 🔄 CI/CD Architecture

### Deployment Pipeline

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Git Push      │───▶│   CI/CD         │───▶│   Build Process │
│   (GitHub)      │    │   (GitHub       │    │   (Docker)      │
│                 │    │   Actions)      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   Testing       │              │
         │              │   (Jest, RTL)   │              │
         │              └─────────────────┘              │
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         └──────────────│   Deployment    │◄─────────────┘
                        │   (Production)  │
                        └─────────────────┘
```

This architecture provides a solid foundation for a scalable, secure, and maintainable employee tracking and safety management platform.