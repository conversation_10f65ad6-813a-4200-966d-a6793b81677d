import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  <PERSON>Axis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import { Download, Eye, FileText, BarChart3, TrendingUp } from 'lucide-react';
import BrandedButton from '../BrandedButton';

const ReportPreview = ({ report, data, loading }) => {
  const [viewMode, setViewMode] = useState('chart'); // 'chart' or 'table'

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d'];

  const renderAttendanceChart = () => {
    if (!data?.attendanceData) return null;

    return (
      <div className="space-y-6">
        {/* Daily Attendance Trend */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Daily Attendance Trend</h4>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={data.attendanceData.dailyTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="attendance" stroke="#3b82f6" strokeWidth={2} />
              <Line type="monotone" dataKey="expected" stroke="#6b7280" strokeWidth={2} strokeDasharray="5 5" />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Attendance by Department */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Attendance by Department</h4>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data.attendanceData.byDepartment}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="department" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="attendance" fill="#3b82f6" />
              <Bar dataKey="expected" fill="#6b7280" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  };

  const renderIncidentChart = () => {
    if (!data?.incidentData) return null;

    return (
      <div className="space-y-6">
        {/* Incident Trends */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Incident Trends</h4>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data.incidentData.trends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area type="monotone" dataKey="incidents" stroke="#ef4444" fill="#fef2f2" />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Incident by Type */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Incidents by Type</h4>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={data.incidentData.byType}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {data.incidentData.byType.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Resolution Times */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Average Resolution Times</h4>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data.incidentData.resolutionTimes}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="type" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="hours" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  };

  const renderPerformanceChart = () => {
    if (!data?.performanceData) return null;

    return (
      <div className="space-y-6">
        {/* Performance Metrics */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Performance Metrics</h4>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data.performanceData.metrics}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="employee" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="productivity" fill="#3b82f6" />
              <Bar dataKey="quality" fill="#10b981" />
              <Bar dataKey="efficiency" fill="#f59e0b" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Performance Trends */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Performance Trends</h4>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={data.performanceData.trends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="average" stroke="#3b82f6" strokeWidth={2} />
              <Line type="monotone" dataKey="target" stroke="#ef4444" strokeWidth={2} strokeDasharray="5 5" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  };

  const renderComplianceChart = () => {
    if (!data?.complianceData) return null;

    return (
      <div className="space-y-6">
        {/* Compliance Score */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Compliance Score</h4>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data.complianceData.scores}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="score" fill="#8b5cf6" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Violations by Type */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Violations by Type</h4>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={data.complianceData.violations}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {data.complianceData.violations.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  };

  const renderTable = () => {
    if (!data?.tableData) return null;

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {data.tableData.headers.map((header, index) => (
                  <th
                    key={index}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.tableData.rows.map((row, rowIndex) => (
                <tr key={rowIndex} className="hover:bg-gray-50">
                  {row.map((cell, cellIndex) => (
                    <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderChart = () => {
    switch (report.categoryId) {
      case 'attendance':
        return renderAttendanceChart();
      case 'incidents':
        return renderIncidentChart();
      case 'performance':
        return renderPerformanceChart();
      case 'compliance':
        return renderComplianceChart();
      default:
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-500">No chart data available</p>
          </div>
        );
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600">Generating report...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              {report.title}
            </h3>
            <p className="text-sm text-gray-600">{report.description}</p>
          </div>
          <div className="flex items-center space-x-2">
            {/* View Mode Toggle */}
            <div className="flex items-center space-x-1 bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => setViewMode('chart')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'chart'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <BarChart3 className="h-4 w-4 inline mr-1" />
                Chart
              </button>
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'table'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <FileText className="h-4 w-4 inline mr-1" />
                Table
              </button>
            </div>

            {/* Export Button */}
            <BrandedButton
              onClick={() => {/* Handle export */}}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export
            </BrandedButton>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {viewMode === 'chart' ? renderChart() : renderTable()}
      </div>

      {/* Summary Stats */}
      {data?.summary && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Summary</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(data.summary).map(([key, value]) => (
              <div key={key} className="text-center">
                <p className="text-lg font-semibold text-gray-900">{value}</p>
                <p className="text-xs text-gray-500 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportPreview; 