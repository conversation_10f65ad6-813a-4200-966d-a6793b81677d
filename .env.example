# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=onthemove
DB_USER=postgres
DB_PASSWORD=sysadmin

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Mapbox Configuration (Optional)
REACT_APP_MAPBOX_ACCESS_TOKEN=your-mapbox-access-token-here

# API Configuration
REACT_APP_API_URL=http://localhost:3001/api

# File Upload Configuration
UPLOAD_MAX_SIZE=10mb
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,video/mp4

# Messaging Configuration (Optional)
REACT_APP_MESSAGING_ENABLED=true
REACT_APP_MAX_MESSAGE_LENGTH=1000
REACT_APP_MAX_ATTACHMENT_SIZE=10485760

# Notification Configuration (Optional)
REACT_APP_NOTIFICATIONS_ENABLED=true
REACT_APP_NOTIFICATION_SOUND_ENABLED=true
REACT_APP_DESKTOP_NOTIFICATIONS_ENABLED=true

# WebSocket Configuration (Optional)
REACT_APP_WEBSOCKET_URL=ws://localhost:3001

# Email Configuration (Optional)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Production Settings
# NODE_ENV=production
# DATABASE_URL=postgresql://username:password@host:port/database
