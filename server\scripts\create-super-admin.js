const bcrypt = require('bcryptjs');
const { pool } = require('../config/database');

async function createSuperAdmin() {
  try {
    console.log('Creating super admin user...');
    
    // Check if super admin already exists
    const existingAdmin = await pool.query(
      'SELECT id FROM users WHERE email = $1 AND role = $2',
      ['<EMAIL>', 'super_admin']
    );
    
    if (existingAdmin.rows.length > 0) {
      console.log('Super admin user already exists');
      return;
    }
    
    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    // Create super admin user
    const result = await pool.query(`
      INSERT INTO users (
        organization_id,
        email,
        password_hash,
        first_name,
        last_name,
        role,
        status,
        created_at,
        updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING id, email, role
    `, [
      null, // Super admin doesn't belong to any organization
      '<EMAIL>',
      hashedPassword,
      'Super',
      'Admin',
      'super_admin',
      'active'
    ]);
    
    console.log('Super admin user created successfully:', result.rows[0]);
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    
  } catch (error) {
    console.error('Error creating super admin:', error);
  } finally {
    await pool.end();
  }
}

// Run the script
createSuperAdmin();
