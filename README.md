# OnTheMove - Employee Tracking & Safety Management Platform

**OnTheMove** is a modern, white-labeled SaaS platform designed for real-time employee tracking, safety management, and workforce well-being. The system serves multiple clients with customizable branding and supports field teams such as security guards, delivery agents, and on-ground staff.

## 🚀 Features

### Core Functionality
- **Real-time GPS Tracking** - Live employee location monitoring with interactive maps
- **Time & Attendance Management** - Automated attendance tracking with comprehensive logging
- **Assignment Management** - Assign employees to specific locations and shifts
- **Incident Management** - Report, track, and manage safety incidents
- **Geofencing** - Set up safety zones with automatic alerts for breaches
- **Communication Tools** - Built-in messaging system for admin-to-employee communication
- **Reporting & Analytics** - Comprehensive reports on attendance, safety, and performance

### Advanced Features
- **White-label Ready** - Customizable branding for each client
- **Multi-tenant Architecture** - Support for multiple organizations
- **Role-based Access Control** - Secure, hierarchical user permissions
- **Real-time Notifications** - Instant alerts for critical events
- **Mobile-responsive Design** - Works seamlessly across all devices
- **Panic Button Integration** - Emergency safety features for field workers

## 🏗️ Architecture

### Frontend (React)
- **Framework**: React 19.1.0 with Redux Toolkit for state management
- **Styling**: Tailwind CSS for responsive, modern UI
- **Maps**: Mapbox GL JS for interactive mapping
- **Forms**: React Hook Form with Zod validation
- **Routing**: React Router DOM for SPA navigation
- **Charts**: Recharts for data visualization

### Backend (Node.js)
- **Framework**: Express.js with RESTful API design
- **Database**: PostgreSQL with connection pooling
- **Authentication**: JWT-based authentication with bcrypt
- **Security**: Helmet, CORS, rate limiting, and input validation
- **Real-time**: Socket.IO for live updates
- **File Upload**: React Dropzone for media handling

### Database
- **PostgreSQL** - Primary database for all application data
- **Connection Pooling** - Efficient database connection management
- **Migrations** - Automated database schema management

## 🛠️ Quick Start

### Prerequisites
- Node.js 16+ 
- npm or yarn
- PostgreSQL 12+

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd onthemove-admin
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Set up the database**
```bash
# Follow the DATABASE_SETUP.md guide
npm run db:setup
```

5. **Start the development server**
```bash
# Start both frontend and backend
npm run start:dev

# Or start them separately
npm run start:server  # Backend on port 3001
npm start            # Frontend on port 3000
```

The application will be available at `http://localhost:3000`

## 📚 Documentation

### Setup Guides
- [Database Setup](DATABASE_SETUP.md) - PostgreSQL configuration and initialization
- [Map Setup](MAP_SETUP.md) - Mapbox integration and configuration
- [Geofencing Setup](GEOFENCING_SETUP.md) - Geofence configuration and management
- [Incident Management Setup](INCIDENT_SETUP.md) - Incident reporting system
- [Messaging Setup](MESSAGING_SETUP.md) - Real-time communication setup
- [Reporting Setup](REPORTING_SETUP.md) - Analytics and reporting configuration

### Additional Documentation
- [Requirements](REQUIREMENTS.md) - Complete project requirements and specifications
- [Bug Fixes Summary](BUG_FIXES_SUMMARY.md) - Recent bug fixes and improvements
- [API Documentation](API_DOCUMENTATION.md) - Complete API reference
- [Development Guide](DEVELOPMENT_GUIDE.md) - Development setup and contribution guidelines
- [Architecture Overview](ARCHITECTURE_OVERVIEW.md) - Technical architecture details

## 🔧 Available Scripts

- `npm start` - Start the React development server
- `npm run start:server` - Start the Express backend server
- `npm run start:dev` - Start both frontend and backend concurrently
- `npm run build` - Build the React app for production
- `npm test` - Run the test suite
- `npm run eject` - Eject from Create React App (⚠️ irreversible)

## 🌟 Key Features by Role

### Super Admin
- Manage multiple organizations
- Configure white-label branding
- System-wide analytics and monitoring
- User management across organizations

### Organization Admin
- Employee management and onboarding
- Location and assignment management
- Incident tracking and resolution
- Custom reporting and analytics
- Real-time monitoring dashboard

### Dispatcher/Supervisor
- Live employee tracking
- Assignment management
- Incident response
- Communication tools
- Attendance monitoring

## 🛡️ Security Features

- **Authentication**: JWT-based secure login system
- **Authorization**: Role-based access control (RBAC)
- **Data Encryption**: Secure data transmission and storage
- **Rate Limiting**: Protection against API abuse
- **Input Validation**: Comprehensive input sanitization
- **CORS Protection**: Secure cross-origin request handling

## 🌍 Multi-tenant & White-label

OnTheMove supports multiple organizations with:
- **Custom Branding**: Logo, colors, and themes per organization
- **Isolated Data**: Complete data separation between organizations
- **Flexible Configuration**: Organization-specific settings and features
- **Scalable Architecture**: Designed to support hundreds of organizations

## 📊 Real-time Features

- **Live GPS Tracking**: Real-time employee location updates
- **Instant Notifications**: Push notifications for critical events
- **Live Chat**: Real-time messaging between admins and employees
- **Dynamic Maps**: Live updates on interactive maps
- **Status Updates**: Real-time employee status changes

## 🔗 API Overview

The backend provides RESTful APIs with the following main endpoints:

- `/api/auth` - Authentication and authorization
- `/api/organizations` - Organization management
- `/api/organizations/:id/users` - User management
- `/api/organizations/:id/locations` - Location management
- `/api/organizations/:id/assignments` - Assignment management
- `/api/organizations/:id/incidents` - Incident management
- `/api/organizations/:id/reports` - Reporting and analytics
- `/api/organizations/:id/messages` - Messaging system
- `/api/organizations/:id/geofences` - Geofencing management

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Environment Variables
Ensure all required environment variables are set:
- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET` - Secret key for JWT tokens
- `MAPBOX_ACCESS_TOKEN` - Mapbox API access token
- `FRONTEND_URL` - Frontend application URL
- `NODE_ENV` - Environment (production/development)

### Health Check
The server provides a health check endpoint at `/health` to monitor application status.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

## 📞 Support

For support and questions, please contact the development team or refer to the documentation guides listed above.

---

**OnTheMove** - Empowering safe and efficient workforce management through technology.
