-- Create subscription_plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  monthly_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  annual_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  per_user_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  max_users INTEGER NOT NULL DEFAULT 0,
  max_locations INTEGER NOT NULL DEFAULT 0,
  max_monthly_tracked_hours INTEGER DEFAULT NULL,
  max_storage_gb INTEGER DEFAULT NULL,
  max_api_calls_per_hour INTEGER DEFAULT NULL,
  features JSONB DEFAULT '[]',
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert default subscription plans
INSERT INTO subscription_plans (
  name, display_name, description, monthly_price, annual_price, per_user_fee, 
  max_users, max_locations, max_monthly_tracked_hours, max_storage_gb, 
  max_api_calls_per_hour, features, sort_order
) VALUES 
(
  'basic', 
  'Basic Plan', 
  'Perfect for small teams getting started with employee tracking',
  29.99, 
  299.99, 
  5.00,
  10, 
  3, 
  1000, 
  5, 
  500,
  '["Basic location tracking", "Employee management", "Basic reporting", "Email support"]',
  1
),
(
  'standard', 
  'Standard Plan', 
  'Ideal for growing businesses with advanced tracking needs',
  79.99, 
  799.99, 
  4.00,
  50, 
  10, 
  5000, 
  25, 
  2000,
  '["Advanced location tracking", "Employee management", "Advanced reporting", "Geofencing", "Real-time alerts", "Priority email support"]',
  2
),
(
  'premium', 
  'Premium Plan', 
  'Comprehensive solution for larger organizations',
  149.99, 
  1499.99, 
  3.50,
  200, 
  25, 
  20000, 
  100, 
  5000,
  '["Premium location tracking", "Advanced employee management", "Custom reporting", "Advanced geofencing", "Real-time alerts", "API access", "Phone support", "Custom integrations"]',
  3
),
(
  'enterprise', 
  'Enterprise Plan', 
  'Unlimited solution for large enterprises with custom requirements',
  299.99, 
  2999.99, 
  3.00,
  999999, 
  999999, 
  NULL, 
  NULL, 
  NULL,
  '["Unlimited location tracking", "Advanced employee management", "Custom reporting", "Advanced geofencing", "Real-time alerts", "Full API access", "24/7 phone support", "Custom integrations", "Dedicated account manager", "Custom features"]',
  4
) ON CONFLICT (name) DO NOTHING;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_subscription_plans_name ON subscription_plans(name);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_active ON subscription_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_sort_order ON subscription_plans(sort_order);
