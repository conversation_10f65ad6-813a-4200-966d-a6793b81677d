# Database Schema Documentation

## Overview

This database schema is designed for a multi-tenant security tracking and management system. It supports organizations managing security personnel, locations, assignments, incidents, and real-time tracking.

## Schema Files

- `full_schema.sql` - Complete database schema with all tables, indexes, triggers, and views
- `organizations_schema.sql` - Enhanced organizations table schema
- `schema.sql` - Original basic schema (legacy)

## Core Tables

### 1. Organizations
The central table for multi-tenancy. Each organization represents a client company using the system.

**Key Features:**
- Comprehensive business information (legal name, tax ID, registration details)
- Billing and subscription management
- Service limits and feature flags
- Compliance and security settings
- Custom branding support

### 2. Users
All system users including employees, managers, and administrators.

**Key Features:**
- Role-based access control
- Employee management (hire date, termination, emergency contacts)
- Session management
- User preferences and settings

### 3. Locations
Physical locations that organizations manage and monitor.

**Key Features:**
- Detailed address information
- Geographic coordinates
- Security levels and operating hours
- Contact information
- Zone management

### 4. Assignments
Work assignments and shifts for security personnel.

**Key Features:**
- Shift scheduling with start/end times
- Assignment types (patrol, guard, maintenance)
- Priority levels and status tracking
- Check-in/check-out times
- Recurring assignment support

### 5. User Locations
Real-time location tracking data.

**Key Features:**
- GPS coordinates with accuracy
- Activity type detection
- Battery level monitoring
- Assignment linking
- Historical tracking

### 6. Incidents
Security incidents and events.

**Key Features:**
- Incident categorization and severity
- Assignment and resolution tracking
- Cost estimation and tracking
- File attachments
- Status updates and comments

### 7. Geofences
Virtual boundaries for location-based alerts.

**Key Features:**
- Multiple geofence types (circular, polygon, route)
- Entry/exit alerts
- User restrictions and permissions
- Complex coordinate systems

### 8. Messages & Communications
Internal communication system.

**Key Features:**
- Direct messaging between users
- Broadcast messages to roles/locations
- Message priorities and status
- File attachments

## Additional Tables

### Audit & Logging
- `audit_logs` - Comprehensive audit trail
- `system_logs` - System-level logging
- `user_sessions` - Session management

### Reporting & Analytics
- `reports` - Report generation and scheduling
- `notifications` - System notifications

### Branding & Customization
- `organization_branding` - Custom branding per organization

## Key Features

### Multi-Tenancy
- Complete data isolation between organizations
- Organization-specific settings and limits
- Custom branding per organization

### Security
- Role-based access control
- Session management
- Audit logging
- Password policies
- Two-factor authentication support

### Scalability
- Comprehensive indexing strategy
- Efficient query optimization
- JSONB fields for flexible data storage
- Partitioning-ready structure

### Compliance
- Audit trail for all actions
- Data retention policies
- Compliance requirement tracking
- Security level management

## Database Views

### 1. `active_organizations`
Shows only active organizations with key metrics.

### 2. `organization_summary`
Provides organization statistics including user count, location count, and incident count.

### 3. `user_activity_summary`
Shows user activity metrics and engagement statistics.

## Indexes

The schema includes comprehensive indexing for:
- Primary keys and foreign keys
- Frequently queried fields (email, status, dates)
- Search fields (names, descriptions)
- Performance-critical queries

## Triggers

### Automatic Timestamps
All tables with `updated_at` columns automatically update timestamps on record changes.

### Data Validation
- Email uniqueness validation
- Organization limits validation
- Custom business rule enforcement

## Usage Examples

### Creating a New Organization
```sql
INSERT INTO organizations (
    name, 
    email, 
    contact_person, 
    subscription_plan,
    max_users,
    max_locations
) VALUES (
    'Acme Security Corp',
    '<EMAIL>',
    'John Smith',
    'premium',
    500,
    50
);
```

### Adding a User
```sql
INSERT INTO users (
    organization_id,
    email,
    password_hash,
    first_name,
    last_name,
    role
) VALUES (
    'org-uuid-here',
    '<EMAIL>',
    'hashed-password',
    'Jane',
    'Doe',
    'employee'
);
```

### Creating an Assignment
```sql
INSERT INTO assignments (
    organization_id,
    user_id,
    location_id,
    shift_start,
    shift_end,
    assignment_type
) VALUES (
    'org-uuid-here',
    'user-uuid-here',
    'location-uuid-here',
    '2024-01-15 08:00:00+00',
    '2024-01-15 16:00:00+00',
    'patrol'
);
```

## Migration Strategy

### From Existing Schema
If you have an existing `tenants` table, you can migrate to the new `organizations` table:

```sql
-- Create organizations table
-- Copy data from tenants to organizations
INSERT INTO organizations (
    id, name, email, phone, address, contact_person, 
    contact_email, contact_phone, subscription_plan, 
    billing_address, payment_method, max_users, 
    max_locations, status, created_at, updated_at
)
SELECT 
    id, name, email, phone, address, contact_person,
    contact_email, contact_phone, subscription_plan,
    billing_address, payment_method, max_users,
    max_locations, status, created_at, updated_at
FROM tenants;

-- Update foreign key references
ALTER TABLE users ADD COLUMN organization_id UUID;
UPDATE users SET organization_id = tenant_id;
ALTER TABLE users DROP COLUMN tenant_id;
ALTER TABLE users ADD CONSTRAINT fk_users_organization 
    FOREIGN KEY (organization_id) REFERENCES organizations(id);
```

## Performance Considerations

### Large Datasets
- Consider partitioning for high-volume tables (user_locations, audit_logs)
- Implement data archiving strategies
- Use appropriate index strategies for time-series data

### Query Optimization
- Use the provided views for common queries
- Leverage JSONB indexes for complex queries
- Consider materialized views for heavy analytics

## Security Best Practices

1. **Always use parameterized queries** to prevent SQL injection
2. **Implement row-level security** for multi-tenant data isolation
3. **Regular security audits** using the audit_logs table
4. **Encrypt sensitive data** at the application level
5. **Use connection pooling** for better performance

## Backup and Recovery

### Recommended Backup Strategy
- Daily full backups
- Hourly transaction log backups
- Point-in-time recovery capability
- Cross-region backup replication

### Data Retention
- Implement data retention policies
- Archive old data to separate storage
- Regular cleanup of expired sessions and logs

## Monitoring and Maintenance

### Key Metrics to Monitor
- Database size and growth
- Query performance
- Index usage
- Connection pool utilization
- Audit log volume

### Regular Maintenance
- Update statistics weekly
- Rebuild indexes monthly
- Clean up old sessions and logs
- Monitor and optimize slow queries

## Support and Documentation

For questions about the schema or implementation:
1. Check the inline comments in the SQL files
2. Review the table documentation
3. Use the provided views for common operations
4. Monitor the audit logs for system behavior

## Future Enhancements

Potential additions to consider:
- Advanced analytics tables
- Machine learning feature tables
- Integration with external systems
- Enhanced reporting capabilities
- Mobile app specific optimizations 