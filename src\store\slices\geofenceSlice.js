import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { geofenceAPI } from '../../services/api';

// Async thunks
export const fetchGeofences = createAsyncThunk(
  'geofences/fetchGeofences',
  async ({ organizationId }, { rejectWithValue }) => {
    try {
      const response = await geofenceAPI.getGeofences(organizationId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch geofences');
    }
  }
);

export const createGeofence = createAsyncThunk(
  'geofences/createGeofence',
  async ({ organizationId, geofence }, { rejectWithValue }) => {
    try {
      const response = await geofenceAPI.createGeofence(organizationId, geofence);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create geofence');
    }
  }
);

export const updateGeofence = createAsyncThunk(
  'geofences/updateGeofence',
  async ({ organizationId, geofence }, { rejectWithValue }) => {
    try {
      const response = await geofenceAPI.updateGeofence(organizationId, geofence.id, geofence);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update geofence');
    }
  }
);

export const deleteGeofence = createAsyncThunk(
  'geofences/deleteGeofence',
  async ({ organizationId, geofenceId }, { rejectWithValue }) => {
    try {
      await geofenceAPI.deleteGeofence(organizationId, geofenceId);
      return geofenceId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete geofence');
    }
  }
);

export const fetchGeofenceAlerts = createAsyncThunk(
  'geofences/fetchGeofenceAlerts',
  async ({ organizationId, filters = {} }, { rejectWithValue }) => {
    try {
      const response = await geofenceAPI.getGeofenceAlerts(organizationId, null, filters);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch geofence alerts');
    }
  }
);

export const acknowledgeAlert = createAsyncThunk(
  'geofences/acknowledgeAlert',
  async ({ organizationId, alertId }, { rejectWithValue }) => {
    try {
      const response = await geofenceAPI.acknowledgeAlert(organizationId, alertId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to acknowledge alert');
    }
  }
);

export const checkGeofenceViolation = createAsyncThunk(
  'geofences/checkGeofenceViolation',
  async ({ organizationId, employeeId, location }, { rejectWithValue }) => {
    try {
      const response = await geofenceAPI.checkViolation(organizationId, { employeeId, location });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to check geofence violation');
    }
  }
);

// Initial state
const initialState = {
  // Geofence data
  geofences: [],
  
  // Alert data
  alerts: [],
  
  // Loading states
  loading: false,
  creating: false,
  updating: false,
  deleting: false,
  alertsLoading: false,
  
  // Error state
  error: null,
  
  // Real-time state
  activeViolations: [],
  employeeLocations: {},
  
  // Filters and settings
  filters: {
    status: 'all',
    location: 'all',
    alertType: 'all'
  }
};

// Slice
const geofenceSlice = createSlice({
  name: 'geofences',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearGeofenceData: (state) => {
      state.geofences = [];
      state.alerts = [];
      state.activeViolations = [];
    },
    
    // Real-time updates
    updateEmployeeLocation: (state, action) => {
      const { employeeId, location, timestamp } = action.payload;
      state.employeeLocations[employeeId] = {
        location,
        timestamp,
        lastUpdated: new Date().toISOString()
      };
    },
    
    addGeofenceViolation: (state, action) => {
      const violation = action.payload;
      // Remove existing violation for this employee-geofence combination
      state.activeViolations = state.activeViolations.filter(
        v => !(v.employeeId === violation.employeeId && v.geofenceId === violation.geofenceId)
      );
      state.activeViolations.push({
        ...violation,
        timestamp: new Date().toISOString()
      });
    },
    
    removeGeofenceViolation: (state, action) => {
      const { employeeId, geofenceId } = action.payload;
      state.activeViolations = state.activeViolations.filter(
        v => !(v.employeeId === employeeId && v.geofenceId === geofenceId)
      );
    },
    
    addGeofenceAlert: (state, action) => {
      const alert = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        acknowledged: false
      };
      state.alerts.unshift(alert);
      
      // Keep only last 1000 alerts
      if (state.alerts.length > 1000) {
        state.alerts = state.alerts.slice(0, 1000);
      }
    },
    
    updateGeofenceAlert: (state, action) => {
      const { id, updates } = action.payload;
      const index = state.alerts.findIndex(alert => alert.id === id);
      if (index !== -1) {
        state.alerts[index] = { ...state.alerts[index], ...updates };
      }
    },
    
    // WebSocket event handlers
    handleGeofenceEntry: (state, action) => {
      const { employeeId, geofenceId, location, timestamp } = action.payload;
      
      // Add alert
      const geofence = state.geofences.find(g => g.id === geofenceId);
      const employee = state.employeeLocations[employeeId];
      
      if (geofence) {
        const alert = {
          id: Date.now().toString(),
          type: 'entry',
          severity: 'medium',
          employeeId,
          employeeName: employee?.name || 'Unknown Employee',
          geofenceId,
          geofenceName: geofence.name,
          location,
          timestamp,
          acknowledged: false,
          description: `${employee?.name || 'Employee'} entered ${geofence.name}`
        };
        
        state.alerts.unshift(alert);
      }
      
      // Update employee location
      state.employeeLocations[employeeId] = {
        location,
        timestamp,
        lastUpdated: new Date().toISOString()
      };
    },
    
    handleGeofenceExit: (state, action) => {
      const { employeeId, geofenceId, location, timestamp } = action.payload;
      
      // Add alert
      const geofence = state.geofences.find(g => g.id === geofenceId);
      const employee = state.employeeLocations[employeeId];
      
      if (geofence) {
        const alert = {
          id: Date.now().toString(),
          type: 'exit',
          severity: 'medium',
          employeeId,
          employeeName: employee?.name || 'Unknown Employee',
          geofenceId,
          geofenceName: geofence.name,
          location,
          timestamp,
          acknowledged: false,
          description: `${employee?.name || 'Employee'} exited ${geofence.name}`
        };
        
        state.alerts.unshift(alert);
      }
      
      // Update employee location
      state.employeeLocations[employeeId] = {
        location,
        timestamp,
        lastUpdated: new Date().toISOString()
      };
    },
    
    handleUnauthorizedEntry: (state, action) => {
      const { employeeId, geofenceId, location, timestamp } = action.payload;
      
      // Add critical alert
      const geofence = state.geofences.find(g => g.id === geofenceId);
      const employee = state.employeeLocations[employeeId];
      
      if (geofence) {
        const alert = {
          id: Date.now().toString(),
          type: 'unauthorized_entry',
          severity: 'critical',
          employeeId,
          employeeName: employee?.name || 'Unknown Employee',
          geofenceId,
          geofenceName: geofence.name,
          location,
          timestamp,
          acknowledged: false,
          description: `Unauthorized entry: ${employee?.name || 'Employee'} entered restricted area ${geofence.name}`
        };
        
        state.alerts.unshift(alert);
      }
    },
    
    handleUnauthorizedExit: (state, action) => {
      const { employeeId, geofenceId, location, timestamp } = action.payload;
      
      // Add critical alert
      const geofence = state.geofences.find(g => g.id === geofenceId);
      const employee = state.employeeLocations[employeeId];
      
      if (geofence) {
        const alert = {
          id: Date.now().toString(),
          type: 'unauthorized_exit',
          severity: 'critical',
          employeeId,
          employeeName: employee?.name || 'Unknown Employee',
          geofenceId,
          geofenceName: geofence.name,
          location,
          timestamp,
          acknowledged: false,
          description: `Unauthorized exit: ${employee?.name || 'Employee'} left restricted area ${geofence.name}`
        };
        
        state.alerts.unshift(alert);
      }
    },
    
    handleDwellTimeAlert: (state, action) => {
      const { employeeId, geofenceId, dwellTime, timestamp } = action.payload;
      
      // Add dwell time alert
      const geofence = state.geofences.find(g => g.id === geofenceId);
      const employee = state.employeeLocations[employeeId];
      
      if (geofence) {
        const alert = {
          id: Date.now().toString(),
          type: 'dwell_time',
          severity: 'high',
          employeeId,
          employeeName: employee?.name || 'Unknown Employee',
          geofenceId,
          geofenceName: geofence.name,
          timestamp,
          acknowledged: false,
          description: `${employee?.name || 'Employee'} has been in ${geofence.name} for ${dwellTime} minutes`
        };
        
        state.alerts.unshift(alert);
      }
    }
  },
  extraReducers: (builder) => {
    // Fetch Geofences
    builder
      .addCase(fetchGeofences.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchGeofences.fulfilled, (state, action) => {
        state.loading = false;
        state.geofences = action.payload;
      })
      .addCase(fetchGeofences.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Create Geofence
    builder
      .addCase(createGeofence.pending, (state) => {
        state.creating = true;
        state.error = null;
      })
      .addCase(createGeofence.fulfilled, (state, action) => {
        state.creating = false;
        state.geofences.push(action.payload);
      })
      .addCase(createGeofence.rejected, (state, action) => {
        state.creating = false;
        state.error = action.payload;
      });

    // Update Geofence
    builder
      .addCase(updateGeofence.pending, (state) => {
        state.updating = true;
        state.error = null;
      })
      .addCase(updateGeofence.fulfilled, (state, action) => {
        state.updating = false;
        const index = state.geofences.findIndex(g => g.id === action.payload.id);
        if (index !== -1) {
          state.geofences[index] = action.payload;
        }
      })
      .addCase(updateGeofence.rejected, (state, action) => {
        state.updating = false;
        state.error = action.payload;
      });

    // Delete Geofence
    builder
      .addCase(deleteGeofence.pending, (state) => {
        state.deleting = true;
        state.error = null;
      })
      .addCase(deleteGeofence.fulfilled, (state, action) => {
        state.deleting = false;
        state.geofences = state.geofences.filter(g => g.id !== action.payload);
      })
      .addCase(deleteGeofence.rejected, (state, action) => {
        state.deleting = false;
        state.error = action.payload;
      });

    // Fetch Geofence Alerts
    builder
      .addCase(fetchGeofenceAlerts.pending, (state) => {
        state.alertsLoading = true;
        state.error = null;
      })
      .addCase(fetchGeofenceAlerts.fulfilled, (state, action) => {
        state.alertsLoading = false;
        state.alerts = action.payload;
      })
      .addCase(fetchGeofenceAlerts.rejected, (state, action) => {
        state.alertsLoading = false;
        state.error = action.payload;
      });

    // Acknowledge Alert
    builder
      .addCase(acknowledgeAlert.fulfilled, (state, action) => {
        const index = state.alerts.findIndex(a => a.id === action.payload.id);
        if (index !== -1) {
          state.alerts[index] = { ...state.alerts[index], acknowledged: true };
        }
      })
      .addCase(acknowledgeAlert.rejected, (state, action) => {
        state.error = action.payload;
      });

    // Check Geofence Violation
    builder
      .addCase(checkGeofenceViolation.fulfilled, (state, action) => {
        const { violation, alert } = action.payload;
        
        if (violation) {
          // Add or update violation
          const existingIndex = state.activeViolations.findIndex(
            v => v.employeeId === violation.employeeId && v.geofenceId === violation.geofenceId
          );
          
          if (existingIndex !== -1) {
            state.activeViolations[existingIndex] = violation;
          } else {
            state.activeViolations.push(violation);
          }
        }
        
        if (alert) {
          state.alerts.unshift(alert);
        }
      });
  }
});

// Actions
export const {
  clearError,
  setFilters,
  clearGeofenceData,
  updateEmployeeLocation,
  addGeofenceViolation,
  removeGeofenceViolation,
  addGeofenceAlert,
  updateGeofenceAlert,
  handleGeofenceEntry,
  handleGeofenceExit,
  handleUnauthorizedEntry,
  handleUnauthorizedExit,
  handleDwellTimeAlert
} = geofenceSlice.actions;

// Selectors
export const selectGeofences = (state) => state.geofences.geofences;
export const selectGeofenceLoading = (state) => state.geofences.loading;
export const selectGeofenceError = (state) => state.geofences.error;
export const selectGeofenceAlerts = (state) => state.geofences.alerts;
export const selectActiveViolations = (state) => state.geofences.activeViolations;
export const selectEmployeeLocations = (state) => state.geofences.employeeLocations;
export const selectGeofenceFilters = (state) => state.geofences.filters;

// Helper selectors
export const selectGeofenceById = (state, geofenceId) => 
  state.geofences.geofences.find(g => g.id === geofenceId);

export const selectAlertsByGeofence = (state, geofenceId) =>
  state.geofences.alerts.filter(a => a.geofenceId === geofenceId);

export const selectUnacknowledgedAlerts = (state) =>
  state.geofences.alerts.filter(a => !a.acknowledged);

export const selectCriticalAlerts = (state) =>
  state.geofences.alerts.filter(a => a.severity === 'critical' && !a.acknowledged);

export default geofenceSlice.reducer; 