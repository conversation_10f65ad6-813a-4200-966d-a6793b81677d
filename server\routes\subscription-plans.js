const express = require('express');
const router = express.Router();
const { pool } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

// Get all subscription plans
router.get('/', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { active_only = 'false' } = req.query;
    
    let query = `
      SELECT * FROM subscription_plans
    `;
    
    if (active_only === 'true') {
      query += ` WHERE is_active = true`;
    }
    
    query += ` ORDER BY name`;
    
    const result = await pool.query(query);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching subscription plans:', error);
    res.status(500).json({ error: 'Failed to fetch subscription plans' });
  }
});

// Get subscription plan by ID
router.get('/:planId', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { planId } = req.params;
    
    const result = await pool.query(`
      SELECT * FROM subscription_plans WHERE id = $1
    `, [planId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Subscription plan not found' });
    }
    
    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching subscription plan:', error);
    res.status(500).json({ error: 'Failed to fetch subscription plan' });
  }
});

// Create new subscription plan
router.post('/', [
  authenticateToken,
  requireRole(['super_admin']),
  body('name').isLength({ min: 1 }).trim(),
  body('display_name').isLength({ min: 1 }).trim(),
  body('description').optional().isLength({ min: 0 }).trim(),
  body('monthly_price').isFloat({ min: 0 }).toFloat(),
  body('annual_price').isFloat({ min: 0 }).toFloat(),
  body('per_user_fee').isFloat({ min: 0 }).toFloat(),
  body('max_users').isInt({ min: 0 }).toInt(),
  body('max_locations').isInt({ min: 0 }).toInt(),
  body('max_monthly_tracked_hours').optional({ nullable: true }).custom(value => {
    if (value === null || value === undefined || value === '') return true;
    return Number.isInteger(Number(value)) && Number(value) >= 0;
  }),
  body('max_storage_gb').optional({ nullable: true }).custom(value => {
    if (value === null || value === undefined || value === '') return true;
    return Number.isInteger(Number(value)) && Number(value) >= 0;
  }),
  body('max_api_calls_per_hour').optional({ nullable: true }).custom(value => {
    if (value === null || value === undefined || value === '') return true;
    return Number.isInteger(Number(value)) && Number(value) >= 0;
  }),
  body('features').optional().isArray(),
  body('is_active').optional().isBoolean().toBoolean(),
  body('sort_order').optional().isInt({ min: 0 }).toInt()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      name,
      display_name,
      description,
      monthly_price,
      annual_price,
      per_user_fee,
      max_users,
      max_locations,
      features = [],
      is_active = true,
    } = req.body;

    // Check if plan name already exists
    const existingPlan = await pool.query(`
      SELECT id FROM subscription_plans WHERE name = $1
    `, [name]);

    if (existingPlan.rows.length > 0) {
      return res.status(409).json({ error: 'Plan name already exists' });
    }

    const result = await pool.query(`
      INSERT INTO subscription_plans (
        name, display_name, description, monthly_price, annual_price, per_user_fee,
        max_users, max_locations, features, is_active
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      name, display_name, description, monthly_price, annual_price, per_user_fee,
      max_users, max_locations, JSON.stringify(features), is_active
    ]);

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating subscription plan:', error);
    res.status(500).json({ error: 'Failed to create subscription plan' });
  }
});

// Update subscription plan
router.put('/:planId', [
  authenticateToken,
  requireRole(['super_admin']),
  body('name').optional().isLength({ min: 1 }).trim(),
  body('display_name').optional().isLength({ min: 1 }).trim(),
  body('description').optional().isLength({ min: 0 }).trim(),
  body('monthly_price').optional().isFloat({ min: 0 }).toFloat(),
  body('annual_price').optional().isFloat({ min: 0 }).toFloat(),
  body('per_user_fee').optional().isFloat({ min: 0 }).toFloat(),
  body('max_users').optional().isInt({ min: 0 }).toInt(),
  body('max_locations').optional().isInt({ min: 0 }).toInt(),
  body('features').optional().isArray(),
  body('is_active').optional().isBoolean().toBoolean(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { planId } = req.params;
    const updateData = req.body;

    // Check if plan exists
    const existingPlan = await pool.query(`
      SELECT id FROM subscription_plans WHERE id = $1
    `, [planId]);

    if (existingPlan.rows.length === 0) {
      return res.status(404).json({ error: 'Subscription plan not found' });
    }

    // If name is being updated, check for conflicts
    if (updateData.name) {
      const nameConflict = await pool.query(`
        SELECT id FROM subscription_plans WHERE name = $1 AND id != $2
      `, [updateData.name, planId]);

      if (nameConflict.rows.length > 0) {
        return res.status(409).json({ error: 'Plan name already exists' });
      }
    }

    // Build update query dynamically
    const updates = [];
    const params = [];
    let paramIndex = 1;

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        if (key === 'features') {
          updates.push(`${key} = ${paramIndex}`);
          params.push(JSON.stringify(updateData[key]));
        } else {
          updates.push(`${key} = ${paramIndex}`);
          params.push(updateData[key]);
        }
        paramIndex++;
      }
    });

    updates.push(`updated_at = CURRENT_TIMESTAMP`);
    params.push(planId);

    const result = await pool.query(`
      UPDATE subscription_plans 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `, params);

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating subscription plan:', error);
    res.status(500).json({ error: 'Failed to update subscription plan' });
  }
});

// Delete subscription plan
router.delete('/:planId', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { planId } = req.params;

    // Check if any organizations are using this plan
    const organizationsUsingPlan = await pool.query(`
      SELECT COUNT(*) as count FROM organizations 
      WHERE subscription_plan = (SELECT name FROM subscription_plans WHERE id = $1)
    `, [planId]);

    if (parseInt(organizationsUsingPlan.rows[0].count) > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete plan that is currently in use by organizations' 
      });
    }

    const result = await pool.query(`
      DELETE FROM subscription_plans WHERE id = $1 RETURNING *
    `, [planId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Subscription plan not found' });
    }

    res.json({ message: 'Subscription plan deleted successfully' });
  } catch (error) {
    console.error('Error deleting subscription plan:', error);
    res.status(500).json({ error: 'Failed to delete subscription plan' });
  }
});

// Toggle plan active status
router.patch('/:planId/toggle-active', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { planId } = req.params;

    const result = await pool.query(`
      UPDATE subscription_plans 
      SET is_active = NOT is_active, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [planId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Subscription plan not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error toggling plan status:', error);
    res.status(500).json({ error: 'Failed to toggle plan status' });
  }
});

module.exports = router;
