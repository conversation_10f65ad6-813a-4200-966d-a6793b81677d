import React from 'react';
import { 
  AlertTriangle, 
  Shield, 
  MapPin, 
  X, 
  ExternalLink,
  Phone,
  MessageSquare
} from 'lucide-react';
import BrandedButton from '../BrandedButton';

const AlertBanner = ({ 
  type, 
  title, 
  message, 
  onClose, 
  action,
  priority = 'medium',
  timestamp,
  employeeId,
  locationId
}) => {
  const getAlertConfig = () => {
    switch (type) {
      case 'incident':
        return {
          icon: AlertTriangle,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-500',
          bgIconColor: 'bg-red-100'
        };
      case 'panic_button':
        return {
          icon: Shield,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-500',
          bgIconColor: 'bg-red-100'
        };
      case 'geofence_breach':
        return {
          icon: MapPin,
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          textColor: 'text-orange-800',
          iconColor: 'text-orange-500',
          bgIconColor: 'bg-orange-100'
        };
      case 'emergency':
        return {
          icon: AlertTriangle,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-500',
          bgIconColor: 'bg-red-100'
        };
      case 'warning':
        return {
          icon: AlertTriangle,
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-500',
          bgIconColor: 'bg-yellow-100'
        };
      case 'info':
        return {
          icon: AlertTriangle,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-500',
          bgIconColor: 'bg-blue-100'
        };
      default:
        return {
          icon: AlertTriangle,
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-800',
          iconColor: 'text-gray-500',
          bgIconColor: 'bg-gray-100'
        };
    }
  };

  const getPriorityConfig = () => {
    switch (priority) {
      case 'high':
        return {
          animation: 'animate-pulse',
          urgency: 'URGENT'
        };
      case 'critical':
        return {
          animation: 'animate-pulse',
          urgency: 'CRITICAL'
        };
      default:
        return {
          animation: '',
          urgency: null
        };
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const config = getAlertConfig();
  const priorityConfig = getPriorityConfig();
  const Icon = config.icon;

  return (
    <div className={`fixed top-4 left-4 right-4 z-50 ${config.bgColor} ${config.borderColor} border rounded-lg shadow-lg ${priorityConfig.animation}`}>
      <div className="p-4">
        <div className="flex items-start">
          {/* Icon */}
          <div className={`flex-shrink-0 ${config.bgIconColor} rounded-full p-2 mr-3`}>
            <Icon className={`h-5 w-5 ${config.iconColor}`} />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <h3 className={`text-sm font-medium ${config.textColor}`}>
                  {title}
                </h3>
                {priorityConfig.urgency && (
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                    {priorityConfig.urgency}
                  </span>
                )}
                {timestamp && (
                  <span className="text-xs text-gray-500">
                    {formatTimestamp(timestamp)}
                  </span>
                )}
              </div>
              <button
                onClick={onClose}
                className={`ml-3 flex-shrink-0 ${config.textColor} hover:opacity-75`}
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            {message && (
              <p className={`mt-1 text-sm ${config.textColor}`}>
                {message}
              </p>
            )}

            {/* Actions */}
            {action && (
              <div className="mt-3 flex items-center space-x-2">
                {action.type === 'navigate' && (
                  <BrandedButton
                    onClick={action.onClick}
                    size="sm"
                    className="flex items-center space-x-1"
                  >
                    <ExternalLink className="h-3 w-3" />
                    <span>{action.label}</span>
                  </BrandedButton>
                )}

                {action.type === 'call' && (
                  <button
                    onClick={action.onClick}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  >
                    <Phone className="h-3 w-3 mr-1" />
                    {action.label}
                  </button>
                )}

                {action.type === 'message' && (
                  <button
                    onClick={action.onClick}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <MessageSquare className="h-3 w-3 mr-1" />
                    {action.label}
                  </button>
                )}

                {action.type === 'acknowledge' && (
                  <button
                    onClick={action.onClick}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  >
                    {action.label}
                  </button>
                )}

                {action.secondary && (
                  <button
                    onClick={action.secondary.onClick}
                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    {action.secondary.label}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlertBanner; 