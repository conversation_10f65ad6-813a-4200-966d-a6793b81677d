import React from 'react';
import { 
  Bell, 
  X, 
  AlertTriangle, 
  Shield, 
  MapPin, 
  MessageSquare,
  Clock,
  CheckCircle,
  Trash2
} from 'lucide-react';
import { useSelector, useDispatch } from 'react-redux';
import {
  markNotificationAsRead,
  clearAllNotifications,
  deleteNotification
} from '../../store/slices/notificationSlice';

const NotificationCenter = ({ onClose }) => {
  const dispatch = useDispatch();
  const notifications = useSelector((state) => state.notifications.notifications);

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'incident':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'panic_button':
        return <Shield className="h-4 w-4 text-red-500" />;
      case 'geofence_breach':
        return <MapPin className="h-4 w-4 text-orange-500" />;
      case 'message':
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'emergency':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const getNotificationColor = (type) => {
    switch (type) {
      case 'incident':
      case 'panic_button':
      case 'emergency':
        return 'border-l-red-500 bg-red-50';
      case 'geofence_breach':
        return 'border-l-orange-500 bg-orange-50';
      case 'message':
        return 'border-l-blue-500 bg-blue-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const handleNotificationClick = (notification) => {
    if (!notification.read) {
      dispatch(markNotificationAsRead(notification.id));
    }
    
    // Handle navigation based on notification type
    switch (notification.type) {
      case 'incident':
        window.location.href = `/org/${notification.tenantId}/incidents`;
        break;
      case 'panic_button':
      case 'geofence_breach':
        window.location.href = `/org/${notification.tenantId}/live-map`;
        break;
      case 'message':
        // Open message center (this would be handled by parent component)
        break;
      default:
        break;
    }
    
    onClose();
  };

  const handleDeleteNotification = (notificationId, e) => {
    e.stopPropagation();
    dispatch(deleteNotification(notificationId));
  };

  const handleClearAll = () => {
    dispatch(clearAllNotifications());
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="bg-white rounded-lg shadow-xl border border-gray-200 max-h-96 overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Bell className="h-5 w-5 text-gray-600" />
          <h3 className="font-medium text-gray-900">Notifications</h3>
          {unreadCount > 0 && (
            <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
              {unreadCount}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {notifications.length > 0 && (
            <button
              onClick={handleClearAll}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              Clear all
            </button>
          )}
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Notifications List */}
      <div className="max-h-80 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            <Bell className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No notifications</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                onClick={() => handleNotificationClick(notification)}
                className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors border-l-4 ${
                  notification.read ? 'opacity-75' : ''
                } ${getNotificationColor(notification.type)}`}
              >
                <div className="flex items-start space-x-3">
                  {/* Icon */}
                  <div className="flex-shrink-0 mt-0.5">
                    {getNotificationIcon(notification.type)}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className={`text-sm font-medium ${
                          notification.read ? 'text-gray-600' : 'text-gray-900'
                        }`}>
                          {notification.title}
                        </p>
                        {notification.message && (
                          <p className={`text-sm mt-1 ${
                            notification.read ? 'text-gray-500' : 'text-gray-700'
                          }`}>
                            {notification.message}
                          </p>
                        )}
                        <div className="flex items-center space-x-2 mt-2">
                          <span className="text-xs text-gray-500">
                            {formatTimestamp(notification.timestamp)}
                          </span>
                          {notification.read && (
                            <CheckCircle className="h-3 w-3 text-green-500" />
                          )}
                        </div>
                      </div>

                      {/* Delete Button */}
                      <button
                        onClick={(e) => handleDeleteNotification(notification.id, e)}
                        className="flex-shrink-0 ml-2 text-gray-400 hover:text-red-500"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </div>

                    {/* Additional Info */}
                    {notification.employeeName && (
                      <p className="text-xs text-gray-500 mt-1">
                        Employee: {notification.employeeName}
                      </p>
                    )}

                    {notification.locationName && (
                      <p className="text-xs text-gray-500">
                        Location: {notification.locationName}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {notifications.length > 0 && (
        <div className="p-3 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>
              {unreadCount} unread of {notifications.length} total
            </span>
            <button
              onClick={handleClearAll}
              className="text-blue-600 hover:text-blue-800"
            >
              Mark all as read
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter; 