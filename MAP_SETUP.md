# Real-Time Map Dashboard Setup Guide

This guide will help you set up the real-time map dashboard for the OnTheMove admin application.

## Prerequisites

- Node.js 16+ and npm
- Mapbox account and access token
- WebSocket server (optional, for real-time updates)

## 1. Mapbox Configuration

### Get a Mapbox Access Token

1. Go to [Mapbox](https://www.mapbox.com/) and create an account
2. Navigate to your account dashboard
3. Create a new access token or use the default public token
4. Copy your access token

### Update the Access Token

In `src/pages/LiveMapDashboard.js`, replace the placeholder token:

```javascript
const MAPBOX_ACCESS_TOKEN = 'your_actual_mapbox_access_token_here';
```

Or set it as an environment variable:

```bash
# .env file
REACT_APP_MAPBOX_ACCESS_TOKEN=your_actual_mapbox_access_token_here
```

Then update the code to use the environment variable:

```javascript
const MAPBOX_ACCESS_TOKEN = process.env.REACT_APP_MAPBOX_ACCESS_TOKEN;
```

## 2. WebSocket Server Setup (Optional)

For real-time location updates, you'll need a WebSocket server. Here's a basic Node.js implementation:

### Install Dependencies

```bash
npm install socket.io express cors dotenv
```

### Create WebSocket Server

Create a new file `websocket-server.js`:

```javascript
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.CLIENT_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());

// Store connected clients and their tenant rooms
const connectedClients = new Map();
const tenantRooms = new Map();

// Authentication middleware
io.use((socket, next) => {
  const { token, tenantId } = socket.handshake.auth;
  
  // Add your JWT verification logic here
  if (token && tenantId) {
    socket.tenantId = tenantId;
    next();
  } else {
    next(new Error('Authentication failed'));
  }
});

io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  const tenantId = socket.tenantId;
  connectedClients.set(socket.id, { tenantId, socket });
  
  // Join tenant room
  socket.on('join_tenant', ({ tenantId }) => {
    socket.join(`tenant:${tenantId}`);
    console.log(`Client ${socket.id} joined tenant room: ${tenantId}`);
  });
  
  // Leave tenant room
  socket.on('leave_tenant', ({ tenantId }) => {
    socket.leave(`tenant:${tenantId}`);
    console.log(`Client ${socket.id} left tenant room: ${tenantId}`);
  });
  
  // Handle employee location updates
  socket.on('employee_location_update', (data) => {
    socket.to(`tenant:${data.tenantId}`).emit('employee_location_update', data);
  });
  
  // Handle employee status updates
  socket.on('employee_status_update', (data) => {
    socket.to(`tenant:${data.tenantId}`).emit('employee_status_update', data);
  });
  
  // Handle emergency alerts
  socket.on('emergency_alert', (data) => {
    socket.to(`tenant:${data.tenantId}`).emit('emergency_alert', data);
  });
  
  // Handle zone violations
  socket.on('zone_violation', (data) => {
    socket.to(`tenant:${data.tenantId}`).emit('zone_violation', data);
  });
  
  // Handle late arrivals
  socket.on('late_arrival', (data) => {
    socket.to(`tenant:${data.tenantId}`).emit('late_arrival', data);
  });
  
  // Handle break events
  socket.on('break_start', (data) => {
    socket.to(`tenant:${data.tenantId}`).emit('break_start', data);
  });
  
  socket.on('break_end', (data) => {
    socket.to(`tenant:${data.tenantId}`).emit('break_end', data);
  });
  
  // Handle shift events
  socket.on('shift_start', (data) => {
    socket.to(`tenant:${data.tenantId}`).emit('shift_start', data);
  });
  
  socket.on('shift_end', (data) => {
    socket.to(`tenant:${data.tenantId}`).emit('shift_end', data);
  });
  
  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
    connectedClients.delete(socket.id);
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`WebSocket server running on port ${PORT}`);
});
```

### Environment Variables

Create a `.env` file for the WebSocket server:

```bash
PORT=3001
CLIENT_URL=http://localhost:3000
JWT_SECRET=your_jwt_secret_here
```

### Start the WebSocket Server

```bash
node websocket-server.js
```

## 3. Environment Configuration

Update your React app's `.env` file:

```bash
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WEBSOCKET_URL=http://localhost:3001
REACT_APP_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here
```

## 4. Features Overview

### Real-Time Map Dashboard

- **Live Employee Tracking**: View real-time locations of all employees
- **Status Indicators**: Visual indicators for employee status (on duty, late, on break, etc.)
- **Interactive Markers**: Click on employee markers to view detailed information
- **Filtering**: Filter employees by status, department, or search terms
- **Auto-refresh**: Automatic updates every 30 seconds (fallback when WebSocket is offline)

### Employee Status Types

- **On Duty**: Employee is actively working
- **Late**: Employee is late for their shift
- **On Break**: Employee is on break
- **Out of Zone**: Employee is outside their assigned work zone
- **Offline**: Employee is not connected or available
- **Online**: Employee is connected but not on duty

### Real-Time Notifications

- **Emergency Alerts**: High-priority alerts for safety issues
- **Zone Violations**: Notifications when employees leave assigned zones
- **Late Arrivals**: Alerts for employees who are late
- **Status Changes**: Updates when employee status changes

### WebSocket Events

The system handles these real-time events:

- `employee_location_update`: Real-time location updates
- `employee_status_update`: Status changes
- `emergency_alert`: Emergency situations
- `zone_violation`: Zone boundary violations
- `late_arrival`: Late arrival notifications
- `break_start`/`break_end`: Break management
- `shift_start`/`shift_end`: Shift management

## 5. Testing

### Without WebSocket Server

The map will work with mock data and fallback polling:

1. Start the React app: `npm start`
2. Navigate to `/org/{tenantId}/live-map`
3. You'll see mock employee data with simulated locations

### With WebSocket Server

1. Start the WebSocket server: `node websocket-server.js`
2. Start the React app: `npm start`
3. Navigate to the live map
4. You should see "Live" connection status
5. Real-time updates will be simulated with mock data

## 6. Customization

### Map Style

Change the map style in `LiveMapDashboard.js`:

```javascript
// Available styles:
// mapbox://styles/mapbox/streets-v11
// mapbox://styles/mapbox/outdoors-v11
// mapbox://styles/mapbox/light-v10
// mapbox://styles/mapbox/dark-v10
// mapbox://styles/mapbox/satellite-v9
mapStyle="mapbox://styles/mapbox/streets-v11"
```

### Update Intervals

Modify the refresh intervals in the Redux slice:

```javascript
// In mapSlice.js
refreshInterval: 30000 // 30 seconds
```

### Notification Settings

Configure notification preferences in the notification slice:

```javascript
// In notificationSlice.js
settings: {
  enableSound: true,
  enableDesktopNotifications: true,
  showEmergencyAlerts: true,
  showZoneViolations: true,
  showStatusChanges: false
}
```

## 7. Production Deployment

### Environment Variables

Set production environment variables:

```bash
REACT_APP_API_URL=https://your-api-domain.com
REACT_APP_WEBSOCKET_URL=https://your-websocket-domain.com
REACT_APP_MAPBOX_ACCESS_TOKEN=your_production_mapbox_token
```

### WebSocket Server Deployment

Deploy the WebSocket server to your preferred hosting platform (Heroku, AWS, etc.) and update the client configuration accordingly.

### Security Considerations

- Use HTTPS in production
- Implement proper JWT authentication
- Add rate limiting to WebSocket connections
- Validate all incoming WebSocket messages
- Use environment variables for sensitive data

## 8. Troubleshooting

### Map Not Loading

- Check Mapbox access token
- Verify network connectivity
- Check browser console for errors

### WebSocket Connection Issues

- Verify WebSocket server is running
- Check CORS configuration
- Ensure authentication tokens are valid
- Check network connectivity

### No Real-Time Updates

- Verify WebSocket connection status
- Check server logs for errors
- Ensure proper room joining
- Verify event emission/reception

## Support

For additional help or questions, refer to:
- [Mapbox Documentation](https://docs.mapbox.com/)
- [Socket.IO Documentation](https://socket.io/docs/)
- [React Map GL Documentation](https://visgl.github.io/react-map-gl/) 