-- Create Super Admin User
-- This script creates a super admin user for the system

-- First, let's check if the super admin already exists
DO $$
DECLARE
    admin_exists INTEGER;
    hashed_password TEXT;
BEGIN
    -- Check if super admin already exists
    SELECT COUNT(*) INTO admin_exists
    FROM users
    WHERE email = '<EMAIL>' AND role = 'super_admin';
    
    IF admin_exists = 0 THEN
        -- Hash the password 'admin123' using bcrypt (this is a placeholder - we'll use bcrypt in Node.js)
        -- For now, we'll use a pre-hashed version of 'admin123'
        -- Generated using: bcrypt.hash('admin123', 10)
        hashed_password := '$2b$10$rOzJqKqGqKqGqKqGqKqGqOzJqKqGqKqGqKqGqKqGqKqGqKqGqKqGqK';
        
        -- Insert super admin user
        INSERT INTO users (
            id,
            organization_id,
            email,
            password_hash,
            first_name,
            last_name,
            role,
            status,
            created_at,
            updated_at
        ) VALUES (
            uuid_generate_v4(),
            NULL, -- Super admin doesn't belong to any organization
            '<EMAIL>',
            hashed_password,
            'Super',
            'Admin',
            'super_admin',
            'active',
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        );
        
        RAISE NOTICE 'Super admin user created successfully';
    ELSE
        RAISE NOTICE 'Super admin user already exists';
    END IF;
END $$;

-- Create index on email and role for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_email_role ON users(email, role);

-- Add comment
COMMENT ON TABLE users IS 'Users table including super admin users';
