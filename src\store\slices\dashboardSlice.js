import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { organizationAPI } from '../../services/api';

// Async thunks
export const fetchDashboardStats = createAsyncThunk(
  'dashboard/fetchStats',
  async ({ organizationId }, { rejectWithValue }) => {
    try {
      const response = await organizationAPI.getOrganizationStats(organizationId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard statistics');
    }
  }
);

// Initial state
const initialState = {
  stats: {
    totalEmployees: 0,
    totalLocations: 0,
    activeAssignments: 0,
    monthlyHours: 0
  },
  recentActivity: [],
  loading: false,
  error: null,
  lastUpdated: null
};

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearDashboardData: (state) => {
      state.stats = initialState.stats;
      state.recentActivity = [];
      state.lastUpdated = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDashboardStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDashboardStats.fulfilled, (state, action) => {
        state.loading = false;
        state.stats = action.payload.stats;
        state.recentActivity = action.payload.recentActivity || [];
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchDashboardStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export const { clearError, clearDashboardData } = dashboardSlice.actions;

export default dashboardSlice.reducer;
