const bcrypt = require('bcryptjs');
const { pool } = require('../config/database');

async function testPassword() {
  try {
    // Get the stored password hash
    const result = await pool.query(
      'SELECT email, password_hash FROM users WHERE email = $1',
      ['<EMAIL>']
    );

    if (result.rows.length === 0) {
      console.log('User not found');
      return;
    }

    const user = result.rows[0];
    console.log('User found:', user.email);
    console.log('Stored hash:', user.password_hash);

    // Test the password
    const testPassword = 'admin123';
    const isValid = await bcrypt.compare(testPassword, user.password_hash);
    
    console.log('Password test result:', isValid);
    
    if (!isValid) {
      console.log('Password does not match. Let\'s create a new hash:');
      const newHash = await bcrypt.hash(testPassword, 10);
      console.log('New hash:', newHash);
      
      // Update the password
      await pool.query(
        'UPDATE users SET password_hash = $1 WHERE email = $2',
        [newHash, '<EMAIL>']
      );
      console.log('Password updated successfully');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    process.exit(0);
  }
}

testPassword();
