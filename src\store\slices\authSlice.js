import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { authAPI } from '../../services/api';
import { decodeJWT } from '../../services/jwt';

// Demo credentials removed - using database authentication only

// Async thunks
export const login = createAsyncThunk(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      // Use database authentication only
      const response = await authAPI.login(credentials);
      return response.data;
    } catch (error) {
      if (error.response) {
        return rejectWithValue(error.response.data || 'Login failed');
      } else if (error.request) {
        return rejectWithValue('Network error - no response from server');
      } else {
        return rejectWithValue('Request setup error: ' + error.message);
      }
    }
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      // For demo users, just clear local storage
      const token = localStorage.getItem('token');
      if (token && token.startsWith('demo-jwt-token')) {
        return null;
      }
      
      await authAPI.logout();
      return null;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Logout failed');
    }
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authAPI.refreshToken();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Token refresh failed');
    }
  }
);

function getInitialAuthState() {
  // Check for existing token but don't auto-authenticate
  const token = localStorage.getItem('token');
  const refreshToken = localStorage.getItem('refreshToken');

  if (token) {
    try {
      const decoded = decodeJWT(token);
      const tenant = decoded?.tenant || decoded?.tenantId || null;

      // Initialize state with token data but keep isAuthenticated false for manual login
      const initialState = {
        user: decoded?.user || null,
        token,
        refreshToken,
        isAuthenticated: true, // Keep authenticated state if token exists
        isLoading: false,
        error: null,
        tenant,
        roles: decoded?.roles || [],
      };

      return initialState;
    } catch (error) {
      // If token is invalid, clear it and start fresh
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
    }
  }

  return {
    user: null,
    token: null,
    refreshToken: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    tenant: null,
    roles: [],
  };
}

const initialState = getInitialAuthState();

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setTenant: (state, action) => {
      state.tenant = action.payload;
    },
    updateUser: (state, action) => {
      state.user = { ...state.user, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
        
        // Handle JWT tokens only
        const decoded = decodeJWT(action.payload.token);
        state.user = decoded?.user || null;
        state.roles = decoded?.roles || [];
        state.tenant = decoded?.tenant || decoded?.tenantId || null;
        
        // Store tokens in localStorage
        localStorage.setItem('token', action.payload.token);
        localStorage.setItem('refreshToken', action.payload.refreshToken);
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Logout
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.tenant = null;
        state.roles = [];
        // Clear tokens from localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('demoUser');
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Refresh token
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
        localStorage.setItem('token', action.payload.token);
        localStorage.setItem('refreshToken', action.payload.refreshToken);
      });
  }
});

export const { clearError, setTenant, updateUser } = authSlice.actions;
export default authSlice.reducer; 