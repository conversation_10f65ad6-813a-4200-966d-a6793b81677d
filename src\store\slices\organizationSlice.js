import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { organizationAPI } from '../../services/api';

// Helper function to extract error message
const getErrorMessage = (error) => {
  if (typeof error === 'string') return error;
  if (error?.response?.data?.message) return error.response.data.message;
  if (error?.response?.data?.error) return error.response.data.error;
  if (error?.response?.data && typeof error.response.data === 'string') return error.response.data;
  if (error?.message) return error.message;
  return 'An error occurred';
};

// Async thunks
export const fetchOrganizations = createAsyncThunk(
  'organization/fetchOrganizations',
  async (_, { rejectWithValue }) => {
    try {
      const response = await organizationAPI.getOrganizations();
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to fetch organizations');
    }
  }
);

export const fetchOrganizationById = createAsyncThunk(
  'organization/fetchOrganizationById',
  async (organizationId, { rejectWithValue }) => {
    try {
      const response = await organizationAPI.getOrganizationById(organizationId);
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to fetch organization');
    }
  }
);

export const createOrganization = createAsyncThunk(
  'organization/createOrganization',
  async (organizationData, { rejectWithValue }) => {
    try {
      const response = await organizationAPI.createOrganization(organizationData);
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to create organization');
    }
  }
);

export const updateOrganization = createAsyncThunk(
  'organization/updateOrganization',
  async ({ organizationId, organizationData }, { rejectWithValue }) => {
    try {
      console.log('Updating organization with ID:', organizationId, 'and data:', organizationData);
      const response = await organizationAPI.updateOrganization(organizationId, organizationData);
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to update organization');
    }
  }
);

export const deleteOrganization = createAsyncThunk(
  'organization/deleteOrganization',
  async (organizationId, { rejectWithValue }) => {
    try {
      await organizationAPI.deleteOrganization(organizationId);
      return organizationId;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to delete organization');
    }
  }
);

export const updateOrganizationBrandingAsync = createAsyncThunk(
  'organization/updateBranding',
  async ({ organizationId, brandingData }, { rejectWithValue }) => {
    try {
      const response = await organizationAPI.updateOrganizationBranding(organizationId, brandingData);
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to update organization branding');
    }
  }
);

const initialState = {
  organizations: [],
  currentOrganization: null,
  isLoading: false,
  error: null,
  selectedOrganizationId: null,
  pagination: null,
};

const organizationSlice = createSlice({
  name: 'organization',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedOrganization: (state, action) => {
      state.selectedOrganizationId = action.payload;
    },
    setCurrentOrganization: (state, action) => {
      state.currentOrganization = action.payload;
    },
    updateOrganizationBranding: (state, action) => {
      if (state.currentOrganization) {
        state.currentOrganization.branding = {
          ...state.currentOrganization.branding,
          ...action.payload,
        };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Organizations
      .addCase(fetchOrganizations.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchOrganizations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.organizations = action.payload.organizations || [];
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchOrganizations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Fetch Organization by ID
      .addCase(fetchOrganizationById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchOrganizationById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentOrganization = action.payload;
      })
      .addCase(fetchOrganizationById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Create Organization
      .addCase(createOrganization.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createOrganization.fulfilled, (state, action) => {
        state.isLoading = false;
        state.organizations.push(action.payload);
      })
      .addCase(createOrganization.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Update Organization
      .addCase(updateOrganization.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateOrganization.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.organizations.findIndex(o => o.id === action.payload.id);
        if (index !== -1) {
          state.organizations[index] = action.payload;
        }
        if (state.currentOrganization?.id === action.payload.id) {
          state.currentOrganization = action.payload;
        }
      })
      .addCase(updateOrganization.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Delete Organization
      .addCase(deleteOrganization.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteOrganization.fulfilled, (state, action) => {
        state.isLoading = false;
        state.organizations = state.organizations.filter(o => o.id !== action.payload);
        if (state.currentOrganization?.id === action.payload) {
          state.currentOrganization = null;
        }
      })
      .addCase(deleteOrganization.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Update Organization Branding
      .addCase(updateOrganizationBrandingAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateOrganizationBrandingAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.currentOrganization) {
          state.currentOrganization.branding = action.payload.organization.branding;
        }
        // Update in organizations list if present
        const orgIndex = state.organizations.findIndex(org => org.id === action.payload.organization.id);
        if (orgIndex !== -1) {
          state.organizations[orgIndex].branding = action.payload.organization.branding;
        }
      })
      .addCase(updateOrganizationBrandingAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { 
  clearError, 
  setSelectedOrganization, 
  setCurrentOrganization, 
  updateOrganizationBranding 
} = organizationSlice.actions;

export default organizationSlice.reducer; 