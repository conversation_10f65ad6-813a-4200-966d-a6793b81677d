import { io } from 'socket.io-client';
import { store } from '../store';
import { updateEmployeeLocation, updateEmployeeStatusLocal } from '../store/slices/mapSlice';
import { 
  addEmergencyAlert, 
  addZoneViolation, 
  addStatusChange, 
  addLateAlert,
  addIncidentAlert,
  addPanicAlert,
  addGeofenceBreach,
  addMessageNotification
} from '../store/slices/notificationSlice';
import {
  addIncidentFromWebSocket,
  updateIncidentFromWebSocket
} from '../store/slices/incidentSlice';
import {
  addMessage,
  updateMessageStatus,
  setTypingUser,
  updateUserOnlineStatus,
  addConversation
} from '../store/slices/messagingSlice';
import {
  addGeofenceAlert
} from '../store/slices/geofenceSlice';

class WebSocketService {
  constructor() {
    this.socket = null;
    this.tenantId = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second
    this.userId = null;
  }

  connect(tenantId, userId, token) {
    if (this.socket) {
      this.disconnect();
    }

    this.tenantId = tenantId;
    this.userId = userId;
    
    // Connect to WebSocket server
    this.socket = io(process.env.REACT_APP_WEBSOCKET_URL || 'http://localhost:3001', {
      auth: {
        token,
        tenantId,
        userId
      },
      transports: ['websocket', 'polling'],
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay,
      timeout: 20000
    });

    this.setupEventListeners();
  }

  setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      store.dispatch({ type: 'map/setConnectionStatus', payload: 'connected' });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.isConnected = false;
      store.dispatch({ type: 'map/setConnectionStatus', payload: 'disconnected' });
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.isConnected = false;
      store.dispatch({ type: 'map/setConnectionStatus', payload: 'error' });
    });

    // Real-time location updates
    this.socket.on('employee_location_update', (data) => {
      store.dispatch(updateEmployeeLocation(data));
    });

    // Employee status updates
    this.socket.on('employee_status_update', (data) => {
      store.dispatch(updateEmployeeStatusLocal({
        employeeId: data.employeeId,
        status: data.status,
        data: data.additionalData
      }));

      // Add notification for status change
      if (data.employeeName) {
        store.dispatch(addStatusChange({
          employeeId: data.employeeId,
          employeeName: data.employeeName,
          newStatus: data.status
        }));
      }
    });

    // Assignment updates
    this.socket.on('assignment_update', (data) => {
      // Handle assignment updates if needed
      console.log('Assignment update received:', data);
    });

    // Emergency alerts
    this.socket.on('emergency_alert', (data) => {
      console.log('Emergency alert:', data);
      store.dispatch(addEmergencyAlert({
        employeeId: data.employeeId,
        employeeName: data.employeeName,
        message: data.message
      }));
    });

    // Zone violation alerts
    this.socket.on('zone_violation', (data) => {
      console.log('Zone violation:', data);
      // Update employee status to out_of_zone
      store.dispatch(updateEmployeeStatusLocal({
        employeeId: data.employeeId,
        status: 'out_of_zone',
        data: { violationReason: data.reason }
      }));

      // Add notification for zone violation
      store.dispatch(addZoneViolation({
        employeeId: data.employeeId,
        employeeName: data.employeeName
      }));
    });

    // Late arrival alerts
    this.socket.on('late_arrival', (data) => {
      console.log('Late arrival:', data);
      store.dispatch(addLateAlert({
        employeeId: data.employeeId,
        employeeName: data.employeeName
      }));
    });

    // Break start/end alerts
    this.socket.on('break_start', (data) => {
      console.log('Break started:', data);
      store.dispatch(updateEmployeeStatusLocal({
        employeeId: data.employeeId,
        status: 'on_break',
        data: { breakStartTime: data.startTime, breakEndTime: data.endTime }
      }));
    });

    this.socket.on('break_end', (data) => {
      console.log('Break ended:', data);
      store.dispatch(updateEmployeeStatusLocal({
        employeeId: data.employeeId,
        status: 'on_duty',
        data: { breakEndTime: data.endTime }
      }));
    });

    // Shift start/end alerts
    this.socket.on('shift_start', (data) => {
      console.log('Shift started:', data);
      store.dispatch(updateEmployeeStatusLocal({
        employeeId: data.employeeId,
        status: 'on_duty',
        data: { shiftStartTime: data.startTime }
      }));
    });

    this.socket.on('shift_end', (data) => {
      console.log('Shift ended:', data);
      store.dispatch(updateEmployeeStatusLocal({
        employeeId: data.employeeId,
        status: 'offline',
        data: { shiftEndTime: data.endTime }
      }));
    });

    // Incident events
    this.socket.on('incident_submitted', (data) => {
      console.log('New incident submitted:', data);
      store.dispatch(addIncidentFromWebSocket(data.incident));
      
      // Add notification for new incident
      store.dispatch(addIncidentAlert({
        incidentId: data.incident.id,
        title: data.incident.title,
        severity: data.incident.severity,
        employeeId: data.incident.submittedBy,
        employeeName: data.employeeName,
        locationId: data.incident.locationId,
        locationName: data.locationName,
        tenantId: data.incident.tenantId
      }));
    });

    this.socket.on('incident_updated', (data) => {
      console.log('Incident updated:', data);
      store.dispatch(updateIncidentFromWebSocket(data.incident));
      
      // Add notification for incident update
      store.dispatch(addStatusChange({
        employeeId: data.incident.assignedEmployeeId,
        employeeName: data.employeeName,
        newStatus: data.incident.status,
        message: `Incident "${data.incident.title}" status updated to ${data.incident.status}`,
        type: 'incident_update',
        incidentId: data.incident.id
      }));
    });

    this.socket.on('incident_assigned', (data) => {
      console.log('Incident assigned:', data);
      store.dispatch(updateIncidentFromWebSocket(data.incident));
      
      // Add notification for incident assignment
      store.dispatch(addStatusChange({
        employeeId: data.incident.assignedEmployeeId,
        employeeName: data.employeeName,
        newStatus: 'assigned',
        message: `You have been assigned to incident: ${data.incident.title}`,
        type: 'incident_assignment',
        incidentId: data.incident.id
      }));
    });

    this.socket.on('incident_escalated', (data) => {
      console.log('Incident escalated:', data);
      store.dispatch(updateIncidentFromWebSocket(data.incident));
      
      // Add notification for incident escalation
      store.dispatch(addEmergencyAlert({
        employeeId: data.incident.assignedEmployeeId,
        employeeName: data.employeeName,
        message: `Incident "${data.incident.title}" has been escalated to ${data.incident.severity} severity`,
        type: 'incident_escalation',
        incidentId: data.incident.id
      }));
    });

    // Messaging events
    this.socket.on('new_message', (data) => {
      console.log('New message received:', data);
      store.dispatch(addMessage({
        conversationId: data.conversationId,
        message: data.message
      }));
      
      // Add notification for new message
      store.dispatch(addMessageNotification({
        conversationId: data.conversationId,
        senderId: data.message.senderId,
        senderName: data.senderName,
        message: data.message.content,
        tenantId: data.tenantId
      }));
    });

    this.socket.on('message_delivered', (data) => {
      console.log('Message delivered:', data);
      store.dispatch(updateMessageStatus({
        conversationId: data.conversationId,
        messageId: data.messageId,
        status: 'delivered',
        timestamp: data.timestamp
      }));
    });

    this.socket.on('message_read', (data) => {
      console.log('Message read:', data);
      store.dispatch(updateMessageStatus({
        conversationId: data.conversationId,
        messageId: data.messageId,
        status: 'read',
        timestamp: data.timestamp
      }));
    });

    this.socket.on('user_typing', (data) => {
      console.log('User typing:', data);
      store.dispatch(setTypingUser({
        conversationId: data.conversationId,
        userId: data.userId,
        isTyping: true
      }));
    });

    this.socket.on('user_stopped_typing', (data) => {
      console.log('User stopped typing:', data);
      store.dispatch(setTypingUser({
        conversationId: data.conversationId,
        userId: data.userId,
        isTyping: false
      }));
    });

    this.socket.on('user_online_status', (data) => {
      console.log('User online status:', data);
      store.dispatch(updateUserOnlineStatus({
        userId: data.userId,
        online: data.online,
        lastSeen: data.lastSeen
      }));
    });

    this.socket.on('new_conversation', (data) => {
      console.log('New conversation:', data);
      store.dispatch(addConversation(data.conversation));
    });

    // Panic button events
    this.socket.on('panic_button_activated', (data) => {
      console.log('Panic button activated:', data);
      store.dispatch(addPanicAlert({
        employeeId: data.employeeId,
        employeeName: data.employeeName,
        location: data.location,
        coordinates: data.coordinates,
        tenantId: data.tenantId
      }));
    });

    // Geofence breach events
    this.socket.on('geofence_breach', (data) => {
      console.log('Geofence breach:', data);
      store.dispatch(addGeofenceBreach({
        employeeId: data.employeeId,
        employeeName: data.employeeName,
        geofenceId: data.geofenceId,
        geofenceName: data.geofenceName,
        location: data.location,
        coordinates: data.coordinates,
        tenantId: data.tenantId
      }));
    });

    // Geofencing events
    this.socket.on('geofence_entry', (data) => {
      // store.dispatch(handleGeofenceEntry(data));
      store.dispatch(addGeofenceAlert({
        id: Date.now().toString(),
        type: 'geofence_entry',
        title: 'Geofence Entry',
        message: `${data.employeeName} entered ${data.geofenceName}`,
        severity: 'medium',
        timestamp: new Date().toISOString(),
        data: data
      }));
    });

    this.socket.on('geofence_exit', (data) => {
      // store.dispatch(handleGeofenceExit(data));
      store.dispatch(addGeofenceAlert({
        id: Date.now().toString(),
        type: 'geofence_exit',
        title: 'Geofence Exit',
        message: `${data.employeeName} exited ${data.geofenceName}`,
        severity: 'medium',
        timestamp: new Date().toISOString(),
        data: data
      }));
    });

    this.socket.on('unauthorized_entry', (data) => {
      // store.dispatch(handleUnauthorizedEntry(data));
      store.dispatch(addGeofenceAlert({
        id: Date.now().toString(),
        type: 'unauthorized_entry',
        title: 'Unauthorized Entry',
        message: `${data.employeeName} entered restricted area ${data.geofenceName}`,
        severity: 'critical',
        timestamp: new Date().toISOString(),
        data: data
      }));
    });

    this.socket.on('unauthorized_exit', (data) => {
      // store.dispatch(handleUnauthorizedExit(data));
      store.dispatch(addGeofenceAlert({
        id: Date.now().toString(),
        type: 'unauthorized_exit',
        title: 'Unauthorized Exit',
        message: `${data.employeeName} left restricted area ${data.geofenceName}`,
        severity: 'critical',
        timestamp: new Date().toISOString(),
        data: data
      }));
    });

    this.socket.on('dwell_time_alert', (data) => {
      // store.dispatch(handleDwellTimeAlert(data));
      store.dispatch(addGeofenceAlert({
        id: Date.now().toString(),
        type: 'dwell_time',
        title: 'Dwell Time Alert',
        message: `${data.employeeName} has been in ${data.geofenceName} for ${data.dwellTime} minutes`,
        severity: 'high',
        timestamp: new Date().toISOString(),
        data: data
      }));
    });

    this.socket.on('geofence_violation', (data) => {
      store.dispatch(addGeofenceAlert({
        id: Date.now().toString(),
        type: data.violationType,
        severity: data.severity || 'medium',
        employeeId: data.employeeId,
        employeeName: data.employeeName,
        geofenceId: data.geofenceId,
        geofenceName: data.geofenceName,
        location: data.location,
        timestamp: new Date().toISOString(),
        acknowledged: false,
        description: data.description
      }));
    });

    // Reconnection events
    this.socket.on('reconnect', (attemptNumber) => {
      console.log('WebSocket reconnected after', attemptNumber, 'attempts');
      this.isConnected = true;
    });

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      console.log('WebSocket reconnection attempt:', attemptNumber);
      this.reconnectAttempts = attemptNumber;
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('WebSocket reconnection error:', error);
    });

    this.socket.on('reconnect_failed', () => {
      console.error('WebSocket reconnection failed after', this.maxReconnectAttempts, 'attempts');
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.tenantId = null;
      this.userId = null;
    }
  }

  // Send message to specific employee
  sendMessageToEmployee(employeeId, message) {
    if (this.socket && this.isConnected) {
      this.socket.emit('send_message', {
        employeeId,
        message,
        tenantId: this.tenantId
      });
    }
  }

  // Request location update from employee
  requestLocationUpdate(employeeId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('request_location', {
        employeeId,
        tenantId: this.tenantId
      });
    }
  }

  // Update employee status
  updateEmployeeStatus(employeeId, status, data = {}) {
    if (this.socket && this.isConnected) {
      this.socket.emit('update_status', {
        employeeId,
        status,
        data,
        tenantId: this.tenantId
      });
    }
  }

  // Join specific room for tenant
  joinTenantRoom() {
    if (this.socket && this.isConnected && this.tenantId) {
      this.socket.emit('join_tenant', { tenantId: this.tenantId });
    }
  }

  // Leave tenant room
  leaveTenantRoom() {
    if (this.socket && this.isConnected && this.tenantId) {
      this.socket.emit('leave_tenant', { tenantId: this.tenantId });
    }
  }

  // Get connection status
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      tenantId: this.tenantId
    };
  }

  // Subscribe to specific employee updates
  subscribeToEmployee(employeeId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('subscribe_employee', {
        employeeId,
        tenantId: this.tenantId
      });
    }
  }

  // Unsubscribe from specific employee updates
  unsubscribeFromEmployee(employeeId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('unsubscribe_employee', {
        employeeId,
        tenantId: this.tenantId
      });
    }
  }

  // Request emergency assistance
  requestEmergencyAssistance(employeeId, emergencyType, details = {}) {
    if (this.socket && this.isConnected) {
      this.socket.emit('emergency_request', {
        employeeId,
        emergencyType,
        details,
        tenantId: this.tenantId
      });
    }
  }

  // Acknowledge alert
  acknowledgeAlert(alertId, employeeId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('acknowledge_alert', {
        alertId,
        employeeId,
        tenantId: this.tenantId
      });
    }
  }

  // Messaging methods
  sendMessage(conversationId, content, attachments = []) {
    if (this.socket && this.isConnected) {
      this.socket.emit('send_message', {
        conversationId,
        content,
        attachments,
        tenantId: this.tenantId
      });
    }
  }

  emitTyping(conversationId, isTyping = true) {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing', {
        conversationId,
        isTyping,
        tenantId: this.tenantId
      });
    }
  }

  joinConversation(conversationId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join_conversation', {
        conversationId,
        tenantId: this.tenantId
      });
    }
  }

  leaveConversation(conversationId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave_conversation', {
        conversationId,
        tenantId: this.tenantId
      });
    }
  }

  markMessageAsRead(conversationId, messageId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('mark_message_read', {
        conversationId,
        messageId,
        tenantId: this.tenantId
      });
    }
  }

  markConversationAsRead(conversationId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('mark_conversation_read', {
        conversationId,
        tenantId: this.tenantId
      });
    }
  }

  createConversation(participantId, initialMessage = '') {
    if (this.socket && this.isConnected) {
      this.socket.emit('create_conversation', {
        participantId,
        initialMessage,
        tenantId: this.tenantId
      });
    }
  }

  // Panic button methods
  activatePanicButton(location, coordinates) {
    if (this.socket && this.isConnected) {
      this.socket.emit('panic_button_activated', {
        location,
        coordinates,
        tenantId: this.tenantId
      });
    }
  }

  deactivatePanicButton() {
    if (this.socket && this.isConnected) {
      this.socket.emit('panic_button_deactivated', {
        tenantId: this.tenantId
      });
    }
  }

  // Geofence methods
  reportGeofenceBreach(geofenceId, location, coordinates) {
    if (this.socket && this.isConnected) {
      this.socket.emit('geofence_breach', {
        geofenceId,
        location,
        coordinates,
        tenantId: this.tenantId
      });
    }
  }

  // Notification methods
  acknowledgeNotification(notificationId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('acknowledge_notification', {
        notificationId,
        tenantId: this.tenantId
      });
    }
  }

  // User presence methods
  updateUserPresence(status) {
    if (this.socket && this.isConnected) {
      this.socket.emit('user_presence', {
        status,
        tenantId: this.tenantId
      });
    }
  }

  // File upload methods
  uploadFile(file, conversationId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('upload_file', {
        file,
        conversationId,
        tenantId: this.tenantId
      });
    }
  }
}

// Create singleton instance
const webSocketService = new WebSocketService();

export default webSocketService; 