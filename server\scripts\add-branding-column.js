const { pool } = require('../config/database');

async function addBrandingColumn() {
  try {
    console.log('Adding branding column to organizations table...');
    
    // Check if branding column already exists
    const columnCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'organizations' AND column_name = 'branding'
    `);
    
    if (columnCheck.rows.length > 0) {
      console.log('Branding column already exists');
      return;
    }
    
    // Add branding column
    await pool.query(`
      ALTER TABLE organizations 
      ADD COLUMN branding JSONB DEFAULT '{}'
    `);
    
    console.log('Branding column added successfully');
    
  } catch (error) {
    console.error('Error adding branding column:', error);
  } finally {
    process.exit(0);
  }
}

addBrandingColumn();
