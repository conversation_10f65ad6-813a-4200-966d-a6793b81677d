import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { userAPI } from '../../services/api';

// Helper function to extract error message
const getErrorMessage = (error) => {
  if (typeof error === 'string') return error;
  if (error?.response?.data?.message) return error.response.data.message;
  if (error?.response?.data?.error) return error.response.data.error;
  if (error?.response?.data && typeof error.response.data === 'string') return error.response.data;
  if (error?.message) return error.message;
  return 'An error occurred';
};

// Async thunks
export const fetchUsers = createAsyncThunk(
  'user/fetchUsers',
  async (organizationId, { rejectWithValue }) => {
    try {
      const response = await userAPI.getUsers(organizationId);
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to fetch users');
    }
  }
);

export const fetchUserById = createAsyncThunk(
  'user/fetchUserById',
  async ({ organizationId, userId }, { rejectWithValue }) => {
    try {
      const response = await userAPI.getUserById(organizationId, userId);
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to fetch user');
    }
  }
);

export const createUser = createAsyncThunk(
  'user/createUser',
  async ({ organizationId, userData }, { rejectWithValue }) => {
    try {
      const response = await userAPI.createUser(organizationId, userData);
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to create user');
    }
  }
);

export const updateUser = createAsyncThunk(
  'user/updateUser',
  async ({ organizationId, userId, userData }, { rejectWithValue }) => {
    try {
      const response = await userAPI.updateUser(organizationId, userId, userData);
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to update user');
    }
  }
);

export const deleteUser = createAsyncThunk(
  'user/deleteUser',
  async ({ organizationId, userId }, { rejectWithValue }) => {
    try {
      await userAPI.deleteUser(organizationId, userId);
      return userId;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to delete user');
    }
  }
);

export const fetchUserLocations = createAsyncThunk(
  'user/fetchUserLocations',
  async ({ organizationId, userId }, { rejectWithValue }) => {
    try {
      const response = await userAPI.getUserLocations(organizationId, userId);
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error) || 'Failed to fetch user locations');
    }
  }
);

const initialState = {
  users: [],
  currentUser: null,
  userLocations: [],
  isLoading: false,
  error: null,
  filters: {
    status: 'all',
    role: 'all',
    search: '',
  },
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
  },
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
      state.pagination.page = 1; // Reset to first page when filters change
    },
    setPagination: (state, action) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    updateUserStatus: (state, action) => {
      const { userId, status } = action.payload;
      const user = state.users.find(u => u.id === userId);
      if (user) {
        user.status = status;
      }
      if (state.currentUser?.id === userId) {
        state.currentUser.status = status;
      }
    },
    updateUserLocation: (state, action) => {
      const { userId, location } = action.payload;
      const user = state.users.find(u => u.id === userId);
      if (user) {
        user.currentLocation = location;
        user.lastSeen = new Date().toISOString();
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Users
      .addCase(fetchUsers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users = Array.isArray(action.payload) ? action.payload : action.payload.users || [];
        state.pagination = action.payload.pagination || { total: action.payload.length || 0, page: 1, limit: 50 };
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      
      // Fetch User by ID
      .addCase(fetchUserById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentUser = action.payload;
      })
      .addCase(fetchUserById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      
      // Create User
      .addCase(createUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users.unshift(action.payload);
      })
      .addCase(createUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      
      // Update User
      .addCase(updateUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.users.findIndex(u => u.id === action.payload.id);
        if (index !== -1) {
          state.users[index] = action.payload;
        }
        if (state.currentUser?.id === action.payload.id) {
          state.currentUser = action.payload;
        }
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      
      // Delete User
      .addCase(deleteUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users = state.users.filter(u => u.id !== action.payload);
        if (state.currentUser?.id === action.payload) {
          state.currentUser = null;
        }
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      
      // Fetch User Locations
      .addCase(fetchUserLocations.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserLocations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.userLocations = action.payload;
      })
      .addCase(fetchUserLocations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { 
  clearError, 
  setFilters, 
  setPagination, 
  updateUserStatus, 
  updateUserLocation 
} = userSlice.actions;

export default userSlice.reducer; 