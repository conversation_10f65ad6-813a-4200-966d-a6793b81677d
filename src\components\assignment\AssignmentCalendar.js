import React from 'react';
import { ChevronLeft, ChevronRight, Edit, Trash2 } from 'lucide-react';

const AssignmentCalendar = ({ 
  assignments, 
  employees, 
  locations, 
  selectedDate, 
  onDateChange, 
  onEdit, 
  onDelete 
}) => {
  const timeSlots = [];
  for (let hour = 6; hour <= 22; hour++) {
    timeSlots.push(`${hour.toString().padStart(2, '0')}:00`);
  }

  const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  const getWeekDates = (date) => {
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay());
    
    const weekDates = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      weekDates.push(day);
    }
    return weekDates;
  };

  const weekDates = getWeekDates(selectedDate);

  const getAssignmentsForTimeSlot = (date, timeSlot) => {
    const [hour] = timeSlot.split(':');
    const slotStart = new Date(date);
    slotStart.setHours(parseInt(hour), 0, 0, 0);
    const slotEnd = new Date(slotStart);
    slotEnd.setHours(parseInt(hour) + 1, 0, 0, 0);

    return assignments.filter(assignment => {
      const assignmentStart = new Date(assignment.startTime);
      const assignmentEnd = new Date(assignment.endTime);
      
      // Check if assignment overlaps with this time slot
      return assignmentStart < slotEnd && assignmentEnd > slotStart &&
             assignmentStart.toDateString() === date.toDateString();
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getEmployeeName = (employeeId) => {
    const employee = employees.find(emp => emp.id === employeeId);
    return employee ? employee.name : 'Unknown';
  };

  const getLocationName = (locationId) => {
    const location = locations.find(loc => loc.id === locationId);
    return location ? location.name : 'Unknown';
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'bg-green-100 text-green-800 border-green-200',
      inactive: 'bg-gray-100 text-gray-800 border-gray-200',
      completed: 'bg-blue-100 text-blue-800 border-blue-200',
      cancelled: 'bg-red-100 text-red-800 border-red-200'
    };
    return colors[status] || colors.inactive;
  };

  // Helper function to check if assignment is in the past
  const isAssignmentPast = (assignment) => {
    const endTime = new Date(assignment.endTime);
    const now = new Date();
    return endTime < now;
  };

  // Helper function to check if assignment is current (ongoing)
  const isAssignmentCurrent = (assignment) => {
    const startTime = new Date(assignment.startTime);
    const endTime = new Date(assignment.endTime);
    const now = new Date();
    return startTime <= now && now <= endTime;
  };

  return (
    <div className="p-4">
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <button
            onClick={() => {
              const newDate = new Date(selectedDate);
              newDate.setDate(newDate.getDate() - 7);
              onDateChange(newDate);
            }}
            className="p-2 hover:bg-gray-100 rounded-md"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          <h3 className="text-lg font-semibold">
            {weekDates[0].toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {weekDates[6].toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}
          </h3>
          <button
            onClick={() => {
              const newDate = new Date(selectedDate);
              newDate.setDate(newDate.getDate() + 7);
              onDateChange(newDate);
            }}
            className="p-2 hover:bg-gray-100 rounded-md"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </div>
        
        <button
          onClick={() => onDateChange(new Date())}
          className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
        >
          Today
        </button>
      </div>

      {/* Calendar Grid */}
      <div className="overflow-x-auto">
        <div className="min-w-[800px]">
          {/* Day Headers */}
          <div className="grid grid-cols-8 border-b border-gray-200">
            <div className="p-2 text-sm font-medium text-gray-500">Time</div>
            {weekDates.map((date, index) => (
              <div key={index} className="p-2 text-center">
                <div className="text-sm font-medium text-gray-900">
                  {daysOfWeek[index]}
                </div>
                <div className={`text-xs text-gray-500 ${
                  date.toDateString() === new Date().toDateString() ? 'text-primary-600 font-semibold' : ''
                }`}>
                  {date.getDate()}
                </div>
              </div>
            ))}
          </div>

          {/* Time Slots */}
          {timeSlots.map((timeSlot, timeIndex) => (
            <div key={timeIndex} className="grid grid-cols-8 border-b border-gray-100">
              {/* Time Label */}
              <div className="p-2 text-xs text-gray-500 border-r border-gray-100 flex items-center justify-end pr-2">
                {timeSlot}
              </div>

              {/* Day Columns */}
              {weekDates.map((date, dayIndex) => {
                const dayAssignments = getAssignmentsForTimeSlot(date, timeSlot);
                
                return (
                  <div key={dayIndex} className="p-1 border-r border-gray-100 min-h-[60px] relative">
                    {dayAssignments.map((assignment, assignmentIndex) => {
                      const assignmentStart = new Date(assignment.startTime);
                      const assignmentEnd = new Date(assignment.endTime);
                      const startHour = assignmentStart.getHours();
                      const endHour = assignmentEnd.getHours();
                      const currentHour = parseInt(timeSlot.split(':')[0]);
                      
                      // Only show assignment at its start time
                      if (startHour === currentHour) {
                        const duration = endHour - startHour;
                        const height = Math.max(duration * 60, 30); // Minimum 30px height
                        
                        const isPast = isAssignmentPast(assignment);
                        const isCurrent = isAssignmentCurrent(assignment);
                        const baseColor = getStatusColor(assignment.status);
                        const timeIndicator = isPast ? 'opacity-60 border-dashed' : isCurrent ? 'ring-2 ring-blue-400' : '';

                        return (
                          <div
                            key={assignmentIndex}
                            className={`absolute left-1 right-1 rounded-md p-2 text-xs border cursor-pointer hover:shadow-md transition-shadow ${baseColor} ${timeIndicator}`}
                            style={{
                              top: '4px',
                              height: `${height}px`,
                              zIndex: 10
                            }}
                            onClick={() => !isPast && onEdit(assignment)}
                          >
                            <div className="font-medium truncate flex items-center gap-1">
                              {getEmployeeName(assignment.employeeId)}
                              {isPast && <span className="text-xs text-gray-500">(Past)</span>}
                              {isCurrent && <span className="text-xs text-blue-600 font-semibold">(Current)</span>}
                            </div>
                            <div className="text-xs opacity-75 truncate">
                              {getLocationName(assignment.locationId)}
                            </div>
                            <div className="text-xs opacity-75">
                              {formatTime(assignment.startTime)} - {formatTime(assignment.endTime)}
                            </div>
                            
                            {/* Action buttons */}
                            <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <div className="flex gap-1">
                                {isAssignmentPast(assignment) ? (
                                  <>
                                    <button
                                      disabled
                                      className="p-1 bg-gray-100 rounded shadow-sm cursor-not-allowed text-gray-400"
                                      title="Cannot edit past assignments"
                                    >
                                      <Edit className="h-3 w-3" />
                                    </button>
                                    <button
                                      disabled
                                      className="p-1 bg-gray-100 rounded shadow-sm cursor-not-allowed text-gray-400"
                                      title="Cannot delete past assignments"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </button>
                                  </>
                                ) : (
                                  <>
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        onEdit(assignment);
                                      }}
                                      className="p-1 bg-white rounded shadow-sm hover:bg-gray-50"
                                      title={isAssignmentCurrent(assignment) ? "Edit current assignment" : "Edit assignment"}
                                    >
                                      <Edit className="h-3 w-3" />
                                    </button>
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        onDelete(assignment.id);
                                      }}
                                      className="p-1 bg-white rounded shadow-sm hover:bg-gray-50 text-red-600"
                                      title="Delete assignment"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </button>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      }
                      return null;
                    })}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>

      {/* Legend */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Status</h4>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-100 border border-green-200 rounded"></div>
                <span className="text-sm text-gray-600">Active</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-gray-100 border border-gray-200 rounded"></div>
                <span className="text-sm text-gray-600">Inactive</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-100 border border-blue-200 rounded"></div>
                <span className="text-sm text-gray-600">Completed</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-100 border border-red-200 rounded"></div>
                <span className="text-sm text-gray-600">Cancelled</span>
              </div>
            </div>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Time Status</h4>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-gray-100 border border-dashed border-gray-400 rounded opacity-60"></div>
                <span className="text-sm text-gray-600">Past Assignment</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-100 border border-blue-400 rounded ring-1 ring-blue-400"></div>
                <span className="text-sm text-gray-600">Current Assignment</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-100 border border-green-200 rounded"></div>
                <span className="text-sm text-gray-600">Future Assignment</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignmentCalendar; 