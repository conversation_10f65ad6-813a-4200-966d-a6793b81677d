import React, { useEffect, useRef } from 'react';
import { User, Check, CheckCheck, Clock, MessageSquare } from 'lucide-react';
import { useSelector } from 'react-redux';

const MessageThread = ({ 
  messages, 
  conversation, 
  typingUsers, 
  loading, 
  messagesEndRef 
}) => {
  const { user } = useSelector((state) => state.auth);
  const messagesContainerRef = useRef(null);

  // Auto-scroll to bottom on new messages
  useEffect(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, [messages]);

  const formatMessageTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatMessageDate = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return date.toLocaleDateString('en-US', { weekday: 'long' });
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  };

  const getReadReceipt = (message) => {
    if (message.senderId !== user.id) return null;
    
    if (message.readAt) {
      return <CheckCheck className="h-3 w-3 text-blue-500" />;
    } else if (message.deliveredAt) {
      return <CheckCheck className="h-3 w-3 text-gray-400" />;
    } else {
      return <Check className="h-3 w-3 text-gray-400" />;
    }
  };

  const renderMessage = (message, index) => {
    const isOwnMessage = message.senderId === user.id;
    const showDate = index === 0 || 
      formatMessageDate(message.timestamp) !== formatMessageDate(messages[index - 1]?.timestamp);
    
    return (
      <div key={message.id}>
        {/* Date Separator */}
        {showDate && (
          <div className="flex justify-center my-4">
            <span className="bg-gray-100 text-gray-500 text-xs px-3 py-1 rounded-full">
              {formatMessageDate(message.timestamp)}
            </span>
          </div>
        )}

        {/* Message */}
        <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-2`}>
          <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
            isOwnMessage 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-100 text-gray-900'
          }`}>
            {/* Message Content */}
            <div className="break-words">
              {message.content}
            </div>

            {/* Message Metadata */}
            <div className={`flex items-center justify-end space-x-1 mt-1 ${
              isOwnMessage ? 'text-blue-100' : 'text-gray-500'
            }`}>
              <span className="text-xs">
                {formatMessageTime(message.timestamp)}
              </span>
              {getReadReceipt(message)}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      {conversation && (
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                {conversation.participant?.avatar ? (
                  <img 
                    src={conversation.participant.avatar} 
                    alt={conversation.participant.name}
                    className="h-8 w-8 rounded-full object-cover"
                  />
                ) : (
                  <User className="h-4 w-4 text-gray-500" />
                )}
              </div>
              <div className={`absolute -bottom-1 -right-1 h-2 w-2 rounded-full border border-white ${
                conversation.participant?.online ? 'bg-green-500' : 'bg-gray-400'
              }`}></div>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">
                {conversation.participant?.name || 'Unknown User'}
              </h3>
              <p className="text-sm text-gray-500">
                {conversation.participant?.online ? 'Online' : 'Offline'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Messages */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-2"
      >
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <MessageSquare className="h-12 w-12 mx-auto mb-2 text-gray-300" />
              <p>No messages yet</p>
              <p className="text-sm">Start a conversation!</p>
            </div>
          </div>
        ) : (
          messages.map((message, index) => renderMessage(message, index))
        )}

        {/* Typing Indicator */}
        {typingUsers.length > 0 && (
          <div className="flex justify-start mb-2">
            <div className="bg-gray-100 text-gray-900 px-4 py-2 rounded-lg">
              <div className="flex items-center space-x-1">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-xs text-gray-500 ml-2">
                  {typingUsers.length === 1 
                    ? `${typingUsers[0]} is typing...`
                    : `${typingUsers.join(', ')} are typing...`
                  }
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Scroll to bottom reference */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default MessageThread; 