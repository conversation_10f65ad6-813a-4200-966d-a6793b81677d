import React from 'react';
import { useParams } from 'react-router-dom';
import { TenantProvider } from '../tenant/TenantProvider';
import { BrandingProvider } from '../tenant/BrandingProvider';

const AuthLayout = ({ children }) => {
  const { tenantId } = useParams();
  
  // Wrap with providers for tenant-specific branding
  if (tenantId) {
    return (
      <TenantProvider>
        <BrandingProvider>
          {children}
        </BrandingProvider>
      </TenantProvider>
    );
  }
  
  // Default layout for non-tenant auth pages (like login)
  return (
    <div className="login-container">
      {children}
    </div>
  );
};

export default AuthLayout; 