import React, { useState, useEffect } from 'react';
import { subscriptionPlanAPI } from '../../services/api';
import {
  Plus,
  Edit,
  Trash2,
  DollarSign,
  Users,
  MapPin,
  Clock,
  Database,
  Zap,
  Eye,
  EyeOff,
  Save,
  X,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

const PricingManagement = () => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingPlan, setEditingPlan] = useState(null);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await subscriptionPlanAPI.getPlans();
      setPlans(response.data || []);
    } catch (error) {
      console.error('Error fetching plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePlan = () => {
    setEditingPlan(null);
    setShowForm(true);
  };

  const handleEditPlan = (plan) => {
    setEditingPlan(plan);
    setShowForm(true);
  };

  const handleDeletePlan = async (planId) => {
    if (window.confirm('Are you sure you want to delete this plan?')) {
      try {
        await subscriptionPlanAPI.deletePlan(planId);
        fetchPlans();
      } catch (error) {
        console.error('Error deleting plan:', error);
        alert('Failed to delete plan. It may be in use by organizations.');
      }
    }
  };

  const handleToggleActive = async (planId) => {
    try {
      await subscriptionPlanAPI.togglePlanActive(planId);
      fetchPlans();
    } catch (error) {
      console.error('Error toggling plan status:', error);
    }
  };

  const handleFormSubmit = async (planData) => {
    try {
      if (editingPlan) {
        await subscriptionPlanAPI.updatePlan(editingPlan.id, planData);
      } else {
        await subscriptionPlanAPI.createPlan(planData);
      }
      setShowForm(false);
      setEditingPlan(null);
      fetchPlans();
    } catch (error) {
      console.error('Error saving plan:', error);
      throw error;
    }
  };

  const getPlanBadgeColor = (planName) => {
    switch (planName.toLowerCase()) {
      case 'basic': return 'bg-blue-100 text-blue-800';
      case 'standard': return 'bg-green-100 text-green-800';
      case 'premium': return 'bg-purple-100 text-purple-800';
      case 'enterprise': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Pricing Plans</h1>
          <p className="text-gray-600">Manage subscription plans and pricing tiers</p>
        </div>
        <button
          onClick={handleCreatePlan}
          className="btn-primary flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add New Plan
        </button>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {plans.map((plan) => (
          <div key={plan.id} className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
            {/* Plan Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPlanBadgeColor(plan.name)}`}>
                  {plan.name}
                </span>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => handleToggleActive(plan.id)}
                    className={`p-1 rounded ${plan.is_active ? 'text-green-600 hover:text-green-800' : 'text-gray-400 hover:text-gray-600'}`}
                    title={plan.is_active ? 'Active' : 'Inactive'}
                  >
                    {plan.is_active ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  </button>
                  <button
                    onClick={() => handleEditPlan(plan)}
                    className="p-1 text-blue-600 hover:text-blue-800"
                    title="Edit Plan"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeletePlan(plan.id)}
                    className="p-1 text-red-600 hover:text-red-800"
                    title="Delete Plan"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">{plan.display_name}</h3>
              <p className="text-sm text-gray-600 mt-1">{plan.description}</p>
            </div>

            {/* Pricing */}
            <div className="p-6 border-b border-gray-200">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900">
                  ${parseFloat(plan.monthly_price).toFixed(2)}
                  <span className="text-lg font-normal text-gray-600">/month</span>
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  ${parseFloat(plan.annual_price).toFixed(2)}/year
                </div>
                <div className="text-sm text-blue-600 mt-2 font-medium">
                  ${parseFloat(plan.per_user_fee).toFixed(2)} per user
                </div>
              </div>
            </div>

            {/* Limits */}
            <div className="p-6 space-y-3">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center text-gray-600">
                  <Users className="h-4 w-4 mr-2" />
                  Max Users
                </div>
                <span className="font-medium text-gray-900">
                  {plan.max_users === 999999 ? 'Unlimited' : plan.max_users.toLocaleString()}
                </span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center text-gray-600">
                  <MapPin className="h-4 w-4 mr-2" />
                  Max Locations
                </div>
                <span className="font-medium text-gray-900">
                  {plan.max_locations === 999999 ? 'Unlimited' : plan.max_locations.toLocaleString()}
                </span>
              </div>
            </div>

            {/* Features */}
            {plan.features && plan.features.length > 0 && (
              <div className="p-6 pt-0">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Features</h4>
                <ul className="space-y-1">
                  {plan.features.slice(0, 3).map((feature, index) => (
                    <li key={index} className="flex items-center text-xs text-gray-600">
                      <CheckCircle className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                  {plan.features.length > 3 && (
                    <li className="text-xs text-gray-500">
                      +{plan.features.length - 3} more features
                    </li>
                  )}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Plan Form Modal */}
      {showForm && (
        <PlanFormModal
          plan={editingPlan}
          onSubmit={handleFormSubmit}
          onClose={() => {
            setShowForm(false);
            setEditingPlan(null);
          }}
        />
      )}
    </div>
  );
};

// Plan Form Modal Component
const PlanFormModal = ({ plan, onSubmit, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    display_name: '',
    description: '',
    monthly_price: 0,
    annual_price: 0,
    per_user_fee: 5.00,
    max_users: 10,
    max_locations: 3,
    features: [],
    is_active: true
  });
  const [featuresText, setFeaturesText] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (plan) {
      setFormData({
        name: plan.name || '',
        display_name: plan.display_name || '',
        description: plan.description || '',
        monthly_price: parseFloat(plan.monthly_price) || 0,
        annual_price: parseFloat(plan.annual_price) || 0,
        per_user_fee: parseFloat(plan.per_user_fee) || 5.00,
        max_users: plan.max_users || 10,
        max_locations: plan.max_locations || 3,
        features: plan.features || [],
        is_active: plan.is_active !== undefined ? plan.is_active : true
      });
      setFeaturesText((plan.features || []).join('\n'));
    }
  }, [plan]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Plan name is required';
    }
    
    if (!formData.display_name.trim()) {
      newErrors.display_name = 'Display name is required';
    }
    
    if (formData.monthly_price < 0) {
      newErrors.monthly_price = 'Monthly price must be 0 or greater';
    }
    
    if (formData.annual_price < 0) {
      newErrors.annual_price = 'Annual price must be 0 or greater';
    }
    
    if (formData.per_user_fee < 0) {
      newErrors.per_user_fee = 'Per user fee must be 0 or greater';
    }
    
    if (formData.max_users < 1) {
      newErrors.max_users = 'Max users must be at least 1';
    }
    
    if (formData.max_locations < 1) {
      newErrors.max_locations = 'Max locations must be at least 1';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      
      const submitData = {
        ...formData,
        features: featuresText.split('\n').filter(f => f.trim()).map(f => f.trim())
      };
      
      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('Failed to save plan');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            {plan ? 'Edit Plan' : 'Create New Plan'}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-6 w-6" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Plan Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="e.g., basic, standard, premium"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Display Name *
              </label>
              <input
                type="text"
                value={formData.display_name}
                onChange={(e) => handleInputChange('display_name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.display_name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="e.g., Basic Plan, Standard Plan"
              />
              {errors.display_name && <p className="mt-1 text-sm text-red-600">{errors.display_name}</p>}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows="2"
              placeholder="Brief description of the plan"
            />
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Monthly Price ($) *
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.monthly_price}
                onChange={(e) => handleInputChange('monthly_price', parseFloat(e.target.value) || 0)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.monthly_price ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.monthly_price && <p className="mt-1 text-sm text-red-600">{errors.monthly_price}</p>}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Annual Price ($) *
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.annual_price}
                onChange={(e) => handleInputChange('annual_price', parseFloat(e.target.value) || 0)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.annual_price ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.annual_price && <p className="mt-1 text-sm text-red-600">{errors.annual_price}</p>}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Per User Fee ($) *
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.per_user_fee}
                onChange={(e) => handleInputChange('per_user_fee', parseFloat(e.target.value) || 0)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.per_user_fee ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.per_user_fee && <p className="mt-1 text-sm text-red-600">{errors.per_user_fee}</p>}
            </div>
          </div>

          {/* Limits */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Max Users *
              </label>
              <input
                type="number"
                min="1"
                value={formData.max_users}
                onChange={(e) => handleInputChange('max_users', parseInt(e.target.value) || 1)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.max_users ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.max_users && <p className="mt-1 text-sm text-red-600">{errors.max_users}</p>}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Max Locations *
              </label>
              <input
                type="number"
                min="1"
                value={formData.max_locations}
                onChange={(e) => handleInputChange('max_locations', parseInt(e.target.value) || 1)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.max_locations ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.max_locations && <p className="mt-1 text-sm text-red-600">{errors.max_locations}</p>}
            </div>
          </div>

          {/* Features */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Features (one per line)
            </label>
            <textarea
              value={featuresText}
              onChange={(e) => setFeaturesText(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows="6"
              placeholder="Enter each feature on a new line..."
            />
          </div>

          {/* Active Status */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => handleInputChange('is_active', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
              Plan is active
            </label>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={loading}
            >
              {loading ? 'Saving...' : (plan ? 'Update Plan' : 'Create Plan')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PricingManagement;
