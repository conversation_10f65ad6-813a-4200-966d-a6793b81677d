const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const { pool } = require('./config/database');
const { initializeDatabase } = require('./database/init');
const authRoutes = require('./routes/auth');
const organizationRoutes = require('./routes/organizations');
const userRoutes = require('./routes/users');
const locationRoutes = require('./routes/locations');
const assignmentRoutes = require('./routes/assignments');
const incidentRoutes = require('./routes/incidents');
const reportRoutes = require('./routes/reports');
const messagingRoutes = require('./routes/messaging');
const geofenceRoutes = require('./routes/geofences');
const userLocationRoutes = require('./routes/user-locations');
const invoiceRoutes = require('./routes/invoices');
const subscriptionPlanRoutes = require('./routes/subscription-plans');
const billingRoutes = require('./routes/billing');

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: [
    process.env.FRONTEND_URL || 'http://localhost:3000',
    'http://localhost:3002',  // Allow React app on port 3002
    'http://localhost:3003'   // Allow React app on port 3003
  ],
  credentials: true
}));

// Rate limiting - Very generous for development, disabled in dev mode
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'production' ? 1000 : 10000, // 10000 requests for dev, 1000 for production
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  skip: (req) => process.env.NODE_ENV === 'development', // Skip rate limiting in development
});
app.use('/api/', limiter);

// Logging
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Test database connection
    await pool.query('SELECT NOW()');
    res.json({ 
      status: 'healthy', 
      timestamp: new Date().toISOString(),
      database: 'connected'
    });
  } catch (error) {
    res.status(500).json({ 
      status: 'unhealthy', 
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    });
  }
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/organizations/:organizationId/users', userRoutes);
app.use('/api/organizations/:organizationId/locations', locationRoutes);
app.use('/api/organizations/:organizationId/assignments', assignmentRoutes);
app.use('/api/organizations/:organizationId/incidents', incidentRoutes);
app.use('/api/organizations/:organizationId/reports', reportRoutes);
app.use('/api/organizations/:organizationId/messages', messagingRoutes);
app.use('/api/organizations/:organizationId/geofences', geofenceRoutes);
app.use('/api/organizations/:organizationId/user-locations', userLocationRoutes);
app.use('/api/organizations/:organizationId/invoices', invoiceRoutes);
app.use('/api/subscription-plans', subscriptionPlanRoutes);
app.use('/api/billing', billingRoutes); // Super admin billing management
app.use('/api/organizations', organizationRoutes);

// Super Admin API Routes (alias routes for super admin access)
app.use('/api/super-admin/organizations', organizationRoutes);
app.use('/api/super-admin/organizations/:organizationId/users', userRoutes);
app.use('/api/super-admin/organizations/:organizationId/locations', locationRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Initialize database and start server
const startServer = async () => {
  try {
    console.log('Initializing database...');
    await initializeDatabase();
    
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
      console.log(`Health check available at http://localhost:${PORT}/health`);
      console.log(`API base URL: http://localhost:${PORT}/api`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer(); 