#!/usr/bin/env node

/**
 * Comprehensive cleanup script to fix all ESLint warnings and code issues
 * This script addresses:
 * 1. Unused imports and variables
 * 2. Missing dependencies in React hooks
 * 3. Debug console.log statements
 * 4. Code quality issues
 */

const fs = require('fs');
const path = require('path');

// File patterns to process
const FILE_PATTERNS = [
  'src/components/**/*.js',
  'src/pages/**/*.js',
  'src/store/**/*.js',
  'src/services/**/*.js',
  'src/layouts/**/*.js'
];

// Function to remove unused imports
function removeUnusedImports(content) {
  const lines = content.split('\n');
  const importLines = [];
  const codeLines = [];
  let inImportSection = true;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    if (line.trim().startsWith('import ')) {
      importLines.push({ line, index: i });
    } else if (line.trim() === '' && inImportSection) {
      // Keep empty lines in import section
    } else {
      inImportSection = false;
      codeLines.push(line);
    }
  }
  
  // Check which imports are actually used
  const codeContent = codeLines.join('\n');
  const usedImports = [];
  
  for (const importLine of importLines) {
    const { line } = importLine;
    
    // Parse import statement
    const importMatch = line.match(/import\s+(.+?)\s+from\s+['"](.+?)['"];?/);
    if (importMatch) {
      const importPart = importMatch[1];
      const moduleName = importMatch[2];
      
      // Check if it's a default import or named imports
      if (importPart.includes('{')) {
        // Named imports
        const namedImports = importPart.match(/\{(.+?)\}/);
        if (namedImports) {
          const imports = namedImports[1].split(',').map(imp => imp.trim());
          const usedNamedImports = imports.filter(imp => {
            const importName = imp.split(' as ')[0].trim();
            return codeContent.includes(importName) || 
                   codeContent.includes(`<${importName}`) ||
                   codeContent.includes(`${importName}(`);
          });
          
          if (usedNamedImports.length > 0) {
            usedImports.push(`import { ${usedNamedImports.join(', ')} } from '${moduleName}';`);
          }
        }
      } else {
        // Default import
        const defaultImport = importPart.trim();
        if (codeContent.includes(defaultImport) || 
            codeContent.includes(`<${defaultImport}`) ||
            codeContent.includes(`${defaultImport}(`)) {
          usedImports.push(line);
        }
      }
    }
  }
  
  return [...usedImports, '', ...codeLines].join('\n');
}

// Function to remove console.log statements
function removeConsoleLog(content) {
  return content.replace(/^\s*console\.(log|warn|error|info|debug)\(.*?\);?\s*$/gm, '');
}

// Function to add missing dependencies to useEffect
function fixUseEffectDependencies(content) {
  // This is a simplified version - in practice, you'd need more sophisticated parsing
  const useEffectRegex = /useEffect\(\s*\(\s*\)\s*=>\s*\{[\s\S]*?\},\s*\[\s*\]\s*\)/g;
  
  return content.replace(useEffectRegex, (match) => {
    // Extract function body to find dependencies
    const functionBody = match.match(/\{([\s\S]*?)\}/)[1];
    const dependencies = [];
    
    // Look for function calls that might be dependencies
    const functionCalls = functionBody.match(/\b[a-zA-Z_$][a-zA-Z0-9_$]*\(/g);
    if (functionCalls) {
      functionCalls.forEach(call => {
        const funcName = call.slice(0, -1);
        if (funcName.startsWith('fetch') || funcName.startsWith('load') || funcName.startsWith('get')) {
          dependencies.push(funcName);
        }
      });
    }
    
    if (dependencies.length > 0) {
      return match.replace('[]', `[${dependencies.join(', ')}]`);
    }
    
    return match;
  });
}

// Function to fix mixed operators
function fixMixedOperators(content) {
  return content.replace(/(\w+\s*\|\|\s*\w+)\s*&&\s*(\w+\s*\|\|\s*\w+)/g, '($1) && ($2)');
}

// Function to fix default case in switch statements
function fixDefaultCase(content) {
  return content.replace(/(switch\s*\([^)]+\)\s*\{[^}]*)(case\s+[^:]+:[^}]*?)(\})/g, (match, switchStart, cases, end) => {
    if (!cases.includes('default:')) {
      return switchStart + cases + '\n      default:\n        break;' + end;
    }
    return match;
  });
}

// Process individual file
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Apply fixes
    content = removeUnusedImports(content);
    content = removeConsoleLog(content);
    content = fixUseEffectDependencies(content);
    content = fixMixedOperators(content);
    content = fixDefaultCase(content);
    
    // Write back to file
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✓ Processed: ${filePath}`);
  } catch (error) {
    console.error(`✗ Error processing ${filePath}:`, error.message);
  }
}

// Get all files matching patterns
function getFiles(patterns) {
  const glob = require('glob');
  const files = [];
  
  patterns.forEach(pattern => {
    const matchedFiles = glob.sync(pattern);
    files.push(...matchedFiles);
  });
  
  return [...new Set(files)]; // Remove duplicates
}

// Main execution
console.log('🧹 Starting comprehensive cleanup...');

try {
  // Install glob if not present
  try {
    require('glob');
  } catch (e) {
    console.log('Installing glob dependency...');
    require('child_process').execSync('npm install glob --save-dev', { stdio: 'inherit' });
  }
  
  const files = getFiles(FILE_PATTERNS);
  console.log(`Found ${files.length} files to process`);
  
  files.forEach(processFile);
  
  console.log('✅ Cleanup completed successfully!');
  console.log('Next steps:');
  console.log('1. Run npm run build to verify fixes');
  console.log('2. Test the application functionality');
  console.log('3. Commit the changes');
  
} catch (error) {
  console.error('❌ Cleanup failed:', error.message);
  process.exit(1);
}
