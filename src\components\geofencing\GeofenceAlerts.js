import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  Clock, 
  MapPin, 
  Users, 
  CheckCircle, 
  XCircle,
  Bell,
  Filter,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import BrandedButton from '../BrandedButton';

const GeofenceAlerts = ({ 
  alerts, 
  onAcknowledge, 
  loading 
}) => {
  const [filters, setFilters] = useState({
    status: 'all', // all, acknowledged, unacknowledged
    type: 'all', // all, entry, exit, unauthorized_entry, unauthorized_exit, dwell_time
    severity: 'all', // all, low, medium, high, critical
    timeRange: '24h' // 1h, 24h, 7d, 30d
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState(null);

  // Filter alerts based on current filters
  const filteredAlerts = (alerts || []).filter(alert => {
    if (filters.status !== 'all') {
      const isAcknowledged = alert.acknowledged;
      if (filters.status === 'acknowledged' && !isAcknowledged) return false;
      if (filters.status === 'unacknowledged' && isAcknowledged) return false;
    }

    if (filters.type !== 'all' && alert.type !== filters.type) {
      return false;
    }

    if (filters.severity !== 'all' && alert.severity !== filters.severity) {
      return false;
    }

    // Time range filter
    const alertTime = new Date(alert.timestamp);
    const now = new Date();
    const timeDiff = now - alertTime;
    
    switch (filters.timeRange) {
      case '1h':
        if (timeDiff > 60 * 60 * 1000) return false;
        break;
      case '24h':
        if (timeDiff > 24 * 60 * 60 * 1000) return false;
        break;
      case '7d':
        if (timeDiff > 7 * 24 * 60 * 60 * 1000) return false;
        break;
      case '30d':
        if (timeDiff > 30 * 24 * 60 * 60 * 1000) return false;
        break;
    }

    return true;
  });

  const getAlertIcon = (type) => {
    const iconMap = {
      entry: Users,
      exit: Users,
      unauthorized_entry: AlertTriangle,
      unauthorized_exit: AlertTriangle,
      dwell_time: Clock
    };
    return iconMap[type] || AlertTriangle;
  };

  const getAlertColor = (severity) => {
    const colorMap = {
      low: 'bg-blue-100 text-blue-800 border-blue-200',
      medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      high: 'bg-orange-100 text-orange-800 border-orange-200',
      critical: 'bg-red-100 text-red-800 border-red-200'
    };
    return colorMap[severity] || colorMap.medium;
  };

  const getAlertTypeLabel = (type) => {
    const labelMap = {
      entry: 'Entry',
      exit: 'Exit',
      unauthorized_entry: 'Unauthorized Entry',
      unauthorized_exit: 'Unauthorized Exit',
      dwell_time: 'Dwell Time Exceeded'
    };
    return labelMap[type] || type;
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    if (diff < 60 * 1000) return 'Just now';
    if (diff < 60 * 60 * 1000) return `${Math.floor(diff / (60 * 1000))}m ago`;
    if (diff < 24 * 60 * 60 * 1000) return `${Math.floor(diff / (60 * 60 * 1000))}h ago`;
    return date.toLocaleDateString();
  };

  const handleAcknowledgeAlert = (alertId) => {
    onAcknowledge(alertId);
    setSelectedAlert(null);
  };

  const getAlertStats = () => {
    const alertsArray = alerts || [];
    const stats = {
      total: alertsArray.length,
      unacknowledged: alertsArray.filter(a => !a.acknowledged).length,
      critical: alertsArray.filter(a => a.severity === 'critical' && !a.acknowledged).length,
      today: alertsArray.filter(a => {
        const alertDate = new Date(a.timestamp);
        const today = new Date();
        return alertDate.toDateString() === today.toDateString();
      }).length
    };
    return stats;
  };

  const stats = getAlertStats();

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600">Loading alerts...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Alert Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Alerts</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
            <Bell className="h-8 w-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Unacknowledged</p>
              <p className="text-2xl font-bold text-gray-900">{stats.unacknowledged}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical</p>
              <p className="text-2xl font-bold text-gray-900">{stats.critical}</p>
            </div>
            <XCircle className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Today</p>
              <p className="text-2xl font-bold text-gray-900">{stats.today}</p>
            </div>
            <Clock className="h-8 w-8 text-green-400" />
          </div>
        </div>
      </div>

      {/* Alerts List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Geofence Alerts ({filteredAlerts.length})
            </h3>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <Filter className="h-4 w-4" />
                Filters
              </button>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="unacknowledged">Unacknowledged</option>
                    <option value="acknowledged">Acknowledged</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type
                  </label>
                  <select
                    value={filters.type}
                    onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Types</option>
                    <option value="entry">Entry</option>
                    <option value="exit">Exit</option>
                    <option value="unauthorized_entry">Unauthorized Entry</option>
                    <option value="unauthorized_exit">Unauthorized Exit</option>
                    <option value="dwell_time">Dwell Time</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Severity
                  </label>
                  <select
                    value={filters.severity}
                    onChange={(e) => setFilters({ ...filters, severity: e.target.value })}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Severities</option>
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Time Range
                  </label>
                  <select
                    value={filters.timeRange}
                    onChange={(e) => setFilters({ ...filters, timeRange: e.target.value })}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="1h">Last Hour</option>
                    <option value="24h">Last 24 Hours</option>
                    <option value="7d">Last 7 Days</option>
                    <option value="30d">Last 30 Days</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Alerts List */}
        <div className="divide-y divide-gray-200">
          {filteredAlerts.length === 0 ? (
            <div className="px-6 py-12 text-center">
              <div className="flex flex-col items-center">
                <Bell className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No alerts found</h3>
                <p className="text-gray-500">
                  {filters.status !== 'all' || filters.type !== 'all' || filters.severity !== 'all'
                    ? 'Try adjusting your filters to see more results.'
                    : 'All clear! No geofence alerts at the moment.'}
                </p>
              </div>
            </div>
          ) : (
            filteredAlerts.map((alert) => {
              const Icon = getAlertIcon(alert.type);
              
              return (
                <div
                  key={alert.id}
                  className={`px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                    selectedAlert?.id === alert.id ? 'bg-blue-50' : ''
                  } ${!alert.acknowledged ? 'border-l-4 border-red-500' : ''}`}
                  onClick={() => setSelectedAlert(alert)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className={`p-2 rounded-lg ${getAlertColor(alert.severity)}`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="text-sm font-medium text-gray-900">
                            {getAlertTypeLabel(alert.type)}
                          </h4>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getAlertColor(alert.severity)}`}>
                            {alert.severity}
                          </span>
                          {!alert.acknowledged && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              New
                            </span>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">
                          {alert.description}
                        </p>
                        
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Users className="h-3 w-3" />
                            <span>{alert.employeeName}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span>{alert.geofenceName}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{formatTimestamp(alert.timestamp)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {!alert.acknowledged && (
                        <BrandedButton
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAcknowledgeAlert(alert.id);
                          }}
                          className="px-3 py-1 text-xs"
                        >
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Acknowledge
                        </BrandedButton>
                      )}
                      {alert.acknowledged && (
                        <span className="text-xs text-gray-500 flex items-center">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Acknowledged
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Alert Details Modal */}
      {selectedAlert && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedAlert(null)} />
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">
                    Alert Details
                  </h3>
                  <button
                    onClick={() => setSelectedAlert(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XCircle className="h-6 w-6" />
                  </button>
                </div>
              </div>
              
              <div className="px-6 py-4 space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    {getAlertTypeLabel(selectedAlert.type)}
                  </h4>
                  <p className="text-sm text-gray-600">{selectedAlert.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Employee:</span>
                    <p className="text-gray-600">{selectedAlert.employeeName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Geofence:</span>
                    <p className="text-gray-600">{selectedAlert.geofenceName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Severity:</span>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getAlertColor(selectedAlert.severity)}`}>
                      {selectedAlert.severity}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Time:</span>
                    <p className="text-gray-600">{new Date(selectedAlert.timestamp).toLocaleString()}</p>
                  </div>
                </div>
                
                {selectedAlert.location && (
                  <div>
                    <span className="font-medium text-gray-700">Location:</span>
                    <p className="text-gray-600">
                      {selectedAlert.location.lat}, {selectedAlert.location.lng}
                    </p>
                  </div>
                )}
                
                {!selectedAlert.acknowledged && (
                  <div className="pt-4 border-t border-gray-200">
                    <BrandedButton
                      onClick={() => handleAcknowledgeAlert(selectedAlert.id)}
                      className="w-full"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Acknowledge Alert
                    </BrandedButton>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GeofenceAlerts; 