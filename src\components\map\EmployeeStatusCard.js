import React from 'react';
import { 
  User, 
  MapPin, 
  Clock, 
  Phone, 
  Mail, 
  CheckCircle, 
  AlertTriangle, 
  Coffee,
  Wifi,
  WifiOff,
  X
} from 'lucide-react';

const EmployeeStatusCard = ({ employee, location, onClose }) => {
  const getStatusConfig = (status) => {
    const configs = {
      on_duty: { 
        color: 'bg-green-500', 
        textColor: 'text-green-800',
        bgColor: 'bg-green-50',
        icon: CheckCircle, 
        label: 'On Duty' 
      },
      late: { 
        color: 'bg-yellow-500', 
        textColor: 'text-yellow-800',
        bgColor: 'bg-yellow-50',
        icon: Clock, 
        label: 'Late' 
      },
      on_break: { 
        color: 'bg-blue-500', 
        textColor: 'text-blue-800',
        bgColor: 'bg-blue-50',
        icon: Coffee, 
        label: 'On Break' 
      },
      out_of_zone: { 
        color: 'bg-red-500', 
        textColor: 'text-red-800',
        bgColor: 'bg-red-50',
        icon: AlertTriangle, 
        label: 'Out of Zone' 
      },
      offline: { 
        color: 'bg-gray-500', 
        textColor: 'text-gray-800',
        bgColor: 'bg-gray-50',
        icon: WifiOff, 
        label: 'Offline' 
      },
      online: { 
        color: 'bg-green-500', 
        textColor: 'text-green-800',
        bgColor: 'bg-green-50',
        icon: Wifi, 
        label: 'Online' 
      }
    };
    return configs[status] || configs.offline;
  };

  const statusConfig = getStatusConfig(employee.status);
  const StatusIcon = statusConfig.icon;

  const formatTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getTimeSinceLastUpdate = () => {
    if (!location?.lastUpdate) return 'Unknown';
    
    const now = new Date();
    const lastUpdate = new Date(location.lastUpdate);
    const diffMs = now - lastUpdate;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  return (
    <div className="w-80 bg-white rounded-lg shadow-xl border border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
              <User className="h-6 w-6 text-gray-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{employee.name}</h3>
              <p className="text-sm text-gray-600">{employee.email}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Status Section */}
      <div className={`p-4 ${statusConfig.bgColor}`}>
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${statusConfig.color}`}></div>
          <StatusIcon className={`h-4 w-4 ${statusConfig.textColor}`} />
          <span className={`text-sm font-medium ${statusConfig.textColor}`}>
            {statusConfig.label}
          </span>
        </div>
        {employee.status === 'late' && employee.expectedTime && (
          <p className="text-sm text-gray-600 mt-1">
            Expected: {formatTime(employee.expectedTime)}
          </p>
        )}
        {employee.status === 'on_break' && employee.breakEndTime && (
          <p className="text-sm text-gray-600 mt-1">
            Break ends: {formatTime(employee.breakEndTime)}
          </p>
        )}
      </div>

      {/* Employee Details */}
      <div className="p-4 space-y-3">
        <div className="flex items-center gap-3">
          <Phone className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-600">{employee.phone || 'N/A'}</span>
        </div>
        
        <div className="flex items-center gap-3">
          <Mail className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-600">{employee.email}</span>
        </div>
        
        {employee.department && (
          <div className="flex items-center gap-3">
            <User className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">{employee.department}</span>
          </div>
        )}
      </div>

      {/* Location Details */}
      {location && (
        <div className="p-4 border-t border-gray-200 space-y-3">
          <div className="flex items-center gap-3">
            <MapPin className="h-4 w-4 text-gray-400" />
            <div>
              <p className="text-sm font-medium text-gray-900">
                {location.address || 'Current Location'}
              </p>
              <p className="text-xs text-gray-500">
                {location.latitude?.toFixed(6)}, {location.longitude?.toFixed(6)}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Clock className="h-4 w-4 text-gray-400" />
            <div>
              <p className="text-sm text-gray-600">
                Last update: {getTimeSinceLastUpdate()}
              </p>
              <p className="text-xs text-gray-500">
                {formatDate(location.lastUpdate)} at {formatTime(location.lastUpdate)}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Current Assignment */}
      {employee.currentAssignment && (
        <div className="p-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Current Assignment</h4>
          <div className="bg-gray-50 rounded-md p-3">
            <p className="text-sm font-medium text-gray-900">
              {employee.currentAssignment.locationName}
            </p>
            <p className="text-xs text-gray-600">
              {formatTime(employee.currentAssignment.startTime)} - {formatTime(employee.currentAssignment.endTime)}
            </p>
            {employee.currentAssignment.notes && (
              <p className="text-xs text-gray-600 mt-1">
                {employee.currentAssignment.notes}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex gap-2">
          <button className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
            View Details
          </button>
          <button className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
            Send Message
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmployeeStatusCard; 