# Geofencing System Setup Guide

This guide covers the setup and configuration of the comprehensive geofencing system for the OnTheMove admin dashboard.

## Overview

The geofencing system provides:
- **Safety Zone Management**: Define circular geofences around locations
- **Real-time Monitoring**: Track employee movements in and out of zones
- **Alert System**: Instant notifications for unauthorized access
- **Access Control**: Allow/restrict specific employees from zones
- **Schedule Management**: Time-based geofence activation
- **Violation Tracking**: Comprehensive audit trail of all events
- **Map Visualization**: Visual representation of geofences and violations

## Features

### 1. Geofence Management

#### Geofence Types
- **Safety Zones**: Restricted areas requiring authorization
- **Work Zones**: Designated work areas for specific employees
- **Exclusion Zones**: Areas employees should avoid
- **Temporary Zones**: Time-limited geofences for special events

#### Geofence Properties
- **Name & Description**: Human-readable identification
- **Location**: Center point (latitude/longitude)
- **Radius**: Circular boundary in meters (10m - 10km)
- **Status**: Active/Inactive toggle
- **Alert Rules**: Entry/exit notification settings
- **Access Control**: Employee allow/restrict lists
- **Schedule**: Time-based activation rules

### 2. Alert System

#### Alert Types
- **Entry Alerts**: Employee enters geofence
- **Exit Alerts**: Employee leaves geofence
- **Unauthorized Entry**: Restricted employee enters zone
- **Unauthorized Exit**: Authorized employee leaves unexpectedly
- **Dwell Time Alerts**: Employee stays too long in zone

#### Alert Severity Levels
- **Low**: Informational alerts
- **Medium**: Standard violations
- **High**: Security concerns
- **Critical**: Emergency situations

### 3. Access Control

#### Employee Management
- **Allowed Employees**: Explicitly permitted access
- **Restricted Employees**: Explicitly denied access
- **Department-based**: Bulk employee management
- **Role-based**: Access based on employee roles

#### Schedule Management
- **Time Windows**: Specific hours of operation
- **Days of Week**: Monday-Sunday selection
- **Holiday Exceptions**: Special date handling
- **Temporary Overrides**: Emergency access grants

### 4. Real-time Monitoring

#### Location Tracking
- **GPS Coordinates**: Precise location data
- **Accuracy Metrics**: Location precision indicators
- **Update Frequency**: Configurable tracking intervals
- **Battery Optimization**: Power-efficient tracking

#### Violation Detection
- **Instant Detection**: Real-time boundary checking
- **Multi-zone Support**: Simultaneous zone monitoring
- **Conflict Resolution**: Priority-based rule handling
- **False Positive Filtering**: Accuracy improvements

## API Endpoints

### Base URL Structure
```
/tenants/{tenantId}/geofences
```

### Geofence Management
```javascript
// Get all geofences
GET /tenants/{tenantId}/geofences
Query Parameters:
- status: string (active, inactive, all)
- location: string (location ID)
- employee: string (employee ID)

Response:
{
  geofences: [
    {
      id: string,
      name: string,
      description: string,
      locationId: string,
      locationName: string,
      center: { lat: number, lng: number },
      radius: number,
      status: 'active' | 'inactive',
      alertRules: {
        entryAlerts: boolean,
        exitAlerts: boolean,
        unauthorizedEntry: boolean,
        unauthorizedExit: boolean,
        dwellTimeAlerts: boolean,
        dwellTimeMinutes: number
      },
      notificationSettings: {
        email: boolean,
        sms: boolean,
        inApp: boolean,
        webhook: boolean,
        webhookUrl: string
      },
      allowedEmployees: string[],
      restrictedEmployees: string[],
      schedule: {
        enabled: boolean,
        startTime: string,
        endTime: string,
        daysOfWeek: number[]
      },
      activeAlerts: number,
      createdAt: string,
      updatedAt: string
    }
  ]
}

// Create geofence
POST /tenants/{tenantId}/geofences
Body:
{
  name: string,
  description: string,
  locationId: string,
  center: { lat: number, lng: number },
  radius: number,
  status: string,
  alertRules: object,
  notificationSettings: object,
  allowedEmployees: string[],
  restrictedEmployees: string[],
  schedule: object
}

// Update geofence
PUT /tenants/{tenantId}/geofences/{geofenceId}
Body: (same as create)

// Delete geofence
DELETE /tenants/{tenantId}/geofences/{geofenceId}
```

### Geofence Alerts
```javascript
// Get geofence alerts
GET /tenants/{tenantId}/geofence-alerts
Query Parameters:
- status: string (acknowledged, unacknowledged, all)
- type: string (entry, exit, unauthorized_entry, unauthorized_exit, dwell_time)
- severity: string (low, medium, high, critical)
- startDate: string (ISO date)
- endDate: string (ISO date)
- employee: string (employee ID)
- geofence: string (geofence ID)

Response:
{
  alerts: [
    {
      id: string,
      type: string,
      severity: string,
      employeeId: string,
      employeeName: string,
      geofenceId: string,
      geofenceName: string,
      location: { lat: number, lng: number },
      timestamp: string,
      acknowledged: boolean,
      acknowledgedBy: string,
      acknowledgedAt: string,
      description: string
    }
  ]
}

// Acknowledge alert
PUT /tenants/{tenantId}/geofence-alerts/{alertId}/acknowledge
Body:
{
  acknowledgedBy: string,
  notes: string
}
```

### Violation Checking
```javascript
// Check geofence violation
POST /tenants/{tenantId}/geofences/check-violation
Body:
{
  employeeId: string,
  location: { lat: number, lng: number },
  timestamp: string
}

Response:
{
  violation: {
    employeeId: string,
    geofenceId: string,
    violationType: string,
    severity: string,
    location: object,
    timestamp: string
  },
  alert: {
    // Alert object if violation detected
  }
}
```

## Data Structure

### Geofence Object
```javascript
const geofence = {
  id: 'uuid',
  name: 'Warehouse Safety Zone',
  description: 'Restricted warehouse area',
  locationId: 'location-uuid',
  locationName: 'Main Warehouse',
  center: { lat: 40.7128, lng: -74.0060 },
  radius: 100, // meters
  status: 'active',
  alertRules: {
    entryAlerts: true,
    exitAlerts: true,
    unauthorizedEntry: true,
    unauthorizedExit: true,
    dwellTimeAlerts: false,
    dwellTimeMinutes: 30
  },
  notificationSettings: {
    email: true,
    sms: false,
    inApp: true,
    webhook: false,
    webhookUrl: ''
  },
  allowedEmployees: ['emp-1', 'emp-2'],
  restrictedEmployees: ['emp-3'],
  schedule: {
    enabled: true,
    startTime: '08:00',
    endTime: '18:00',
    daysOfWeek: [1, 2, 3, 4, 5] // Monday to Friday
  },
  activeAlerts: 2,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
};
```

### Alert Object
```javascript
const alert = {
  id: 'uuid',
  type: 'unauthorized_entry',
  severity: 'critical',
  employeeId: 'emp-uuid',
  employeeName: 'John Doe',
  geofenceId: 'geofence-uuid',
  geofenceName: 'Warehouse Safety Zone',
  location: { lat: 40.7128, lng: -74.0060 },
  timestamp: '2024-01-01T12:00:00Z',
  acknowledged: false,
  acknowledgedBy: null,
  acknowledgedAt: null,
  description: 'Unauthorized entry: John Doe entered restricted area Warehouse Safety Zone'
};
```

## Frontend Components

### 1. GeofencingManagement.js
Main page component with:
- Geofence overview dashboard
- Quick stats and metrics
- View mode toggle (list/map/alerts)
- Filter controls
- Add/edit geofence functionality

### 2. GeofenceForm.js
Comprehensive form for:
- Basic geofence information
- Location and radius selection
- Alert rule configuration
- Notification settings
- Employee access control
- Schedule management

### 3. GeofenceList.js
Table view component with:
- Sortable geofence list
- Status indicators
- Alert counts
- Quick actions (edit/delete)
- Filter functionality

### 4. GeofenceMap.js
Map visualization with:
- Interactive map display
- Geofence boundary circles
- Employee location markers
- Real-time updates
- Violation highlighting

### 5. GeofenceAlerts.js
Alert management with:
- Real-time alert feed
- Severity-based filtering
- Acknowledgment workflow
- Alert details modal
- Historical alert viewing

## Redux Store

### Geofence Slice Structure
```javascript
const geofenceSlice = {
  // Geofence data
  geofences: [],
  
  // Alert data
  alerts: [],
  
  // Loading states
  loading: false,
  creating: false,
  updating: false,
  deleting: false,
  alertsLoading: false,
  
  // Error handling
  error: null,
  
  // Real-time state
  activeViolations: [],
  employeeLocations: {},
  
  // Filters and settings
  filters: {
    status: 'all',
    location: 'all',
    alertType: 'all'
  }
};
```

### Async Thunks
- `fetchGeofences`: Load all geofences for tenant
- `createGeofence`: Create new geofence
- `updateGeofence`: Update existing geofence
- `deleteGeofence`: Remove geofence
- `fetchGeofenceAlerts`: Load geofence alerts
- `acknowledgeAlert`: Mark alert as acknowledged
- `checkGeofenceViolation`: Check for violations

### Real-time Actions
- `handleGeofenceEntry`: Employee enters geofence
- `handleGeofenceExit`: Employee exits geofence
- `handleUnauthorizedEntry`: Unauthorized access
- `handleUnauthorizedExit`: Unauthorized departure
- `handleDwellTimeAlert`: Extended stay alert

## WebSocket Integration

### Event Types
```javascript
// Geofence events
socket.on('geofence_entry', (data) => {
  // Employee enters geofence
});

socket.on('geofence_exit', (data) => {
  // Employee exits geofence
});

socket.on('unauthorized_entry', (data) => {
  // Unauthorized access detected
});

socket.on('unauthorized_exit', (data) => {
  // Unauthorized departure detected
});

socket.on('dwell_time_alert', (data) => {
  // Employee staying too long
});

socket.on('geofence_violation', (data) => {
  // General violation event
});
```

### Event Data Structure
```javascript
const geofenceEvent = {
  employeeId: 'emp-uuid',
  employeeName: 'John Doe',
  geofenceId: 'geofence-uuid',
  geofenceName: 'Warehouse Safety Zone',
  location: { lat: 40.7128, lng: -74.0060 },
  timestamp: '2024-01-01T12:00:00Z',
  violationType: 'unauthorized_entry',
  severity: 'critical'
};
```

## Configuration

### Environment Variables
```bash
# Geofencing settings
GEOFENCING_ENABLED=true
GEOFENCE_MAX_RADIUS=10000
GEOFENCE_MIN_RADIUS=10
GEOFENCE_UPDATE_INTERVAL=30
GEOFENCE_ALERT_RETENTION_DAYS=90

# Location tracking
LOCATION_ACCURACY_THRESHOLD=10
LOCATION_UPDATE_FREQUENCY=30
LOCATION_HISTORY_RETENTION_DAYS=30

# Alert settings
ALERT_EMAIL_ENABLED=true
ALERT_SMS_ENABLED=false
ALERT_WEBHOOK_ENABLED=true
ALERT_BATCH_SIZE=100
```

### Tenant Configuration
```javascript
const tenantConfig = {
  geofencing: {
    enabled: true,
    maxGeofences: 100,
    maxRadius: 10000,
    minRadius: 10,
    alertRetentionDays: 90,
    locationHistoryDays: 30,
    updateInterval: 30
  }
};
```

## Role-Based Access

### Geofence Access by Role
- **Super Admin**: Full access to all geofences
- **Client Admin**: Full access to tenant geofences
- **Dispatcher**: View and manage geofences
- **Supervisor**: View-only access to assigned geofences

### Alert Access by Role
- **Super Admin**: All alerts across all tenants
- **Client Admin**: All alerts for their tenant
- **Dispatcher**: Alerts for managed geofences
- **Supervisor**: Alerts for assigned employees

## Integration Points

### 1. Location Management
- Geofences are tied to specific locations
- Location coordinates used as geofence centers
- Location updates trigger geofence re-evaluation

### 2. Employee Management
- Employee profiles linked to geofence access
- Department-based bulk access management
- Employee status affects geofence permissions

### 3. Live Map Dashboard
- Geofences displayed on live map
- Real-time employee locations
- Violation indicators and alerts

### 4. Incident Management
- Geofence violations create incidents
- Automatic incident categorization
- Escalation workflows

### 5. Messaging System
- Geofence alerts sent via messaging
- Real-time notification delivery
- Alert acknowledgment tracking

## Performance Considerations

### 1. Location Processing
- Efficient geofence boundary calculations
- Spatial indexing for large datasets
- Caching of frequently accessed data

### 2. Alert Management
- Batch processing for high-volume alerts
- Alert deduplication and filtering
- Priority-based alert handling

### 3. Map Rendering
- Optimized geofence circle rendering
- Clustering for dense geofence areas
- Lazy loading for large datasets

### 4. Real-time Updates
- WebSocket connection management
- Efficient event broadcasting
- Connection fallback mechanisms

## Security

### 1. Data Access
- Tenant isolation for all geofence data
- Role-based access control
- Audit logging for all operations

### 2. Location Privacy
- Encrypted location data transmission
- Privacy-compliant data retention
- User consent management

### 3. API Security
- Rate limiting for violation checks
- Input validation and sanitization
- SQL injection prevention

## Troubleshooting

### Common Issues

1. **Geofences Not Detecting Violations**
   - Check location accuracy settings
   - Verify geofence radius configuration
   - Test with known coordinates

2. **Alerts Not Triggering**
   - Verify alert rules configuration
   - Check notification settings
   - Test WebSocket connectivity

3. **Map Not Displaying Geofences**
   - Check Mapbox API key
   - Verify geofence coordinate format
   - Test map container rendering

4. **Performance Issues**
   - Reduce geofence update frequency
   - Implement spatial indexing
   - Optimize database queries

### Debug Mode
```javascript
// Enable debug logging
const DEBUG_MODE = process.env.NODE_ENV === 'development';

if (DEBUG_MODE) {
  console.log('Geofence data:', geofences);
  console.log('Employee location:', location);
  console.log('Violation check:', violation);
}
```

## Future Enhancements

### Planned Features
1. **Polygon Geofences**: Complex boundary shapes
2. **Dynamic Geofences**: Time-based boundary changes
3. **Predictive Alerts**: AI-powered violation prediction
4. **Mobile Integration**: Native mobile app support
5. **Advanced Analytics**: Geofence usage insights
6. **Integration APIs**: Third-party system connections
7. **Custom Rules**: User-defined violation logic
8. **Geofence Templates**: Pre-built geofence configurations

### Analytics Features
1. **Violation Patterns**: Trend analysis
2. **Employee Behavior**: Movement pattern insights
3. **Zone Effectiveness**: Geofence performance metrics
4. **Compliance Reporting**: Regulatory compliance tracking
5. **Cost Analysis**: Geofence ROI calculations

This comprehensive geofencing system provides powerful safety and security monitoring for OnTheMove operations with multi-tenant support, role-based access, and extensive real-time capabilities. 