-- Organizations Table Schema for Multi-Tenant Admin System

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Organizations table (enhanced version of tenants)
CREATE TABLE IF NOT EXISTS organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Basic Organization Information
    name VARCHAR(255) NOT NULL,
    legal_name VARCHAR(255),
    business_type VARCHAR(100), -- 'corporation', 'llc', 'partnership', 'sole_proprietorship', 'non_profit'
    industry VARCHAR(100),
    description TEXT,
    website VARCHAR(255),
    
    -- Contact Information
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    fax VARCHAR(50),
    
    -- Address Information
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',
    
    -- Primary Contact Person
    contact_person VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    contact_title VARCHAR(100),
    
    -- Business Registration Details
    tax_id VARCHAR(50), -- Tax ID or EIN
    registration_number VARCHAR(100),
    registration_date DATE,
    
    -- Billing Information
    billing_address_line1 VARCHAR(255),
    billing_address_line2 VARCHAR(255),
    billing_city VARCHAR(100),
    billing_state VARCHAR(100),
    billing_postal_code VARCHAR(20),
    billing_country VARCHAR(100),
    billing_contact_name VARCHAR(255),
    billing_contact_email VARCHAR(255),
    billing_contact_phone VARCHAR(50),
    
    -- Subscription & Plan Details
    subscription_plan VARCHAR(50) DEFAULT 'basic', -- 'basic', 'standard', 'premium', 'enterprise'
    subscription_status VARCHAR(20) DEFAULT 'active', -- 'active', 'suspended', 'cancelled', 'trial'
    subscription_start_date DATE,
    subscription_end_date DATE,
    billing_cycle VARCHAR(20) DEFAULT 'monthly', -- 'monthly', 'quarterly', 'annual'
    payment_method VARCHAR(50) DEFAULT 'invoice', -- 'credit_card', 'bank_transfer', 'invoice', 'other'
    
    -- Service Limits
    max_users INTEGER DEFAULT 100,
    max_locations INTEGER DEFAULT 10,
    max_monthly_tracked_hours INTEGER DEFAULT 5000,
    max_storage_gb INTEGER DEFAULT 10,
    max_api_calls_per_hour INTEGER DEFAULT 1000,
    
    -- Features & Permissions
    features JSONB DEFAULT '{}', -- JSON object storing enabled features
    custom_permissions JSONB DEFAULT '{}', -- JSON object storing custom permissions
    
    -- Organization Settings
    timezone VARCHAR(50) DEFAULT 'America/New_York',
    date_format VARCHAR(20) DEFAULT 'MM/DD/YYYY',
    time_format VARCHAR(10) DEFAULT '12h', -- '12h' or '24h'
    currency VARCHAR(10) DEFAULT 'USD',
    locale VARCHAR(10) DEFAULT 'en_US',
    
    -- Compliance & Security
    compliance_requirements TEXT[], -- Array of compliance requirements
    security_level VARCHAR(20) DEFAULT 'standard', -- 'basic', 'standard', 'high', 'enterprise'
    two_factor_required BOOLEAN DEFAULT FALSE,
    password_policy JSONB DEFAULT '{}',
    
    -- Organization Status
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'inactive', 'suspended', 'pending_approval'
    approval_status VARCHAR(20) DEFAULT 'approved', -- 'pending', 'approved', 'rejected'
    approved_by UUID, -- Reference to super admin user
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    logo_url VARCHAR(500),
    notes TEXT,
    internal_notes TEXT, -- For admin use only
    tags VARCHAR(255)[], -- Array of tags for categorization
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID, -- Reference to user who created the organization
    updated_by UUID  -- Reference to user who last updated the organization
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_organizations_name ON organizations(name);
CREATE INDEX IF NOT EXISTS idx_organizations_email ON organizations(email);
CREATE INDEX IF NOT EXISTS idx_organizations_status ON organizations(status);
CREATE INDEX IF NOT EXISTS idx_organizations_subscription_plan ON organizations(subscription_plan);
CREATE INDEX IF NOT EXISTS idx_organizations_created_at ON organizations(created_at);
CREATE INDEX IF NOT EXISTS idx_organizations_industry ON organizations(industry);
CREATE INDEX IF NOT EXISTS idx_organizations_country ON organizations(country);
CREATE INDEX IF NOT EXISTS idx_organizations_subscription_status ON organizations(subscription_status);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to organizations table
CREATE TRIGGER update_organizations_updated_at 
    BEFORE UPDATE ON organizations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to validate organization email uniqueness
CREATE OR REPLACE FUNCTION validate_organization_email()
RETURNS TRIGGER AS $$
BEGIN
    IF EXISTS (SELECT 1 FROM organizations WHERE email = NEW.email AND id != NEW.id) THEN
        RAISE EXCEPTION 'Organization email already exists';
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply email validation trigger
CREATE TRIGGER validate_organization_email_trigger
    BEFORE INSERT OR UPDATE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION validate_organization_email();

-- Create function to check organization limits
CREATE OR REPLACE FUNCTION check_organization_limits()
RETURNS TRIGGER AS $$
BEGIN
    -- Add custom validation logic here
    -- For example, checking if max_users is within acceptable range
    IF NEW.max_users < 1 OR NEW.max_users > 10000 THEN
        RAISE EXCEPTION 'max_users must be between 1 and 10000';
    END IF;
    
    IF NEW.max_locations < 1 OR NEW.max_locations > 1000 THEN
        RAISE EXCEPTION 'max_locations must be between 1 and 1000';
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply limits validation trigger
CREATE TRIGGER check_organization_limits_trigger
    BEFORE INSERT OR UPDATE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION check_organization_limits();

-- Create view for active organizations
CREATE OR REPLACE VIEW active_organizations AS
SELECT 
    id,
    name,
    legal_name,
    email,
    phone,
    contact_person,
    contact_email,
    subscription_plan,
    subscription_status,
    max_users,
    max_locations,
    created_at,
    updated_at
FROM organizations 
WHERE status = 'active';

-- Create view for organization summary
CREATE OR REPLACE VIEW organization_summary AS
SELECT 
    o.id,
    o.name,
    o.email,
    o.subscription_plan,
    o.status,
    o.created_at,
    COUNT(DISTINCT u.id) as user_count,
    COUNT(DISTINCT l.id) as location_count
FROM organizations o
LEFT JOIN users u ON u.tenant_id = o.id
LEFT JOIN locations l ON l.tenant_id = o.id
GROUP BY o.id, o.name, o.email, o.subscription_plan, o.status, o.created_at;

-- Insert some sample data (optional - remove in production)
-- INSERT INTO organizations (name, email, contact_person, subscription_plan) VALUES
-- ('Sample Security Corp', '<EMAIL>', 'John Smith', 'premium'),
-- ('City Guard Services', '<EMAIL>', 'Jane Doe', 'standard');

-- Comments explaining the schema
COMMENT ON TABLE organizations IS 'Master table for all client organizations in the multi-tenant system';
COMMENT ON COLUMN organizations.features IS 'JSON object storing enabled features like {"geofencing": true, "reporting": true}';
COMMENT ON COLUMN organizations.custom_permissions IS 'JSON object storing custom permissions and access levels';
COMMENT ON COLUMN organizations.compliance_requirements IS 'Array of compliance standards like {"SOC2", "HIPAA", "GDPR"}';
COMMENT ON COLUMN organizations.password_policy IS 'JSON object storing password requirements like {"min_length": 8, "require_special": true}'; 