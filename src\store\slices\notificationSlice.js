import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  notifications: [],
  unreadCount: 0,
  settings: {
    enableSound: true,
    enableDesktopNotifications: true,
    showEmergencyAlerts: true,
    showZoneViolations: true,
    showStatusChanges: false,
    showIncidents: true,
    showPanicAlerts: true,
    showGeofenceBreaches: true,
    showMessages: true
  }
};

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    addNotification: (state, action) => {
      const notification = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        read: false,
        ...action.payload
      };
      
      state.notifications.unshift(notification);
      state.unreadCount += 1;
      
      // Keep only last 100 notifications
      if (state.notifications.length > 100) {
        state.notifications = state.notifications.slice(0, 100);
      }
    },
    
    markAsRead: (state, action) => {
      const notificationId = action.payload;
      const notification = state.notifications.find(n => n.id === notificationId);
      
      if (notification && !notification.read) {
        notification.read = true;
        state.unreadCount = Math.max(0, state.unreadCount - 1);
      }
    },
    
    markAllAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
      state.unreadCount = 0;
    },
    
    clearNotification: (state, action) => {
      const notificationId = action.payload;
      const notification = state.notifications.find(n => n.id === notificationId);
      
      if (notification && !notification.read) {
        state.unreadCount = Math.max(0, state.unreadCount - 1);
      }
      
      state.notifications = state.notifications.filter(n => n.id !== notificationId);
    },

    deleteNotification: (state, action) => {
      const notificationId = action.payload;
      const notification = state.notifications.find(n => n.id === notificationId);
      
      if (notification && !notification.read) {
        state.unreadCount = Math.max(0, state.unreadCount - 1);
      }
      
      state.notifications = state.notifications.filter(n => n.id !== notificationId);
    },
    
    clearAllNotifications: (state) => {
      state.notifications = [];
      state.unreadCount = 0;
    },
    
    updateNotificationSettings: (state, action) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    
    // Specialized notification actions
    addEmergencyAlert: (state, action) => {
      if (state.settings.showEmergencyAlerts) {
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'emergency',
          title: 'Emergency Alert',
          message: action.payload.message,
          employeeId: action.payload.employeeId,
          employeeName: action.payload.employeeName,
          timestamp: new Date().toISOString(),
          read: false,
          priority: 'high'
        });
        state.unreadCount += 1;
      }
    },
    
    addZoneViolation: (state, action) => {
      if (state.settings.showZoneViolations) {
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'zone_violation',
          title: 'Zone Violation',
          message: `${action.payload.employeeName} is out of assigned zone`,
          employeeId: action.payload.employeeId,
          employeeName: action.payload.employeeName,
          timestamp: new Date().toISOString(),
          read: false,
          priority: 'medium'
        });
        state.unreadCount += 1;
      }
    },
    
    addStatusChange: (state, action) => {
      if (state.settings.showStatusChanges) {
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'status_change',
          title: 'Status Change',
          message: `${action.payload.employeeName} is now ${action.payload.newStatus}`,
          employeeId: action.payload.employeeId,
          employeeName: action.payload.employeeName,
          timestamp: new Date().toISOString(),
          read: false,
          priority: 'low'
        });
        state.unreadCount += 1;
      }
    },
    
    addLateAlert: (state, action) => {
      state.notifications.unshift({
        id: Date.now().toString(),
        type: 'late_alert',
        title: 'Employee Late',
        message: `${action.payload.employeeName} is late for their shift`,
        employeeId: action.payload.employeeId,
        employeeName: action.payload.employeeName,
        timestamp: new Date().toISOString(),
        read: false,
        priority: 'medium'
      });
      state.unreadCount += 1;
    },

    // New notification types
    addIncidentAlert: (state, action) => {
      if (state.settings.showIncidents) {
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'incident',
          title: 'New Incident',
          message: action.payload.message || `New incident reported: ${action.payload.title}`,
          incidentId: action.payload.incidentId,
          incidentTitle: action.payload.title,
          severity: action.payload.severity,
          employeeId: action.payload.employeeId,
          employeeName: action.payload.employeeName,
          locationId: action.payload.locationId,
          locationName: action.payload.locationName,
          tenantId: action.payload.tenantId,
          timestamp: new Date().toISOString(),
          read: false,
          priority: action.payload.severity === 'critical' ? 'high' : 'medium'
        });
        state.unreadCount += 1;
      }
    },

    addPanicAlert: (state, action) => {
      if (state.settings.showPanicAlerts) {
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'panic_button',
          title: 'Panic Button Activated',
          message: `${action.payload.employeeName} has activated their panic button`,
          employeeId: action.payload.employeeId,
          employeeName: action.payload.employeeName,
          location: action.payload.location,
          coordinates: action.payload.coordinates,
          tenantId: action.payload.tenantId,
          timestamp: new Date().toISOString(),
          read: false,
          priority: 'high'
        });
        state.unreadCount += 1;
      }
    },

    addGeofenceBreach: (state, action) => {
      if (state.settings.showGeofenceBreaches) {
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'geofence_breach',
          title: 'Geofence Breach',
          message: `${action.payload.employeeName} has left their assigned geofence`,
          employeeId: action.payload.employeeId,
          employeeName: action.payload.employeeName,
          geofenceId: action.payload.geofenceId,
          geofenceName: action.payload.geofenceName,
          location: action.payload.location,
          coordinates: action.payload.coordinates,
          tenantId: action.payload.tenantId,
          timestamp: new Date().toISOString(),
          read: false,
          priority: 'medium'
        });
        state.unreadCount += 1;
      }
    },

    addMessageNotification: (state, action) => {
      if (state.settings.showMessages) {
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'message',
          title: 'New Message',
          message: action.payload.message || `New message from ${action.payload.senderName}`,
          conversationId: action.payload.conversationId,
          senderId: action.payload.senderId,
          senderName: action.payload.senderName,
          tenantId: action.payload.tenantId,
          timestamp: new Date().toISOString(),
          read: false,
          priority: 'low'
        });
        state.unreadCount += 1;
      }
    },

    addIncidentUpdate: (state, action) => {
      state.notifications.unshift({
        id: Date.now().toString(),
        type: 'incident_update',
        title: 'Incident Updated',
        message: `Incident "${action.payload.title}" has been updated`,
        incidentId: action.payload.incidentId,
        incidentTitle: action.payload.title,
        status: action.payload.status,
        assignedEmployeeId: action.payload.assignedEmployeeId,
        assignedEmployeeName: action.payload.assignedEmployeeName,
        tenantId: action.payload.tenantId,
        timestamp: new Date().toISOString(),
        read: false,
        priority: 'medium'
      });
      state.unreadCount += 1;
    },

    addIncidentAssignment: (state, action) => {
      state.notifications.unshift({
        id: Date.now().toString(),
        type: 'incident_assignment',
        title: 'Incident Assigned',
        message: `You have been assigned to incident: ${action.payload.title}`,
        incidentId: action.payload.incidentId,
        incidentTitle: action.payload.title,
        assignedEmployeeId: action.payload.assignedEmployeeId,
        assignedEmployeeName: action.payload.assignedEmployeeName,
        tenantId: action.payload.tenantId,
        timestamp: new Date().toISOString(),
        read: false,
        priority: 'medium'
      });
      state.unreadCount += 1;
    },

    addIncidentEscalation: (state, action) => {
      state.notifications.unshift({
        id: Date.now().toString(),
        type: 'incident_escalation',
        title: 'Incident Escalated',
        message: `Incident "${action.payload.title}" has been escalated to ${action.payload.severity} severity`,
        incidentId: action.payload.incidentId,
        incidentTitle: action.payload.title,
        severity: action.payload.severity,
        tenantId: action.payload.tenantId,
        timestamp: new Date().toISOString(),
        read: false,
        priority: 'high'
      });
      state.unreadCount += 1;
    }
  }
});

export const {
  addNotification,
  markAsRead,
  markAllAsRead,
  clearNotification,
  deleteNotification,
  clearAllNotifications,
  updateNotificationSettings,
  addEmergencyAlert,
  addZoneViolation,
  addStatusChange,
  addLateAlert,
  addIncidentAlert,
  addPanicAlert,
  addGeofenceBreach,
  addMessageNotification,
  addIncidentUpdate,
  addIncidentAssignment,
  addIncidentEscalation
} = notificationSlice.actions;

// Selectors
export const selectNotifications = (state) => state.notifications.notifications;
export const selectUnreadCount = (state) => state.notifications.unreadCount;
export const selectNotificationSettings = (state) => state.notifications.settings;
export const selectUnreadNotifications = (state) => 
  state.notifications.notifications.filter(n => !n.read);
export const selectHighPriorityNotifications = (state) => 
  state.notifications.notifications.filter(n => n.priority === 'high' && !n.read);
export const selectNotificationsByType = (type) => (state) =>
  state.notifications.notifications.filter(n => n.type === type);
export const selectRecentNotifications = (limit = 10) => (state) =>
  state.notifications.notifications.slice(0, limit);

// Alias for backward compatibility
export const markNotificationAsRead = markAsRead;

export default notificationSlice.reducer; 