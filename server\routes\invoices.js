const express = require('express');
const router = express.Router({ mergeParams: true });
const { pool } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');

// Get all invoices for an organization
router.get('/', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      status = 'all',
      page = 1, 
      limit = 20 
    } = req.query;

    let query = `
      SELECT 
        i.*,
        o.name as organization_name
      FROM invoices i
      JOIN organizations o ON i.organization_id = o.id
      WHERE i.organization_id = $1
    `;
    
    const params = [organizationId];
    let paramIndex = 2;

    if (status !== 'all') {
      query += ` AND i.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    query += ` ORDER BY i.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, (page - 1) * limit);

    const result = await pool.query(query, params);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching invoices:', error);
    res.status(500).json({ error: 'Failed to fetch invoices' });
  }
});

// Get invoice by ID with line items
router.get('/:invoiceId', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { organizationId, invoiceId } = req.params;

    // Get invoice
    const invoiceResult = await pool.query(`
      SELECT 
        i.*,
        o.name as organization_name,
        o.email as organization_email,
        o.address_line1,
        o.city,
        o.state,
        o.postal_code,
        o.country
      FROM invoices i
      JOIN organizations o ON i.organization_id = o.id
      WHERE i.id = $1 AND i.organization_id = $2
    `, [invoiceId, organizationId]);

    if (invoiceResult.rows.length === 0) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Get line items
    const lineItemsResult = await pool.query(`
      SELECT * FROM invoice_line_items
      WHERE invoice_id = $1
      ORDER BY location_name
    `, [invoiceId]);

    const invoice = invoiceResult.rows[0];
    invoice.line_items = lineItemsResult.rows;

    res.json(invoice);
  } catch (error) {
    console.error('Error fetching invoice:', error);
    res.status(500).json({ error: 'Failed to fetch invoice' });
  }
});

// Create new invoice
router.post('/', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      billing_period_start, 
      billing_period_end,
      due_date,
      tax_rate = 0,
      notes = ''
    } = req.body;

    // Get organization details
    const orgResult = await pool.query(`
      SELECT name, per_user_fee FROM organizations WHERE id = $1
    `, [organizationId]);

    if (orgResult.rows.length === 0) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organization = orgResult.rows[0];
    const perUserFee = organization.per_user_fee || 5.00;

    // Get active users by location for the billing period
    const locationsResult = await pool.query(`
      SELECT 
        l.id,
        l.name,
        COUNT(DISTINCT u.id) as user_count
      FROM locations l
      LEFT JOIN assignments a ON l.id = a.location_id
      LEFT JOIN users u ON a.user_id = u.id
      WHERE l.organization_id = $1 
        AND u.status = 'active'
        AND a.status = 'active'
        AND a.created_at <= $3
      GROUP BY l.id, l.name
      HAVING COUNT(DISTINCT u.id) > 0
      ORDER BY l.name
    `, [organizationId, billing_period_start, billing_period_end]);

    if (locationsResult.rows.length === 0) {
      return res.status(400).json({ error: 'No active users found for billing period' });
    }

    // Generate invoice number
    const invoiceNumber = `INV-${Date.now()}`;

    // Calculate totals
    let subtotal = 0;
    const lineItems = locationsResult.rows.map(location => {
      const lineTotal = location.user_count * perUserFee;
      subtotal += lineTotal;
      return {
        location_id: location.id,
        location_name: location.name,
        user_count: parseInt(location.user_count),
        unit_price: perUserFee,
        line_total: lineTotal
      };
    });

    const taxAmount = subtotal * (tax_rate / 100);
    const totalAmount = subtotal + taxAmount;

    // Create invoice in transaction
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Insert invoice
      const invoiceResult = await client.query(`
        INSERT INTO invoices (
          organization_id, invoice_number, due_date,
          billing_period_start, billing_period_end,
          subtotal, tax_rate, tax_amount, total_amount, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `, [
        organizationId, invoiceNumber, due_date,
        billing_period_start, billing_period_end,
        subtotal, tax_rate, taxAmount, totalAmount, notes
      ]);

      const invoice = invoiceResult.rows[0];

      // Insert line items
      for (const item of lineItems) {
        await client.query(`
          INSERT INTO invoice_line_items (
            invoice_id, location_id, location_name, user_count, unit_price, line_total
          ) VALUES ($1, $2, $3, $4, $5, $6)
        `, [
          invoice.id, item.location_id, item.location_name,
          item.user_count, item.unit_price, item.line_total
        ]);
      }

      await client.query('COMMIT');

      // Return invoice with line items
      invoice.line_items = lineItems;
      res.status(201).json(invoice);

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error creating invoice:', error);
    res.status(500).json({ error: 'Failed to create invoice' });
  }
});

// Update invoice status
router.patch('/:invoiceId/status', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { organizationId, invoiceId } = req.params;
    const { status, payment_date, payment_method } = req.body;

    const updateFields = ['status = $3', 'updated_at = CURRENT_TIMESTAMP'];
    const params = [invoiceId, organizationId, status];
    let paramIndex = 4;

    if (status === 'paid' && payment_date) {
      updateFields.push(`payment_date = $${paramIndex}`);
      params.push(payment_date);
      paramIndex++;
    }

    if (status === 'paid' && payment_method) {
      updateFields.push(`payment_method = $${paramIndex}`);
      params.push(payment_method);
    }

    const result = await pool.query(`
      UPDATE invoices 
      SET ${updateFields.join(', ')}
      WHERE id = $1 AND organization_id = $2
      RETURNING *
    `, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating invoice status:', error);
    res.status(500).json({ error: 'Failed to update invoice status' });
  }
});

// Send invoice reminder
router.post('/:invoiceId/reminder', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { organizationId, invoiceId } = req.params;

    // Get invoice and organization details
    const result = await pool.query(`
      SELECT 
        i.*,
        o.name as organization_name,
        o.contact_email
      FROM invoices i
      JOIN organizations o ON i.organization_id = o.id
      WHERE i.id = $1 AND i.organization_id = $2
    `, [invoiceId, organizationId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    const invoice = result.rows[0];

    // Email reminder logic
    const reminderData = {
      to: invoice.contact_email,
      subject: `Payment Reminder - Invoice ${invoice.invoice_number}`,
      template: 'invoice_reminder',
      data: {
        organizationName: invoice.organization_name,
        invoiceNumber: invoice.invoice_number,
        totalAmount: invoice.total_amount,
        dueDate: invoice.due_date,
        invoiceDate: invoice.invoice_date
      }
    };

    // Log the reminder (in production, this would send an actual email)
    console.log(`Sending reminder for invoice ${invoice.invoice_number} to ${invoice.contact_email}`, reminderData);

    // Update invoice with reminder sent timestamp
    await pool.query(`
      UPDATE invoices
      SET updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [invoiceId]);

    res.json({
      message: 'Reminder sent successfully',
      sentTo: invoice.contact_email,
      invoiceNumber: invoice.invoice_number
    });
  } catch (error) {
    console.error('Error sending invoice reminder:', error);
    res.status(500).json({ error: 'Failed to send reminder' });
  }
});

module.exports = router;
