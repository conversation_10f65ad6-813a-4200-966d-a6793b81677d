import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTenant } from '../../tenant/TenantProvider';
import { useBranding } from '../../tenant/BrandingProvider';
import BrandedButton from '../../components/BrandedButton';
import IncidentForm from '../../components/incident/IncidentForm';
import IncidentList from '../../components/incident/IncidentList';
import IncidentFilters from '../../components/incident/IncidentFilters';
import IncidentStats from '../../components/incident/IncidentStats';
import { 
  AlertTriangle, 
  Plus, 
  Search, 
  Filter, 
  Clock, 
  MapPin, 
  User, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  TrendingUp
} from 'lucide-react';
import {
  fetchIncidents,
  fetchEmployees,
  fetchLocations,
  createIncident,
  updateIncident,
  deleteIncident,
  selectIncidents,
  selectEmployees,
  selectLocations,
  selectIncidentLoading,
  selectIncidentError
} from '../../store/slices/incidentSlice';

const IncidentManagement = () => {
  const dispatch = useDispatch();
  const { tenant } = useTenant();
  const { branding } = useBranding();
  const { user } = useSelector((state) => state.auth);

  // Redux state
  const incidents = useSelector(selectIncidents);
  const employees = useSelector(selectEmployees);
  const locations = useSelector(selectLocations);
  const loading = useSelector(selectIncidentLoading);
  const error = useSelector(selectIncidentError);

  // Local state
  const [showForm, setShowForm] = useState(false);
  const [editingIncident, setEditingIncident] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    severity: 'all',
    location: 'all',
    employee: 'all',
    dateRange: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'stats'

  // Load data on component mount
  useEffect(() => {
    if (tenant?.id) {
      dispatch(fetchIncidents({ organizationId: tenant.id, filters }));
      dispatch(fetchEmployees({ organizationId: tenant.id }));
      dispatch(fetchLocations({ organizationId: tenant.id }));
    }
  }, [dispatch, tenant?.id]);

  // Reload incidents when filters change
  useEffect(() => {
    if (tenant?.id) {
      dispatch(fetchIncidents({ organizationId: tenant.id, filters }));
    }
  }, [dispatch, tenant?.id, filters]);

  const handleAddIncident = () => {
    setEditingIncident(null);
    setShowForm(true);
  };

  const handleEditIncident = (incident) => {
    setEditingIncident(incident);
    setShowForm(true);
  };

  const handleDeleteIncident = async (incidentId) => {
    if (window.confirm('Are you sure you want to delete this incident?')) {
      await dispatch(deleteIncident({ organizationId: tenant.id, incidentId }));
    }
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingIncident(null);
  };

  const handleFormSubmit = async (incidentData) => {
    try {
      if (editingIncident) {
        await dispatch(updateIncident({
          organizationId: tenant.id,
          incidentId: editingIncident.id,
          incidentData
        }));
      } else {
        await dispatch(createIncident({
          organizationId: tenant.id,
          incidentData
        }));
      }
      handleFormClose();
    } catch (error) {
      console.error('Error saving incident:', error);
    }
  };

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
  };

  // Get incident statistics
  const getIncidentStats = () => {
    const stats = {
      total: incidents.length,
      open: incidents.filter(inc => inc.status === 'open').length,
      inProgress: incidents.filter(inc => inc.status === 'in_progress').length,
      resolved: incidents.filter(inc => inc.status === 'resolved').length,
      closed: incidents.filter(inc => inc.status === 'closed').length,
      critical: incidents.filter(inc => inc.severity === 'critical').length,
      high: incidents.filter(inc => inc.severity === 'high').length,
      medium: incidents.filter(inc => inc.severity === 'medium').length,
      low: incidents.filter(inc => inc.severity === 'low').length
    };
    return stats;
  };

  if (!tenant?.id) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading incidents...</p>
      </div>
    );
  }

  const stats = getIncidentStats();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="p-8">
        {/* Enhanced Header */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-8 mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-red-500 to-orange-600 rounded-xl shadow-lg">
              <AlertTriangle className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Incident Management
              </h1>
              <p className="text-gray-600 mt-1">
                Track and manage incidents for {tenant.name}
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Incidents</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.total}</p>
              </div>
              <div className="p-3 bg-gray-100 rounded-xl">
                <AlertTriangle className="h-8 w-8 text-gray-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Open</p>
                <p className="text-3xl font-bold text-red-600 mt-2">{stats.open}</p>
              </div>
              <div className="p-3 bg-red-100 rounded-xl">
                <Clock className="h-8 w-8 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-3xl font-bold text-yellow-600 mt-2">{stats.inProgress}</p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-xl">
                <TrendingUp className="h-8 w-8 text-yellow-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Resolved</p>
                <p className="text-3xl font-bold text-green-600 mt-2">{stats.resolved}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-xl">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Actions Bar */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6 mb-8">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-3 flex-1">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search incidents..."
                  className="pl-12 pr-4 py-3 w-full border border-gray-300 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400"
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                />
              </div>

              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-6 py-3 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 transition-all duration-200 hover:border-gray-400"
              >
                <Filter className="h-5 w-5" />
                Filters
              </button>

              {/* View Mode Toggle */}
              <div className="flex items-center space-x-1 bg-gray-100 p-1 rounded-lg">
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                    viewMode === 'list'
                      ? 'bg-white text-blue-600 shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  List
                </button>
                <button
                  onClick={() => setViewMode('stats')}
                  className={`px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                    viewMode === 'stats'
                      ? 'bg-white text-blue-600 shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Statistics
                </button>
              </div>
            </div>

            {/* Add Incident Button */}
            <BrandedButton
              onClick={handleAddIncident}
              className="flex items-center gap-2 px-6 py-3 shadow-lg"
            >
              <Plus className="h-5 w-5" />
            Add Incident
          </BrandedButton>
        </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <IncidentFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
                employees={employees}
                locations={locations}
              />
            </div>
          )}
        </div>

        {/* Content Area */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200">
          {viewMode === 'stats' ? (
            <IncidentStats incidents={incidents} employees={employees} locations={locations} />
          ) : (
            <IncidentList
              incidents={incidents}
              employees={employees}
              locations={locations}
              onEdit={handleEditIncident}
              onDelete={handleDeleteIncident}
            />
          )}
        </div>

        {/* Incident Form Modal */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <IncidentForm
                incident={editingIncident}
                employees={employees}
                locations={locations}
                organizationId={tenant.id}
                onSubmit={handleFormSubmit}
                onCancel={handleFormClose}
              />
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50">
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4" />
              <span>Error: {error}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default IncidentManagement; 