const express = require('express');
const bcrypt = require('bcryptjs');
const { authenticateToken, requireTenantAccess } = require('../middleware/auth');
const { pool } = require('../config/database');
const router = express.Router({ mergeParams: true });

// Get all users for an organization
router.get('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      search = '', 
      role = 'all', 
      status = 'all',
      page = 1, 
      limit = 50 
    } = req.query;

    let query = `
      SELECT 
        u.id,
        u.email,
        u.first_name,
        u.last_name,
        u.phone,
        u.role,
        u.status,
        u.created_at,
        u.updated_at,
        u.last_login,
        COUNT(a.id) as assignment_count
      FROM users u
      LEFT JOIN assignments a ON u.id = a.user_id AND a.status = 'active'
      WHERE u.organization_id = $1
    `;
    
    const params = [organizationId];
    let paramIndex = 2;

    // Apply filters
    if (search) {
      query += ` AND (u.first_name ILIKE $${paramIndex} OR u.last_name ILIKE $${paramIndex} OR u.email ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (role !== 'all') {
      query += ` AND u.role = $${paramIndex}`;
      params.push(role);
      paramIndex++;
    }

    if (status !== 'all') {
      query += ` AND u.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    query += ` 
      GROUP BY u.id, u.email, u.first_name, u.last_name, u.phone, u.role, u.status, u.created_at, u.updated_at, u.last_login
      ORDER BY u.first_name, u.last_name
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(limit, (page - 1) * limit);

    const result = await pool.query(query, params);
    const users = result.rows.map(user => ({
      id: user.id,
      name: `${user.first_name} ${user.last_name}`,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status,
      assignmentCount: parseInt(user.assignment_count),
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      lastLogin: user.last_login
    }));

    res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get user by ID
router.get('/:userId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, userId } = req.params;

    const result = await pool.query(`
      SELECT 
        u.id,
        u.email,
        u.first_name,
        u.last_name,
        u.phone,
        u.role,
        u.status,
        u.created_at,
        u.updated_at,
        u.last_login
      FROM users u
      WHERE u.organization_id = $1 AND u.id = $2
    `, [organizationId, userId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = result.rows[0];
    
    // Get current assignments
    const assignmentsResult = await pool.query(`
      SELECT 
        a.id,
        a.shift_start,
        a.shift_end,
        a.status,
        a.notes,
        l.name as location_name,
        l.address as location_address
      FROM assignments a
      JOIN locations l ON a.location_id = l.id
      WHERE a.user_id = $1 AND a.status = 'active'
      ORDER BY a.shift_start
    `, [userId]);

    res.json({
      id: user.id,
      name: `${user.first_name} ${user.last_name}`,
      firstName: user.first_name,
      lastName: user.last_name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      lastLogin: user.last_login,
      assignments: assignmentsResult.rows
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Failed to fetch user' });
  }
});

// Create new user
router.post('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      firstName, 
      lastName, 
      email, 
      phone, 
      role = 'employee', 
      password = 'employee123' 
    } = req.body;

    // Validate required fields
    if (!firstName || !lastName || !email) {
      return res.status(400).json({ error: 'First name, last name, and email are required' });
    }

    // Check if email already exists
    const existingUser = await pool.query(`
      SELECT id FROM users WHERE email = $1
    `, [email]);

    if (existingUser.rows.length > 0) {
      return res.status(409).json({ error: 'Email already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const result = await pool.query(`
      INSERT INTO users (
        organization_id, email, password_hash, first_name, last_name, phone, role, status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'active')
      RETURNING id, email, first_name, last_name, phone, role, status, created_at
    `, [organizationId, email, hashedPassword, firstName, lastName, phone, role]);

    const newUser = result.rows[0];

    res.status(201).json({
      id: newUser.id,
      name: `${newUser.first_name} ${newUser.last_name}`,
      firstName: newUser.first_name,
      lastName: newUser.last_name,
      email: newUser.email,
      phone: newUser.phone,
      role: newUser.role,
      status: newUser.status,
      createdAt: newUser.created_at
    });
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

// Update user
router.put('/:userId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, userId } = req.params;
    const { firstName, lastName, email, phone, role, status } = req.body;

    // Check if user exists and belongs to organization
    const existingUser = await pool.query(`
      SELECT id FROM users WHERE id = $1 AND organization_id = $2
    `, [userId, organizationId]);

    if (existingUser.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Build update query dynamically
    const updates = [];
    const params = [];
    let paramIndex = 1;

    if (firstName) {
      updates.push(`first_name = $${paramIndex}`);
      params.push(firstName);
      paramIndex++;
    }
    if (lastName) {
      updates.push(`last_name = $${paramIndex}`);
      params.push(lastName);
      paramIndex++;
    }
    if (email) {
      updates.push(`email = $${paramIndex}`);
      params.push(email);
      paramIndex++;
    }
    if (phone) {
      updates.push(`phone = $${paramIndex}`);
      params.push(phone);
      paramIndex++;
    }
    if (role) {
      updates.push(`role = $${paramIndex}`);
      params.push(role);
      paramIndex++;
    }
    if (status) {
      updates.push(`status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updates.push(`updated_at = NOW()`);
    params.push(userId, organizationId);

    const result = await pool.query(`
      UPDATE users 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex} AND organization_id = $${paramIndex + 1}
      RETURNING id, email, first_name, last_name, phone, role, status, updated_at
    `, params);

    const updatedUser = result.rows[0];

    res.json({
      id: updatedUser.id,
      name: `${updatedUser.first_name} ${updatedUser.last_name}`,
      firstName: updatedUser.first_name,
      lastName: updatedUser.last_name,
      email: updatedUser.email,
      phone: updatedUser.phone,
      role: updatedUser.role,
      status: updatedUser.status,
      updatedAt: updatedUser.updated_at
    });
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Delete user
router.delete('/:userId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, userId } = req.params;

    // Soft delete by setting status to inactive
    await pool.query(`
      UPDATE users 
      SET status = 'inactive', updated_at = NOW()
      WHERE id = $1 AND organization_id = $2
    `, [userId, organizationId]);

    res.json({ message: 'User deactivated successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

// Get users for live tracking
router.get('/live/locations', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;

    const result = await pool.query(`
      SELECT 
        u.id,
        u.first_name,
        u.last_name,
        u.email,
        u.phone,
        u.role,
        u.status,
        ul.latitude,
        ul.longitude,
        ul.accuracy,
        ul.battery_level,
        ul.timestamp,
        l.name as location_name,
        a.shift_start,
        a.shift_end,
        a.status as assignment_status
      FROM users u
      LEFT JOIN user_locations ul ON u.id = ul.user_id
      LEFT JOIN assignments a ON u.id = a.user_id AND a.status = 'active'
      LEFT JOIN locations l ON a.location_id = l.id
      WHERE u.organization_id = $1 AND u.status = 'active'
      ORDER BY u.first_name, u.last_name
    `, [organizationId]);

    const users = result.rows.map(user => ({
      id: user.id,
      name: `${user.first_name} ${user.last_name}`,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status,
      location: user.latitude && user.longitude ? {
        latitude: parseFloat(user.latitude),
        longitude: parseFloat(user.longitude),
        accuracy: user.accuracy,
        batteryLevel: user.battery_level,
        timestamp: user.timestamp
      } : null,
      currentAssignment: user.location_name ? {
        locationName: user.location_name,
        startTime: user.shift_start,
        endTime: user.shift_end,
        status: user.assignment_status
      } : null
    }));

    res.json(users);
  } catch (error) {
    console.error('Error fetching live user locations:', error);
    res.status(500).json({ error: 'Failed to fetch live user locations' });
  }
});

module.exports = router; 