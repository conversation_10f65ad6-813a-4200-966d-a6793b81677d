import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { 
  MapPin, 
  Users, 
  AlertTriangle, 
  Eye, 
  EyeOff,
  ZoomIn,
  ZoomOut,
  Layers,
  Filter,
  X,
  Shield
} from 'lucide-react';
import BrandedButton from '../BrandedButton';

const GeofenceMap = ({ 
  geofences, 
  selectedGeofence, 
  onGeofenceSelect, 
  loading 
}) => {
  const { employees, locations } = useSelector((state) => ({
    employees: state.employees?.employees || [],
    locations: state.locations?.locations || []
  }));

  const mapRef = useRef(null);
  const mapInstance = useRef(null);
  const markersRef = useRef({});
  const circlesRef = useRef({});

  // Local state
  const [mapLoaded, setMapLoaded] = useState(false);
  const [showEmployees, setShowEmployees] = useState(true);
  const [showGeofences, setShowGeofences] = useState(true);
  const [mapFilters, setMapFilters] = useState({
    status: 'all',
    alertStatus: 'all'
  });

  // Initialize map
  useEffect(() => {
    if (!mapRef.current || mapInstance.current) return;

    // Initialize Mapbox (you'll need to add Mapbox GL JS to your dependencies)
    if (window.mapboxgl) {
      mapInstance.current = new window.mapboxgl.Map({
        container: mapRef.current,
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [-74.006, 40.7128], // Default to NYC, you might want to center on your tenant's location
        zoom: 10
      });

      mapInstance.current.on('load', () => {
        setMapLoaded(true);
      });
    }
  }, []);

  // Update map when geofences change
  useEffect(() => {
    if (!mapLoaded || !mapInstance.current) return;

    // Clear existing markers and circles
    Object.values(markersRef.current).forEach(marker => marker.remove());
    Object.values(circlesRef.current).forEach(circle => circle.remove());
    markersRef.current = {};
    circlesRef.current = {};

    // Filter geofences based on map filters
    const filteredGeofences = (geofences || []).filter(geofence => {
      if (mapFilters.status !== 'all' && geofence.status !== mapFilters.status) {
        return false;
      }
      if (mapFilters.alertStatus !== 'all') {
        const hasAlerts = geofence.activeAlerts > 0;
        if (mapFilters.alertStatus === 'with_alerts' && !hasAlerts) return false;
        if (mapFilters.alertStatus === 'without_alerts' && hasAlerts) return false;
      }
      return true;
    });

    // Add geofence circles
    filteredGeofences.forEach(geofence => {
      if (!showGeofences) return;

      const location = locations.find(loc => loc.id === geofence.locationId);
      if (!location || !location.coordinates) return;

      const { lat, lng } = location.coordinates;

      // Create circle for geofence boundary
      const circle = new window.mapboxgl.Circle({
        center: [lng, lat],
        radius: geofence.radius,
        fillColor: getGeofenceColor(geofence),
        fillOpacity: 0.2,
        strokeColor: getGeofenceColor(geofence),
        strokeWidth: 2,
        strokeOpacity: 0.8
      });

      circle.addTo(mapInstance.current);
      circlesRef.current[geofence.id] = circle;

      // Add click handler to circle
      circle.on('click', () => {
        onGeofenceSelect(geofence);
      });

      // Add geofence marker
      const markerElement = createGeofenceMarker(geofence);
      const marker = new window.mapboxgl.Marker(markerElement)
        .setLngLat([lng, lat])
        .addTo(mapInstance.current);

      markersRef.current[geofence.id] = marker;

      // Add click handler to marker
      marker.getElement().addEventListener('click', () => {
        onGeofenceSelect(geofence);
      });
    });

    // Add employee markers if enabled
    if (showEmployees) {
      employees.forEach(employee => {
        if (!employee.currentLocation) return;

        const { lat, lng } = employee.currentLocation;
        const markerElement = createEmployeeMarker(employee);
        const marker = new window.mapboxgl.Marker(markerElement)
          .setLngLat([lng, lat])
          .addTo(mapInstance.current);

        markersRef.current[`employee-${employee.id}`] = marker;
      });
    }

    // Fit map to show all geofences
    if (filteredGeofences.length > 0) {
      const bounds = new window.mapboxgl.LngLatBounds();
      filteredGeofences.forEach(geofence => {
        const location = locations.find(loc => loc.id === geofence.locationId);
        if (location && location.coordinates) {
          bounds.extend([location.coordinates.lng, location.coordinates.lat]);
        }
      });
      mapInstance.current.fitBounds(bounds, { padding: 50 });
    }
  }, [geofences, mapLoaded, showEmployees, showGeofences, mapFilters, locations, employees]);

  // Highlight selected geofence
  useEffect(() => {
    if (!mapLoaded || !mapInstance.current) return;

    // Remove previous highlights
    Object.values(circlesRef.current).forEach(circle => {
      circle.setStrokeColor(getGeofenceColor(circlesRef.current[circle.id]));
      circle.setStrokeWidth(2);
    });

    // Highlight selected geofence
    if (selectedGeofence && circlesRef.current[selectedGeofence.id]) {
      const circle = circlesRef.current[selectedGeofence.id];
      circle.setStrokeColor('#3b82f6');
      circle.setStrokeWidth(4);
    }
  }, [selectedGeofence, mapLoaded]);

  const getGeofenceColor = (geofence) => {
    if (geofence.activeAlerts > 0) return '#ef4444'; // Red for alerts
    if (geofence.status === 'active') return '#10b981'; // Green for active
    return '#6b7280'; // Gray for inactive
  };

  const createGeofenceMarker = (geofence) => {
    const marker = document.createElement('div');
    marker.className = 'geofence-marker';
    marker.innerHTML = `
      <div class="relative">
        <div class="w-8 h-8 bg-white border-2 border-gray-300 rounded-full flex items-center justify-center shadow-lg">
          <MapPin className="w-4 h-4 text-gray-600" />
        </div>
        ${geofence.activeAlerts > 0 ? `
          <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <span class="text-xs text-white font-bold">${geofence.activeAlerts}</span>
          </div>
        ` : ''}
      </div>
    `;
    return marker;
  };

  const createEmployeeMarker = (employee) => {
    const marker = document.createElement('div');
    marker.className = 'employee-marker';
    marker.innerHTML = `
      <div class="relative">
        <div class="w-6 h-6 bg-blue-500 border-2 border-white rounded-full flex items-center justify-center shadow-lg">
          <Users className="w-3 h-3 text-white" />
        </div>
        <div class="absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity">
          ${employee.name}
        </div>
      </div>
    `;
    return marker;
  };

  const handleZoomIn = () => {
    if (mapInstance.current) {
      mapInstance.current.zoomIn();
    }
  };

  const handleZoomOut = () => {
    if (mapInstance.current) {
      mapInstance.current.zoomOut();
    }
  };

  const handleFitBounds = () => {
    if (!mapInstance.current || geofences.length === 0) return;

    const bounds = new window.mapboxgl.LngLatBounds();
    geofences.forEach(geofence => {
      const location = locations.find(loc => loc.id === geofence.locationId);
      if (location && location.coordinates) {
        bounds.extend([location.coordinates.lng, location.coordinates.lat]);
      }
    });
    mapInstance.current.fitBounds(bounds, { padding: 50 });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600">Loading map...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Map Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            Geofence Map
          </h3>
          <div className="flex items-center space-x-2">
            {/* Map Controls */}
            <div className="flex items-center space-x-1 bg-gray-100 p-1 rounded-lg">
              <button
                onClick={handleZoomIn}
                className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded"
                title="Zoom In"
              >
                <ZoomIn className="h-4 w-4" />
              </button>
              <button
                onClick={handleZoomOut}
                className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded"
                title="Zoom Out"
              >
                <ZoomOut className="h-4 w-4" />
              </button>
              <button
                onClick={handleFitBounds}
                className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded"
                title="Fit to Geofences"
              >
                <Layers className="h-4 w-4" />
              </button>
            </div>

            {/* Layer Toggles */}
            <div className="flex items-center space-x-1 bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => setShowGeofences(!showGeofences)}
                className={`p-1 rounded text-xs font-medium transition-colors ${
                  showGeofences
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Toggle Geofences"
              >
                <Shield className="h-4 w-4" />
              </button>
              <button
                onClick={() => setShowEmployees(!showEmployees)}
                className={`p-1 rounded text-xs font-medium transition-colors ${
                  showEmployees
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Toggle Employees"
              >
                <Users className="h-4 w-4" />
              </button>
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-2">
              <select
                value={mapFilters.status}
                onChange={(e) => setMapFilters({ ...mapFilters, status: e.target.value })}
                className="px-2 py-1 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
              <select
                value={mapFilters.alertStatus}
                onChange={(e) => setMapFilters({ ...mapFilters, alertStatus: e.target.value })}
                className="px-2 py-1 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Alerts</option>
                <option value="with_alerts">With Alerts</option>
                <option value="without_alerts">Without Alerts</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="relative">
        <div
          ref={mapRef}
          className="w-full h-96"
          style={{ minHeight: '400px' }}
        />

        {/* Map Legend */}
        <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 border border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Legend</h4>
          <div className="space-y-2 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>Active Geofence</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span>Geofence with Alerts</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
              <span>Inactive Geofence</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span>Employee Location</span>
            </div>
          </div>
        </div>

        {/* Selected Geofence Info */}
        {selectedGeofence && (
          <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-4 border border-gray-200 max-w-sm">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-900">
                {selectedGeofence.name}
              </h4>
              <button
                onClick={() => onGeofenceSelect(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <div className="space-y-1 text-xs text-gray-600">
              <p><strong>Location:</strong> {locations.find(loc => loc.id === selectedGeofence.locationId)?.name}</p>
              <p><strong>Radius:</strong> {selectedGeofence.radius}m</p>
              <p><strong>Status:</strong> {selectedGeofence.status}</p>
              <p><strong>Active Alerts:</strong> {selectedGeofence.activeAlerts || 0}</p>
              <p><strong>Employees:</strong> {selectedGeofence.allowedEmployees.length + selectedGeofence.restrictedEmployees.length}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GeofenceMap; 