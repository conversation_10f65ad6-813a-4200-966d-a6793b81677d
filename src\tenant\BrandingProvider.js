import React, { createContext, useContext, useEffect, useCallback } from 'react';
import { useTenant } from './TenantProvider';

const BrandingContext = createContext();

export const useBranding = () => {
  const context = useContext(BrandingContext);
  if (!context) {
    // Return a default branding object if context is not available
    return {
      branding: { primaryColor: '#3b82f6' },
      isLoading: false,
      applyBranding: () => {},
      resetBranding: () => {}
    };
  }
  return context;
};

export const BrandingProvider = ({ children }) => {
  const { tenant, isLoading } = useTenant();

  const removeCustomCSS = useCallback(() => {
    const existingStyle = document.getElementById('tenant-custom-css');
    if (existingStyle) {
      existingStyle.remove();
    }
  }, []);

  const applyCustomCSS = useCallback((customCSS) => {
    // Remove existing custom CSS
    removeCustomCSS();

    // Add new custom CSS
    const style = document.createElement('style');
    style.id = 'tenant-custom-css';
    style.textContent = customCSS;
    document.head.appendChild(style);
  }, [removeCustomCSS]);

  const applyBranding = useCallback((branding) => {
    const root = document.documentElement;
    
    // Apply primary color
    if (branding.primaryColor) {
      root.style.setProperty('--primary-50', adjustColor(branding.primaryColor, 0.95));
      root.style.setProperty('--primary-100', adjustColor(branding.primaryColor, 0.9));
      root.style.setProperty('--primary-200', adjustColor(branding.primaryColor, 0.8));
      root.style.setProperty('--primary-300', adjustColor(branding.primaryColor, 0.7));
      root.style.setProperty('--primary-400', adjustColor(branding.primaryColor, 0.6));
      root.style.setProperty('--primary-500', branding.primaryColor);
      root.style.setProperty('--primary-600', adjustColor(branding.primaryColor, -0.1));
      root.style.setProperty('--primary-700', adjustColor(branding.primaryColor, -0.2));
      root.style.setProperty('--primary-800', adjustColor(branding.primaryColor, -0.3));
      root.style.setProperty('--primary-900', adjustColor(branding.primaryColor, -0.4));
    }

    // Apply login background
    if (branding.loginBackground) {
      root.style.setProperty('--login-background', branding.loginBackground);
    }

    // Apply custom CSS if provided
    if (branding.customCSS) {
      applyCustomCSS(branding.customCSS);
    }
  }, [applyCustomCSS]);

  const resetBranding = useCallback(() => {
    const root = document.documentElement;
    
    // Reset to default colors
    root.style.removeProperty('--primary-50');
    root.style.removeProperty('--primary-100');
    root.style.removeProperty('--primary-200');
    root.style.removeProperty('--primary-300');
    root.style.removeProperty('--primary-400');
    root.style.removeProperty('--primary-500');
    root.style.removeProperty('--primary-600');
    root.style.removeProperty('--primary-700');
    root.style.removeProperty('--primary-800');
    root.style.removeProperty('--primary-900');
    root.style.removeProperty('--login-background');
    
    // Remove custom CSS
    removeCustomCSS();
  }, [removeCustomCSS]);

  const adjustColor = (color, amount) => {
    // Simple color adjustment - in production, use a proper color library
    const hex = color.replace('#', '');
    const r = Math.max(0, Math.min(255, parseInt(hex.substr(0, 2), 16) + (amount * 255)));
    const g = Math.max(0, Math.min(255, parseInt(hex.substr(2, 2), 16) + (amount * 255)));
    const b = Math.max(0, Math.min(255, parseInt(hex.substr(4, 2), 16) + (amount * 255)));
    return `#${Math.round(r).toString(16).padStart(2, '0')}${Math.round(g).toString(16).padStart(2, '0')}${Math.round(b).toString(16).padStart(2, '0')}`;
  };



  useEffect(() => {
    if (tenant?.branding) {
      applyBranding(tenant.branding);
    } else {
      // Reset to default branding
      resetBranding();
    }
  }, [tenant?.branding, applyBranding, resetBranding]);

  return (
    <BrandingContext.Provider value={{
      branding: tenant?.branding || null,
      isLoading,
      applyBranding,
      resetBranding,
    }}>
      {children}
    </BrandingContext.Provider>
  );
}; 