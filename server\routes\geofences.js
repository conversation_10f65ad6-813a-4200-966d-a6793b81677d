const express = require('express');
const { authenticateToken, requireTenantAccess } = require('../middleware/auth');
const pool = require('../config/database');
const router = express.Router({ mergeParams: true });

// Get all geofences for an organization
router.get('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { status = 'all', location = 'all' } = req.query;

    let query = `
      SELECT
        g.id,
        g.name,
        g.description,
        g.type,
        g.center_latitude,
        g.center_longitude,
        g.radius,
        g.polygon_coordinates,
        g.route_coordinates,
        g.status,
        g.alert_on_entry,
        g.alert_on_exit,
        g.created_at,
        g.updated_at,
        l.id as location_id,
        l.name as location_name,
        l.address as location_address
      FROM geofences g
      LEFT JOIN locations l ON g.location_id = l.id
      WHERE g.organization_id = $1
    `;

    const params = [organizationId];
    let paramIndex = 2;

    if (status !== 'all') {
      query += ` AND g.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (location !== 'all') {
      query += ` AND g.location_id = $${paramIndex}`;
      params.push(location);
      paramIndex++;
    }

    query += ` ORDER BY g.created_at DESC`;

    const result = await pool.query(query, params);
    const geofences = result.rows.map(geofence => ({
      id: geofence.id,
      name: geofence.name,
      description: geofence.description,
      type: geofence.type,
      center: {
        lat: parseFloat(geofence.center_latitude),
        lng: parseFloat(geofence.center_longitude)
      },
      radius: parseFloat(geofence.radius),
      polygonCoordinates: geofence.polygon_coordinates,
      routeCoordinates: geofence.route_coordinates,
      status: geofence.status,
      alertOnEntry: geofence.alert_on_entry,
      alertOnExit: geofence.alert_on_exit,
      location: geofence.location_id ? {
        id: geofence.location_id,
        name: geofence.location_name,
        address: geofence.location_address
      } : null,
      createdAt: geofence.created_at,
      updatedAt: geofence.updated_at
    }));

    res.json(geofences);
  } catch (error) {
    console.error('Error fetching geofences:', error);
    res.status(500).json({ error: 'Failed to fetch geofences' });
  }
});

// Create a new geofence
router.post('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const {
      name,
      description = '',
      type = 'circular',
      locationId,
      center,
      radius,
      polygonCoordinates,
      routeCoordinates,
      status = 'active',
      alertOnEntry = true,
      alertOnExit = true
    } = req.body;

    // Validate required fields
    if (!name || !center || !center.lat || !center.lng) {
      return res.status(400).json({ error: 'Name and center coordinates are required' });
    }

    if (type === 'circular' && !radius) {
      return res.status(400).json({ error: 'Radius is required for circular geofences' });
    }

    const result = await pool.query(`
      INSERT INTO geofences (
        organization_id, location_id, name, description, type,
        center_latitude, center_longitude, radius, polygon_coordinates,
        route_coordinates, status, alert_on_entry, alert_on_exit
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *
    `, [
      organizationId, locationId, name, description, type,
      center.lat, center.lng, radius, polygonCoordinates,
      routeCoordinates, status, alertOnEntry, alertOnExit
    ]);

    const geofence = result.rows[0];

    // Get location details if locationId is provided
    let location = null;
    if (locationId) {
      const locationResult = await pool.query(
        'SELECT id, name, address FROM locations WHERE id = $1 AND organization_id = $2',
        [locationId, organizationId]
      );
      if (locationResult.rows.length > 0) {
        location = locationResult.rows[0];
      }
    }

    res.status(201).json({
      id: geofence.id,
      name: geofence.name,
      description: geofence.description,
      type: geofence.type,
      center: {
        lat: parseFloat(geofence.center_latitude),
        lng: parseFloat(geofence.center_longitude)
      },
      radius: parseFloat(geofence.radius),
      polygonCoordinates: geofence.polygon_coordinates,
      routeCoordinates: geofence.route_coordinates,
      status: geofence.status,
      alertOnEntry: geofence.alert_on_entry,
      alertOnExit: geofence.alert_on_exit,
      location,
      createdAt: geofence.created_at,
      updatedAt: geofence.updated_at
    });
  } catch (error) {
    console.error('Error creating geofence:', error);
    res.status(500).json({ error: 'Failed to create geofence' });
  }
});

// Get geofence by ID
router.get('/:geofenceId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, geofenceId } = req.params;

    const result = await pool.query(`
      SELECT
        g.id,
        g.name,
        g.description,
        g.type,
        g.center_latitude,
        g.center_longitude,
        g.radius,
        g.polygon_coordinates,
        g.route_coordinates,
        g.status,
        g.alert_on_entry,
        g.alert_on_exit,
        g.created_at,
        g.updated_at,
        l.id as location_id,
        l.name as location_name,
        l.address as location_address
      FROM geofences g
      LEFT JOIN locations l ON g.location_id = l.id
      WHERE g.organization_id = $1 AND g.id = $2
    `, [organizationId, geofenceId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Geofence not found' });
    }

    const geofence = result.rows[0];
    res.json({
      id: geofence.id,
      name: geofence.name,
      description: geofence.description,
      type: geofence.type,
      center: {
        lat: parseFloat(geofence.center_latitude),
        lng: parseFloat(geofence.center_longitude)
      },
      radius: parseFloat(geofence.radius),
      polygonCoordinates: geofence.polygon_coordinates,
      routeCoordinates: geofence.route_coordinates,
      status: geofence.status,
      alertOnEntry: geofence.alert_on_entry,
      alertOnExit: geofence.alert_on_exit,
      location: geofence.location_id ? {
        id: geofence.location_id,
        name: geofence.location_name,
        address: geofence.location_address
      } : null,
      createdAt: geofence.created_at,
      updatedAt: geofence.updated_at
    });
  } catch (error) {
    console.error('Error fetching geofence:', error);
    res.status(500).json({ error: 'Failed to fetch geofence' });
  }
});

// Update geofence
router.put('/:geofenceId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, geofenceId } = req.params;
    const {
      name,
      description,
      type,
      locationId,
      center,
      radius,
      polygonCoordinates,
      routeCoordinates,
      status,
      alertOnEntry,
      alertOnExit
    } = req.body;

    const result = await pool.query(`
      UPDATE geofences SET
        name = COALESCE($3, name),
        description = COALESCE($4, description),
        type = COALESCE($5, type),
        location_id = COALESCE($6, location_id),
        center_latitude = COALESCE($7, center_latitude),
        center_longitude = COALESCE($8, center_longitude),
        radius = COALESCE($9, radius),
        polygon_coordinates = COALESCE($10, polygon_coordinates),
        route_coordinates = COALESCE($11, route_coordinates),
        status = COALESCE($12, status),
        alert_on_entry = COALESCE($13, alert_on_entry),
        alert_on_exit = COALESCE($14, alert_on_exit),
        updated_at = CURRENT_TIMESTAMP
      WHERE organization_id = $1 AND id = $2
      RETURNING *
    `, [
      organizationId, geofenceId, name, description, type, locationId,
      center?.lat, center?.lng, radius, polygonCoordinates, routeCoordinates,
      status, alertOnEntry, alertOnExit
    ]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Geofence not found' });
    }

    const geofence = result.rows[0];

    // Get location details if locationId is provided
    let location = null;
    if (geofence.location_id) {
      const locationResult = await pool.query(
        'SELECT id, name, address FROM locations WHERE id = $1 AND organization_id = $2',
        [geofence.location_id, organizationId]
      );
      if (locationResult.rows.length > 0) {
        location = locationResult.rows[0];
      }
    }

    res.json({
      id: geofence.id,
      name: geofence.name,
      description: geofence.description,
      type: geofence.type,
      center: {
        lat: parseFloat(geofence.center_latitude),
        lng: parseFloat(geofence.center_longitude)
      },
      radius: parseFloat(geofence.radius),
      polygonCoordinates: geofence.polygon_coordinates,
      routeCoordinates: geofence.route_coordinates,
      status: geofence.status,
      alertOnEntry: geofence.alert_on_entry,
      alertOnExit: geofence.alert_on_exit,
      location,
      createdAt: geofence.created_at,
      updatedAt: geofence.updated_at
    });
  } catch (error) {
    console.error('Error updating geofence:', error);
    res.status(500).json({ error: 'Failed to update geofence' });
  }
});

// Delete geofence
router.delete('/:geofenceId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, geofenceId } = req.params;

    const result = await pool.query(
      'DELETE FROM geofences WHERE organization_id = $1 AND id = $2 RETURNING id',
      [organizationId, geofenceId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Geofence not found' });
    }

    res.json({ message: 'Geofence deleted successfully' });
  } catch (error) {
    console.error('Error deleting geofence:', error);
    res.status(500).json({ error: 'Failed to delete geofence' });
  }
});

module.exports = router;