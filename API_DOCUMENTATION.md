# API Documentation

This document provides a comprehensive reference for the FootOnStreet API endpoints.

## Base URL

```
http://localhost:3001/api (Development)
https://your-domain.com/api (Production)
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "data": {...},
  "message": "Success message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

For errors:
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Rate Limiting

- **Limit**: 100 requests per 15-minute window per IP
- **Headers**: Rate limit info is included in response headers
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time

## Health Check

### GET /health

Check the API server health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "database": "connected"
}
```

---

## Authentication Endpoints

### POST /api/auth/login

Authenticate a user and receive a JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "organizationId": "org-uuid" // Optional for super admin
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "jwt-token-here",
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "admin",
      "organizationId": "org-uuid"
    }
  }
}
```

### POST /api/auth/logout

Logout the current user (invalidate token).

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### POST /api/auth/verify

Verify if the provided token is valid.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "role": "admin",
      "organizationId": "org-uuid"
    }
  }
}
```

---

## Organization Management

### GET /api/organizations

Get all organizations (Super Admin only).

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "org-uuid",
      "name": "Organization Name",
      "domain": "org-domain",
      "settings": {
        "branding": {
          "logo": "logo-url",
          "primaryColor": "#007bff",
          "secondaryColor": "#6c757d"
        }
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### POST /api/organizations

Create a new organization (Super Admin only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "name": "New Organization",
  "domain": "new-org",
  "settings": {
    "branding": {
      "logo": "logo-url",
      "primaryColor": "#007bff",
      "secondaryColor": "#6c757d"
    }
  }
}
```

### GET /api/organizations/:id

Get organization details.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "org-uuid",
    "name": "Organization Name",
    "domain": "org-domain",
    "settings": {...},
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /api/organizations/:id

Update organization details.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "name": "Updated Organization Name",
  "settings": {
    "branding": {
      "logo": "new-logo-url",
      "primaryColor": "#ff0000"
    }
  }
}
```

---

## User Management

### GET /api/organizations/:organizationId/users

Get all users in an organization.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `role` (optional): Filter by role
- `status` (optional): Filter by status

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user-uuid",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "role": "employee",
        "status": "active",
        "phoneNumber": "+1234567890",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

### POST /api/organizations/:organizationId/users

Create a new user.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "phoneNumber": "+1234567890",
  "role": "employee",
  "password": "temporaryPassword123"
}
```

### GET /api/organizations/:organizationId/users/:userId

Get user details.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "employee",
    "status": "active",
    "phoneNumber": "+1234567890",
    "profilePicture": "profile-url",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /api/organizations/:organizationId/users/:userId

Update user details.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "firstName": "Updated John",
  "lastName": "Updated Doe",
  "phoneNumber": "+0987654321",
  "role": "supervisor"
}
```

### DELETE /api/organizations/:organizationId/users/:userId

Delete a user.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

---

## Location Management

### GET /api/organizations/:organizationId/locations

Get all locations for an organization.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "location-uuid",
      "name": "Main Office",
      "address": "123 Main St, City, State",
      "coordinates": {
        "latitude": 40.7128,
        "longitude": -74.0060
      },
      "contactDetails": {
        "phone": "+1234567890",
        "email": "<EMAIL>"
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### POST /api/organizations/:organizationId/locations

Create a new location.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "name": "New Location",
  "address": "456 Oak Ave, City, State",
  "coordinates": {
    "latitude": 40.7580,
    "longitude": -73.9855
  },
  "contactDetails": {
    "phone": "+1234567890",
    "email": "<EMAIL>"
  }
}
```

### GET /api/organizations/:organizationId/locations/:locationId

Get location details.

**Headers:** `Authorization: Bearer <token>`

### PUT /api/organizations/:organizationId/locations/:locationId

Update location details.

**Headers:** `Authorization: Bearer <token>`

### DELETE /api/organizations/:organizationId/locations/:locationId

Delete a location.

**Headers:** `Authorization: Bearer <token>`

---

## Assignment Management

### GET /api/organizations/:organizationId/assignments

Get all assignments for an organization.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `userId` (optional): Filter by user
- `locationId` (optional): Filter by location
- `status` (optional): Filter by status
- `date` (optional): Filter by date

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "assignment-uuid",
      "userId": "user-uuid",
      "locationId": "location-uuid",
      "startTime": "2024-01-01T09:00:00.000Z",
      "endTime": "2024-01-01T17:00:00.000Z",
      "status": "active",
      "notes": "Security shift",
      "user": {
        "id": "user-uuid",
        "firstName": "John",
        "lastName": "Doe"
      },
      "location": {
        "id": "location-uuid",
        "name": "Main Office"
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### POST /api/organizations/:organizationId/assignments

Create a new assignment.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "userId": "user-uuid",
  "locationId": "location-uuid",
  "startTime": "2024-01-01T09:00:00.000Z",
  "endTime": "2024-01-01T17:00:00.000Z",
  "notes": "Security shift"
}
```

---

## Incident Management

### GET /api/organizations/:organizationId/incidents

Get all incidents for an organization.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `status` (optional): Filter by status
- `severity` (optional): Filter by severity
- `userId` (optional): Filter by user
- `locationId` (optional): Filter by location

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "incident-uuid",
      "title": "Security Breach",
      "description": "Unauthorized access detected",
      "severity": "high",
      "status": "open",
      "reportedBy": "user-uuid",
      "locationId": "location-uuid",
      "coordinates": {
        "latitude": 40.7128,
        "longitude": -74.0060
      },
      "media": ["image-url-1", "image-url-2"],
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### POST /api/organizations/:organizationId/incidents

Create a new incident.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "title": "New Incident",
  "description": "Incident description",
  "severity": "medium",
  "locationId": "location-uuid",
  "coordinates": {
    "latitude": 40.7128,
    "longitude": -74.0060
  },
  "media": ["image-url-1"]
}
```

### GET /api/organizations/:organizationId/incidents/:incidentId

Get incident details.

**Headers:** `Authorization: Bearer <token>`

### PUT /api/organizations/:organizationId/incidents/:incidentId

Update incident details.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "status": "resolved",
  "notes": "Issue resolved by security team"
}
```

---

## Geofence Management

### GET /api/organizations/:organizationId/geofences

Get all geofences for an organization.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "geofence-uuid",
      "name": "Safe Zone",
      "type": "circle",
      "coordinates": {
        "latitude": 40.7128,
        "longitude": -74.0060
      },
      "radius": 100,
      "alertType": "both",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### POST /api/organizations/:organizationId/geofences

Create a new geofence.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "name": "New Geofence",
  "type": "circle",
  "coordinates": {
    "latitude": 40.7128,
    "longitude": -74.0060
  },
  "radius": 150,
  "alertType": "exit"
}
```

---

## Reporting & Analytics

### GET /api/organizations/:organizationId/reports/attendance

Get attendance reports.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `startDate`: Start date (YYYY-MM-DD)
- `endDate`: End date (YYYY-MM-DD)
- `userId` (optional): Filter by user
- `locationId` (optional): Filter by location

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalHours": 320,
      "averageHours": 8,
      "lateArrivals": 5,
      "earlyDepartures": 2
    },
    "records": [
      {
        "date": "2024-01-01",
        "userId": "user-uuid",
        "userName": "John Doe",
        "checkIn": "2024-01-01T09:00:00.000Z",
        "checkOut": "2024-01-01T17:00:00.000Z",
        "hoursWorked": 8,
        "status": "present"
      }
    ]
  }
}
```

### GET /api/organizations/:organizationId/reports/incidents

Get incident reports.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `startDate`: Start date (YYYY-MM-DD)
- `endDate`: End date (YYYY-MM-DD)
- `severity` (optional): Filter by severity

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalIncidents": 25,
      "openIncidents": 5,
      "resolvedIncidents": 20,
      "averageResolutionTime": 4.5
    },
    "incidents": [...]
  }
}
```

---

## Messaging

### GET /api/organizations/:organizationId/messages

Get messages for an organization.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `userId` (optional): Filter by user
- `limit` (optional): Number of messages to return

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "message-uuid",
      "senderId": "admin-uuid",
      "recipientId": "user-uuid",
      "content": "Please report to the main office",
      "timestamp": "2024-01-01T10:00:00.000Z",
      "isRead": false
    }
  ]
}
```

### POST /api/organizations/:organizationId/messages

Send a new message.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "recipientId": "user-uuid",
  "content": "Message content here"
}
```

---

## Error Codes

| Code | Description |
|------|-------------|
| `AUTH_REQUIRED` | Authentication token required |
| `INVALID_TOKEN` | Invalid or expired token |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions |
| `VALIDATION_ERROR` | Request validation failed |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `DUPLICATE_RESOURCE` | Resource already exists |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `SERVER_ERROR` | Internal server error |
| `DATABASE_ERROR` | Database operation failed |

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `429` - Too Many Requests
- `500` - Internal Server Error

## WebSocket Events

The application uses Socket.IO for real-time communication:

### Client Events
- `join-organization` - Join organization room
- `location-update` - Send location update
- `message-sent` - Send message

### Server Events
- `location-updated` - Broadcast location update
- `message-received` - Broadcast new message
- `incident-created` - Broadcast new incident
- `geofence-alert` - Broadcast geofence breach