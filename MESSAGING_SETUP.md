# Secure In-App Messaging & Notification System Setup Guide

This guide covers the implementation of secure in-app messaging between admin users and employees, plus comprehensive alert banners and notifications for various events.

## Overview

The messaging and notification system provides:

- **Secure Real-time Messaging**: End-to-end encrypted messaging between admins and employees
- **Alert Banners**: Real-time alerts for critical events
- **Notification Center**: Centralized notification management
- **Multi-tenant Support**: Isolated messaging per organization
- **File Sharing**: Secure file attachments in messages
- **Typing Indicators**: Real-time typing status
- **Read Receipts**: Message delivery and read status
- **Event Notifications**: Alerts for incidents, panic buttons, geofence breaches

## Features

### Messaging System
- Real-time chat between admins and employees
- File attachments (images, documents, etc.)
- Typing indicators
- Read receipts and delivery status
- Message search and history
- Conversation management
- Emoji support

### Notification System
- Real-time notifications for various events
- Alert banners for critical events
- Notification center with filtering
- Sound and desktop notifications
- Notification preferences
- Mark as read functionality

### Alert Types
- **Incidents**: New incident reports and updates
- **Panic Buttons**: Employee emergency alerts
- **Geofence Breaches**: Location boundary violations
- **Status Changes**: Employee status updates
- **Messages**: New message notifications
- **Emergency Alerts**: Critical system alerts

## API Endpoints

### Base URL
```
/api/v1/messaging
```

### Messaging Endpoints

#### GET /messaging/conversations
Fetch user conversations
```javascript
// Query Parameters
{
  tenantId: string (required)
}

// Response
{
  conversations: [
    {
      id: string,
      participant: {
        id: string,
        name: string,
        avatar: string,
        online: boolean,
        lastSeen: string
      },
      lastMessage: {
        id: string,
        content: string,
        senderId: string,
        timestamp: string
      },
      unreadCount: number,
      updatedAt: string
    }
  ]
}
```

#### GET /messaging/conversations/:id/messages
Fetch conversation messages
```javascript
// Query Parameters
{
  tenantId: string (required),
  limit?: number,
  before?: string
}

// Response
{
  messages: [
    {
      id: string,
      content: string,
      senderId: string,
      timestamp: string,
      deliveredAt: string,
      readAt: string,
      attachments: [
        {
          id: string,
          name: string,
          url: string,
          size: number,
          type: string
        }
      ]
    }
  ]
}
```

#### POST /messaging/conversations
Create new conversation
```javascript
// Request Body
{
  tenantId: string (required),
  participantId: string (required),
  initialMessage?: string
}
```

#### POST /messaging/conversations/:id/messages
Send message
```javascript
// Request Body (multipart/form-data)
{
  content: string (required),
  senderId: string (required),
  tenantId: string (required),
  attachments?: File[]
}
```

#### PUT /messaging/conversations/:id/read
Mark conversation as read
```javascript
// Query Parameters
{
  tenantId: string (required)
}
```

#### PUT /messaging/conversations/:id/messages/:messageId/read
Mark message as read
```javascript
// Query Parameters
{
  tenantId: string (required)
}
```

### Notification Endpoints

#### GET /notifications
Fetch user notifications
```javascript
// Query Parameters
{
  tenantId: string (required),
  type?: string,
  read?: boolean,
  limit?: number
}

// Response
{
  notifications: [
    {
      id: string,
      type: string,
      title: string,
      message: string,
      read: boolean,
      priority: string,
      timestamp: string,
      data: object
    }
  ]
}
```

#### PUT /notifications/:id/read
Mark notification as read
```javascript
// Query Parameters
{
  tenantId: string (required)
}
```

#### DELETE /notifications/:id
Delete notification
```javascript
// Query Parameters
{
  tenantId: string (required)
}
```

## WebSocket Events

### Client to Server Events

#### send_message
```javascript
{
  conversationId: string,
  content: string,
  attachments: File[],
  tenantId: string
}
```

#### typing
```javascript
{
  conversationId: string,
  isTyping: boolean,
  tenantId: string
}
```

#### join_conversation
```javascript
{
  conversationId: string,
  tenantId: string
}
```

#### leave_conversation
```javascript
{
  conversationId: string,
  tenantId: string
}
```

#### mark_message_read
```javascript
{
  conversationId: string,
  messageId: string,
  tenantId: string
}
```

#### mark_conversation_read
```javascript
{
  conversationId: string,
  tenantId: string
}
```

#### create_conversation
```javascript
{
  participantId: string,
  initialMessage: string,
  tenantId: string
}
```

#### panic_button_activated
```javascript
{
  location: string,
  coordinates: {
    latitude: number,
    longitude: number
  },
  tenantId: string
}
```

#### geofence_breach
```javascript
{
  geofenceId: string,
  location: string,
  coordinates: {
    latitude: number,
    longitude: number
  },
  tenantId: string
}
```

### Server to Client Events

#### new_message
```javascript
{
  conversationId: string,
  message: MessageObject,
  senderName: string,
  tenantId: string
}
```

#### message_delivered
```javascript
{
  conversationId: string,
  messageId: string,
  timestamp: string
}
```

#### message_read
```javascript
{
  conversationId: string,
  messageId: string,
  timestamp: string
}
```

#### user_typing
```javascript
{
  conversationId: string,
  userId: string,
  userName: string
}
```

#### user_stopped_typing
```javascript
{
  conversationId: string,
  userId: string
}
```

#### user_online_status
```javascript
{
  userId: string,
  online: boolean,
  lastSeen: string
}
```

#### new_conversation
```javascript
{
  conversation: ConversationObject
}
```

#### panic_button_activated
```javascript
{
  employeeId: string,
  employeeName: string,
  location: string,
  coordinates: {
    latitude: number,
    longitude: number
  },
  tenantId: string
}
```

#### geofence_breach
```javascript
{
  employeeId: string,
  employeeName: string,
  geofenceId: string,
  geofenceName: string,
  location: string,
  coordinates: {
    latitude: number,
    longitude: number
  },
  tenantId: string
}
```

## Database Schema

### Conversations Table
```sql
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  participant1_id UUID NOT NULL REFERENCES employees(id),
  participant2_id UUID NOT NULL REFERENCES employees(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, participant1_id, participant2_id)
);

CREATE INDEX idx_conversations_tenant_id ON conversations(tenant_id);
CREATE INDEX idx_conversations_participants ON conversations(participant1_id, participant2_id);
```

### Messages Table
```sql
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES employees(id),
  content TEXT NOT NULL,
  message_type VARCHAR(20) DEFAULT 'text',
  delivered_at TIMESTAMP WITH TIME ZONE,
  read_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
```

### Message Attachments Table
```sql
CREATE TABLE message_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  file_url TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type VARCHAR(100) NOT NULL,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_message_attachments_message_id ON message_attachments(message_id);
```

### Notifications Table
```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  user_id UUID NOT NULL REFERENCES employees(id),
  type VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT,
  priority VARCHAR(20) DEFAULT 'medium',
  read BOOLEAN DEFAULT FALSE,
  data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_notifications_tenant_id ON notifications(tenant_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_read ON notifications(read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
```

### User Online Status Table
```sql
CREATE TABLE user_online_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES employees(id),
  online BOOLEAN DEFAULT FALSE,
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

CREATE INDEX idx_user_online_status_user_id ON user_online_status(user_id);
CREATE INDEX idx_user_online_status_online ON user_online_status(online);
```

## Environment Variables

Add these environment variables to your `.env` file:

```bash
# Messaging Configuration
REACT_APP_MESSAGING_ENABLED=true
REACT_APP_MAX_MESSAGE_LENGTH=1000
REACT_APP_MAX_ATTACHMENT_SIZE=10485760  # 10MB
REACT_APP_ALLOWED_ATTACHMENT_TYPES=image/*,.pdf,.doc,.docx,.txt

# Notification Configuration
REACT_APP_NOTIFICATIONS_ENABLED=true
REACT_APP_NOTIFICATION_SOUND_ENABLED=true
REACT_APP_DESKTOP_NOTIFICATIONS_ENABLED=true
REACT_APP_NOTIFICATION_RETENTION_DAYS=30

# WebSocket Configuration
REACT_APP_WEBSOCKET_URL=ws://localhost:3001
REACT_APP_WEBSOCKET_RECONNECTION_ATTEMPTS=5
REACT_APP_WEBSOCKET_RECONNECTION_DELAY=1000

# Security Configuration
REACT_APP_MESSAGE_ENCRYPTION_ENABLED=true
REACT_APP_FILE_UPLOAD_ENCRYPTION=true
```

## Security Features

### Message Encryption
- End-to-end encryption for all messages
- File encryption for attachments
- Secure key exchange
- Message integrity verification

### Access Control
- Tenant isolation for all messaging data
- Role-based access control
- User authentication required
- Message ownership validation

### File Security
- Virus scanning for uploaded files
- File type validation
- Size limits enforcement
- Secure file storage

### Privacy Features
- Message deletion capabilities
- Conversation archiving
- Data retention policies
- GDPR compliance

## Mobile App Integration

### React Native Example
```javascript
import { sendMessage, createConversation } from '../services/messagingService';

const sendMessageToEmployee = async (employeeId, message, attachments = []) => {
  try {
    // Create conversation if it doesn't exist
    const conversation = await createConversation(employeeId);
    
    // Send message
    const response = await sendMessage(conversation.id, message, attachments);
    return response;
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};

const activatePanicButton = async (location, coordinates) => {
  try {
    const response = await fetch('/api/v1/emergency/panic', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        location,
        coordinates,
        tenantId: currentTenant.id
      })
    });
    return response.json();
  } catch (error) {
    console.error('Error activating panic button:', error);
    throw error;
  }
};
```

### Flutter Example
```dart
class MessagingService {
  final Dio _dio = Dio();
  
  Future<Map<String, dynamic>> sendMessage(String conversationId, String content, List<File> attachments) async {
    try {
      FormData formData = FormData.fromMap({
        'content': content,
        'conversationId': conversationId,
        'tenantId': currentTenant.id,
      });
      
      // Add attachments
      for (var attachment in attachments) {
        formData.files.add(MapEntry(
          'attachments',
          await MultipartFile.fromFile(attachment.path, filename: attachment.path.split('/').last),
        ));
      }
      
      Response response = await _dio.post('/messaging/conversations/$conversationId/messages', data: formData);
      return response.data;
    } catch (e) {
      print('Error sending message: $e');
      rethrow;
    }
  }
}
```

## Configuration

### Notification Settings
Configure notification preferences:

```javascript
const NOTIFICATION_SETTINGS = {
  enableSound: true,
  enableDesktopNotifications: true,
  showEmergencyAlerts: true,
  showZoneViolations: true,
  showStatusChanges: false,
  showIncidents: true,
  showPanicAlerts: true,
  showGeofenceBreaches: true,
  showMessages: true
};
```

### Message Types
```javascript
const MESSAGE_TYPES = {
  text: 'text',
  image: 'image',
  file: 'file',
  location: 'location',
  emergency: 'emergency'
};
```

### Notification Types
```javascript
const NOTIFICATION_TYPES = {
  incident: 'incident',
  panic_button: 'panic_button',
  geofence_breach: 'geofence_breach',
  status_change: 'status_change',
  message: 'message',
  emergency: 'emergency',
  late_alert: 'late_alert'
};
```

## Testing

### Unit Tests
```javascript
describe('Messaging System', () => {
  test('should send message', async () => {
    const messageData = {
      conversationId: 'conv-123',
      content: 'Test message',
      senderId: 'user-123'
    };
    
    const response = await sendMessage(messageData);
    expect(response.status).toBe(201);
    expect(response.data.content).toBe(messageData.content);
  });
});
```

### Integration Tests
```javascript
describe('WebSocket Messaging', () => {
  test('should receive real-time message', (done) => {
    const socket = io('ws://localhost:3001');
    
    socket.on('new_message', (data) => {
      expect(data.message.content).toBe('Test message');
      done();
    });
    
    // Send message via API
    sendMessage(testMessageData);
  });
});
```

## Performance Optimization

### Database Optimization
- Index optimization for message queries
- Message pagination
- Conversation caching
- Read status optimization

### Frontend Optimization
- Message virtualization for large conversations
- Image lazy loading
- File upload progress indicators
- Offline message queuing

### WebSocket Optimization
- Connection pooling
- Message batching
- Heartbeat monitoring
- Automatic reconnection

## Monitoring & Analytics

### Key Metrics
- Message delivery rates
- Response times
- User engagement
- Notification effectiveness

### Alerts
- WebSocket connection failures
- Message delivery failures
- High notification volume
- System performance issues

## Troubleshooting

### Common Issues

1. **Messages not sending**
   - Check WebSocket connection
   - Verify authentication
   - Check file size limits

2. **Notifications not appearing**
   - Check notification settings
   - Verify browser permissions
   - Check WebSocket events

3. **File upload failures**
   - Check file type restrictions
   - Verify size limits
   - Check storage permissions

4. **Real-time updates not working**
   - Check WebSocket connection
   - Verify event handlers
   - Check Redux store

### Debug Mode
Enable debug logging:

```javascript
localStorage.setItem('debug', 'messaging:*');
```

## Support

For technical support or questions about the messaging and notification system:

- **Documentation**: Check this guide and API documentation
- **Issues**: Report bugs through the project issue tracker
- **Feature Requests**: Submit feature requests through the project repository
- **Email**: <EMAIL>

---

This setup guide provides comprehensive information for implementing and configuring the secure in-app messaging and notification system. Follow the steps carefully to ensure proper integration with your OnTheMove admin dashboard. 