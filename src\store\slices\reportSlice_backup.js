import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { reportAPI } from '../../services/api';

// Async thunks
export const fetchAttendanceData = createAsyncThunk(
  'reports/fetchAttendanceData',
  async ({ tenantId, filters }, { rejectWithValue }) => {
    try {
      const response = await reportAPI.getAttendanceReport(tenantId, filters);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch attendance data');
    }
  }
);

export const fetchIncidentData = createAsyncThunk(
  'reports/fetchIncidentData',
  async ({ tenantId, filters }, { rejectWithValue }) => {
    try {
      const response = await reportAPI.getSafetyReport(tenantId, filters);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch incident data');
    }
  }
);

export const fetchPerformanceData = createAsyncThunk(
  'reports/fetchPerformanceData',
  async ({ tenantId, filters }, { rejectWithValue }) => {
    try {
      const response = await reportAPI.getPerformanceReport(tenantId, filters);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch performance data');
    }
  }
);

export const fetchComplianceData = createAsyncThunk(
  'reports/fetchComplianceData',
  async ({ tenantId, filters }, { rejectWithValue }) => {
    try {
      const response = await reportAPI.getSafetyReport(tenantId, { ...filters, type: 'compliance' });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch compliance data');
    }
  }
);

export const generateReport = createAsyncThunk(
  'reports/generateReport',
  async ({ tenantId, reportType, filters, format }, { rejectWithValue }) => {
    try {
      const response = await reportAPI.getAttendanceReport(tenantId, { ...filters, format });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to generate report');
    }
  }
);

export const exportReport = createAsyncThunk(
  'reports/exportReport',
  async ({ tenantId, reportType, filters, format }, { rejectWithValue }) => {
    try {
      const response = await reportAPI.exportReport(tenantId, reportType, { ...filters, format });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${reportType}_${new Date().toISOString().split('T')[0]}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      return { success: true };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to export report');
    }
  }
);

// Initial state
const initialState = {
  // Report data
  attendance: null,
  incidents: null,
  performance: null,
  compliance: null,
  
  // Loading states
  loading: false,
  generating: false,
  exporting: false,
  
  // Error state
  error: null,
  
  // Generated reports
  generatedReports: [],
  
  // Filters
  currentFilters: {
    dateRange: 'month',
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    employees: [],
    locations: [],
    departments: [],
    incidentTypes: [],
    severityLevels: []
  }
};

// Slice
const reportSlice = createSlice({
  name: 'reports',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action) => {
      state.currentFilters = { ...state.currentFilters, ...action.payload };
    },
    clearReportData: (state) => {
      state.attendance = null;
      state.incidents = null;
      state.performance = null;
      state.compliance = null;
    },
    addGeneratedReport: (state, action) => {
      state.generatedReports.unshift(action.payload);
      // Keep only last 10 reports
      if (state.generatedReports.length > 10) {
        state.generatedReports = state.generatedReports.slice(0, 10);
      }
    }
  },
  extraReducers: (builder) => {
    // Fetch Attendance Data
    builder
      .addCase(fetchAttendanceData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAttendanceData.fulfilled, (state, action) => {
        state.loading = false;
        state.attendance = action.payload;
      })
      .addCase(fetchAttendanceData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Fetch Incident Data
    builder
      .addCase(fetchIncidentData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchIncidentData.fulfilled, (state, action) => {
        state.loading = false;
        state.incidents = action.payload;
      })
      .addCase(fetchIncidentData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Fetch Performance Data
    builder
      .addCase(fetchPerformanceData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPerformanceData.fulfilled, (state, action) => {
        state.loading = false;
        state.performance = action.payload;
      })
      .addCase(fetchPerformanceData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Fetch Compliance Data
    builder
      .addCase(fetchComplianceData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchComplianceData.fulfilled, (state, action) => {
        state.loading = false;
        state.compliance = action.payload;
      })
      .addCase(fetchComplianceData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Generate Report
    builder
      .addCase(generateReport.pending, (state) => {
        state.generating = true;
        state.error = null;
      })
      .addCase(generateReport.fulfilled, (state, action) => {
        state.generating = false;
        // Add to generated reports
        state.generatedReports.unshift({
          id: Date.now(),
          type: action.payload.reportType,
          generatedAt: new Date().toISOString(),
          downloadUrl: action.payload.downloadUrl,
          ...action.payload
        });
      })
      .addCase(generateReport.rejected, (state, action) => {
        state.generating = false;
        state.error = action.payload;
      });

    // Export Report
    builder
      .addCase(exportReport.pending, (state) => {
        state.exporting = true;
        state.error = null;
      })
      .addCase(exportReport.fulfilled, (state) => {
        state.exporting = false;
      })
      .addCase(exportReport.rejected, (state, action) => {
        state.exporting = false;
        state.error = action.payload;
      });
  }
});

// Actions
export const { clearError, setFilters, clearReportData, addGeneratedReport } = reportSlice.actions;

// Selectors
export const selectReportData = (state) => ({
  attendance: state.reports.attendance,
  incidents: state.reports.incidents,
  performance: state.reports.performance,
  compliance: state.reports.compliance
});

export const selectReportLoading = (state) => state.reports.loading;
export const selectReportGenerating = (state) => state.reports.generating;
export const selectReportExporting = (state) => state.reports.exporting;
export const selectReportError = (state) => state.reports.error;
export const selectCurrentFilters = (state) => state.reports.currentFilters;
export const selectGeneratedReports = (state) => state.reports.generatedReports;

// Helper selectors for specific data
export const selectAttendanceData = (state) => state.reports.attendance;
export const selectIncidentData = (state) => state.reports.incidents;
export const selectPerformanceData = (state) => state.reports.performance;
export const selectComplianceData = (state) => state.reports.compliance;

export default reportSlice.reducer; 