import React, { createContext, useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchOrganizationById } from '../store/slices/organizationSlice';

const TenantContext = createContext();

export const useTenant = () => {
  const context = useContext(TenantContext);
  if (!context) {
    // Return a tenant object with null ID if context is not available
    // This prevents API calls with invalid tenant IDs
    return {
      tenant: { name: 'OnTheMove Admin', id: null },
      isLoading: true,
      error: null,
      theme: 'default'
    };
  }
  return context;
};

export const TenantProvider = ({ children }) => {
  const dispatch = useDispatch();
  const authState = useSelector((state) => state.auth);
  const { user, tenant: authTenant, isAuthenticated } = authState;
  const { currentOrganization, isLoading, error } = useSelector((state) => state.organization);
  const [theme, setTheme] = useState('default');

  // Get organization ID from authenticated user or auth state
  // Priority: authTenant (from Redux) > user.organization.id > user direct properties
  const organizationId = authTenant ||
                        user?.organization?.id ||
                        user?.organizationId ||
                        user?.tenant ||
                        user?.tenantId;


  useEffect(() => {
    if (organizationId && user?.role !== 'super_admin') {
      dispatch(fetchOrganizationById(organizationId));
    }
  }, [organizationId, dispatch, user?.role]);

  // Apply custom theme if available
  useEffect(() => {
    if (currentOrganization?.branding?.theme) {
      setTheme(currentOrganization.branding.theme);
      // TODO: apply theme (e.g., set CSS variables)
    }
  }, [currentOrganization]);

  // Provide default organization for super admin or when no organization is available
  const effectiveOrganization = currentOrganization || {
    id: user?.role === 'super_admin' ? 'system' : organizationId,
    name: user?.role === 'super_admin' ? 'System Administration' : 'OnTheMove Admin',
    branding: { primaryColor: '#3b82f6' }
  };

  return (
    <TenantContext.Provider value={{
      tenant: effectiveOrganization, // keep key as tenant for compatibility
      isLoading,
      error,
      theme,
    }}>
      {children}
    </TenantContext.Provider>
  );
};