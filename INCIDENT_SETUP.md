# Incident Management Module Setup Guide

This guide covers the setup and configuration of the Incident Management module for the OnTheMove admin dashboard.

## Overview

The Incident Management module allows employees to submit incidents from their mobile apps and provides administrators with a comprehensive dashboard to track, manage, and resolve incidents. The module includes:

- **Incident Submission**: Mobile app integration for employee incident reporting
- **Dashboard Management**: Admin interface for incident tracking and management
- **Real-time Updates**: WebSocket integration for live incident updates
- **Filtering & Search**: Advanced filtering by status, severity, location, and more
- **Statistics & Analytics**: Comprehensive reporting and trend analysis
- **Multi-tenant Support**: Isolated incident data per organization

## Features

### For Employees (Mobile App)
- Submit incident reports with photos and descriptions
- Track incident status and updates
- Receive notifications about assigned incidents
- Emergency incident escalation

### For Administrators (Dashboard)
- View all incidents with filtering and search
- Assign incidents to employees
- Update incident status and priority
- Track resolution times and statistics
- Real-time incident notifications

## API Endpoints

### Base URL
```
/api/v1/incidents
```

### Endpoints

#### GET /incidents
Fetch incidents with filtering
```javascript
// Query Parameters
{
  tenantId: string (required),
  search?: string,
  status?: 'open' | 'in_progress' | 'resolved' | 'closed',
  severity?: 'low' | 'medium' | 'high' | 'critical',
  category?: string,
  priority?: 'low' | 'normal' | 'high' | 'urgent',
  location?: string,
  employee?: string,
  dateRange?: 'today' | 'week' | 'month' | 'quarter' | 'year'
}

// Response
{
  incidents: [
    {
      id: string,
      title: string,
      description: string,
      severity: string,
      status: string,
      category: string,
      priority: string,
      locationId: string,
      assignedEmployeeId: string,
      submittedBy: string,
      submittedAt: string,
      updatedAt: string,
      attachments: [
        {
          id: string,
          name: string,
          url: string,
          size: number,
          type: string
        }
      ]
    }
  ]
}
```

#### POST /incidents
Create new incident
```javascript
// Request Body
{
  title: string (required),
  description: string (required),
  severity: string (required),
  category: string,
  priority: string,
  locationId: string (required),
  assignedEmployeeId: string,
  attachments: File[],
  tenantId: string (required)
}
```

#### POST /incidents/mobile
Submit incident from mobile app
```javascript
// Request Body
{
  title: string (required),
  description: string (required),
  severity: string (required),
  category: string,
  locationId: string (required),
  attachments: File[],
  submittedBy: string (required),
  tenantId: string (required),
  coordinates: {
    latitude: number,
    longitude: number
  }
}
```

#### PUT /incidents/:id
Update incident
```javascript
// Request Body
{
  title?: string,
  description?: string,
  severity?: string,
  status?: string,
  category?: string,
  priority?: string,
  locationId?: string,
  assignedEmployeeId?: string,
  attachments?: File[],
  tenantId: string (required)
}
```

#### DELETE /incidents/:id
Delete incident
```javascript
// Query Parameters
{
  tenantId: string (required)
}
```

## WebSocket Events

### Client to Server Events

#### incident_submitted
```javascript
// Emitted when incident is submitted from mobile app
{
  incident: {
    id: string,
    title: string,
    description: string,
    severity: string,
    status: string,
    submittedBy: string,
    tenantId: string
  },
  employeeName: string
}
```

#### incident_updated
```javascript
// Emitted when incident is updated
{
  incident: {
    id: string,
    title: string,
    status: string,
    severity: string,
    assignedEmployeeId: string,
    tenantId: string
  },
  employeeName: string
}
```

#### incident_assigned
```javascript
// Emitted when incident is assigned to employee
{
  incident: {
    id: string,
    title: string,
    assignedEmployeeId: string,
    tenantId: string
  },
  employeeName: string
}
```

#### incident_escalated
```javascript
// Emitted when incident severity is escalated
{
  incident: {
    id: string,
    title: string,
    severity: string,
    assignedEmployeeId: string,
    tenantId: string
  },
  employeeName: string
}
```

### Server to Client Events

#### incident_submitted
```javascript
// Received when new incident is submitted
{
  incident: IncidentObject,
  employeeName: string
}
```

#### incident_updated
```javascript
// Received when incident is updated
{
  incident: IncidentObject,
  employeeName: string
}
```

#### incident_assigned
```javascript
// Received when incident is assigned
{
  incident: IncidentObject,
  employeeName: string
}
```

#### incident_escalated
```javascript
// Received when incident is escalated
{
  incident: IncidentObject,
  employeeName: string
}
```

## Database Schema

### Incidents Table
```sql
CREATE TABLE incidents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  status VARCHAR(20) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
  category VARCHAR(50) NOT NULL DEFAULT 'general',
  priority VARCHAR(20) NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  location_id UUID REFERENCES locations(id),
  assigned_employee_id UUID REFERENCES employees(id),
  submitted_by UUID NOT NULL REFERENCES employees(id),
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  coordinates POINT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_incidents_tenant_id ON incidents(tenant_id);
CREATE INDEX idx_incidents_status ON incidents(status);
CREATE INDEX idx_incidents_severity ON incidents(severity);
CREATE INDEX idx_incidents_submitted_at ON incidents(submitted_at);
CREATE INDEX idx_incidents_assigned_employee_id ON incidents(assigned_employee_id);
```

### Incident Attachments Table
```sql
CREATE TABLE incident_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  incident_id UUID NOT NULL REFERENCES incidents(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  file_url TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type VARCHAR(100) NOT NULL,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_incident_attachments_incident_id ON incident_attachments(incident_id);
```

## Environment Variables

Add these environment variables to your `.env` file:

```bash
# File Upload Configuration
REACT_APP_FILE_UPLOAD_URL=https://your-upload-service.com
REACT_APP_MAX_FILE_SIZE=10485760  # 10MB in bytes
REACT_APP_ALLOWED_FILE_TYPES=image/*,.pdf,.doc,.docx,.txt

# Incident Configuration
REACT_APP_INCIDENT_AUTO_ASSIGN=true
REACT_APP_INCIDENT_ESCALATION_THRESHOLD=3600  # 1 hour in seconds
REACT_APP_INCIDENT_NOTIFICATION_EMAIL=true

# WebSocket Configuration
REACT_APP_WEBSOCKET_URL=ws://localhost:3001
```

## Mobile App Integration

### React Native Example
```javascript
import { submitIncident } from '../services/incidentService';

const submitIncidentReport = async (incidentData) => {
  try {
    const formData = new FormData();
    
    // Add incident details
    formData.append('title', incidentData.title);
    formData.append('description', incidentData.description);
    formData.append('severity', incidentData.severity);
    formData.append('category', incidentData.category);
    formData.append('locationId', incidentData.locationId);
    formData.append('submittedBy', currentEmployee.id);
    formData.append('tenantId', currentTenant.id);
    
    // Add coordinates if available
    if (incidentData.coordinates) {
      formData.append('coordinates', JSON.stringify(incidentData.coordinates));
    }
    
    // Add attachments
    if (incidentData.attachments) {
      incidentData.attachments.forEach((attachment, index) => {
        formData.append(`attachments`, {
          uri: attachment.uri,
          type: attachment.type,
          name: attachment.name
        });
      });
    }
    
    const response = await submitIncident(formData);
    return response;
  } catch (error) {
    console.error('Error submitting incident:', error);
    throw error;
  }
};
```

### Flutter Example
```dart
import 'package:dio/dio.dart';

class IncidentService {
  final Dio _dio = Dio();
  
  Future<Map<String, dynamic>> submitIncident(Map<String, dynamic> incidentData) async {
    try {
      FormData formData = FormData.fromMap({
        'title': incidentData['title'],
        'description': incidentData['description'],
        'severity': incidentData['severity'],
        'category': incidentData['category'],
        'locationId': incidentData['locationId'],
        'submittedBy': currentEmployee.id,
        'tenantId': currentTenant.id,
      });
      
      // Add coordinates
      if (incidentData['coordinates'] != null) {
        formData.fields.add(MapEntry('coordinates', jsonEncode(incidentData['coordinates'])));
      }
      
      // Add attachments
      if (incidentData['attachments'] != null) {
        for (var attachment in incidentData['attachments']) {
          formData.files.add(MapEntry(
            'attachments',
            await MultipartFile.fromFile(attachment.path, filename: attachment.name),
          ));
        }
      }
      
      Response response = await _dio.post('/incidents/mobile', data: formData);
      return response.data;
    } catch (e) {
      print('Error submitting incident: $e');
      rethrow;
    }
  }
}
```

## Configuration

### Incident Categories
Configure available incident categories in your backend:

```javascript
const INCIDENT_CATEGORIES = [
  'general',
  'safety',
  'equipment',
  'maintenance',
  'security',
  'environmental',
  'quality',
  'other'
];
```

### Severity Levels
```javascript
const SEVERITY_LEVELS = [
  { value: 'low', label: 'Low', color: '#10b981' },
  { value: 'medium', label: 'Medium', color: '#f59e0b' },
  { value: 'high', label: 'High', color: '#f97316' },
  { value: 'critical', label: 'Critical', color: '#ef4444' }
];
```

### Priority Levels
```javascript
const PRIORITY_LEVELS = [
  { value: 'low', label: 'Low' },
  { value: 'normal', label: 'Normal' },
  { value: 'high', label: 'High' },
  { value: 'urgent', label: 'Urgent' }
];
```

## Notifications

### Email Notifications
Configure email notifications for incident events:

```javascript
// Email templates
const EMAIL_TEMPLATES = {
  incident_submitted: {
    subject: 'New Incident Reported - {title}',
    template: 'incident-submitted.html'
  },
  incident_assigned: {
    subject: 'Incident Assigned to You - {title}',
    template: 'incident-assigned.html'
  },
  incident_escalated: {
    subject: 'Incident Escalated - {title}',
    template: 'incident-escalated.html'
  }
};
```

### Push Notifications
Configure push notifications for mobile apps:

```javascript
// Push notification payload
const pushNotification = {
  title: 'New Incident',
  body: 'A new incident has been reported',
  data: {
    type: 'incident',
    incidentId: 'incident-id',
    action: 'view_incident'
  }
};
```

## Security Considerations

### Authentication & Authorization
- All incident endpoints require valid JWT token
- Tenant isolation ensures users can only access their organization's incidents
- Role-based access control for incident management

### File Upload Security
- Validate file types and sizes
- Scan uploaded files for malware
- Store files in secure cloud storage
- Generate secure URLs for file access

### Data Privacy
- Encrypt sensitive incident data
- Implement data retention policies
- Ensure GDPR compliance for personal data
- Audit trail for all incident modifications

## Testing

### Unit Tests
```javascript
describe('Incident Management', () => {
  test('should create incident', async () => {
    const incidentData = {
      title: 'Test Incident',
      description: 'Test description',
      severity: 'medium',
      locationId: 'location-id',
      tenantId: 'tenant-id'
    };
    
    const response = await createIncident(incidentData);
    expect(response.status).toBe(201);
    expect(response.data.title).toBe(incidentData.title);
  });
});
```

### Integration Tests
```javascript
describe('Incident API Integration', () => {
  test('should filter incidents by status', async () => {
    const response = await fetchIncidents({
      tenantId: 'tenant-id',
      status: 'open'
    });
    
    expect(response.data.incidents).toHaveLength(2);
    expect(response.data.incidents.every(inc => inc.status === 'open')).toBe(true);
  });
});
```

## Troubleshooting

### Common Issues

1. **Incidents not appearing in dashboard**
   - Check tenant isolation filters
   - Verify user permissions
   - Check WebSocket connection status

2. **File upload failures**
   - Verify file size limits
   - Check allowed file types
   - Ensure upload service is accessible

3. **WebSocket connection issues**
   - Check WebSocket server status
   - Verify authentication tokens
   - Check network connectivity

4. **Real-time updates not working**
   - Verify WebSocket event handlers
   - Check Redux store configuration
   - Ensure proper event dispatching

### Debug Mode
Enable debug logging by setting:

```javascript
localStorage.setItem('debug', 'incidents:*');
```

## Performance Optimization

### Database Optimization
- Add appropriate indexes for filtering
- Implement pagination for large datasets
- Use database views for complex queries

### Frontend Optimization
- Implement virtual scrolling for large lists
- Use React.memo for expensive components
- Optimize re-renders with useMemo and useCallback

### Caching Strategy
- Cache incident data with React Query
- Implement Redis caching for frequently accessed data
- Use CDN for file attachments

## Monitoring & Analytics

### Key Metrics
- Incident response time
- Resolution time by severity
- Incident frequency by location
- Employee workload distribution

### Alerts
- High severity incident alerts
- Unassigned incident notifications
- Escalation threshold alerts
- System performance monitoring

## Support

For technical support or questions about the Incident Management module:

- **Documentation**: Check this guide and API documentation
- **Issues**: Report bugs through the project issue tracker
- **Feature Requests**: Submit feature requests through the project repository
- **Email**: <EMAIL>

---

This setup guide provides comprehensive information for implementing and configuring the Incident Management module. Follow the steps carefully to ensure proper integration with your OnTheMove admin dashboard. 